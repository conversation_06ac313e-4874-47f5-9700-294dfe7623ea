"""
配置文件
"""

import os


class Config:
    """应用配置类"""
    
    # WebSocket服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    
    # ROS2机器人接口配置
    ROS2_ENABLED: bool = os.getenv("ROS2_ENABLED", "true").lower() == "true"
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/app.log")
    LOG_ROTATION: str = os.getenv("LOG_ROTATION", "1 day")
    LOG_RETENTION: str = os.getenv("LOG_RETENTION", "7 days")
    
    # WebSocket配置
    MAX_CONNECTIONS: int = int(os.getenv("MAX_CONNECTIONS", "100"))
    PING_INTERVAL: int = int(os.getenv("PING_INTERVAL", "20"))
    PING_TIMEOUT: int = int(os.getenv("PING_TIMEOUT", "10"))
    
    # 重试配置
    MAX_RETRIES: int = int(os.getenv("MAX_RETRIES", "3"))
    RETRY_DELAY: float = float(os.getenv("RETRY_DELAY", "1.0"))


# 创建配置实例
config = Config()
