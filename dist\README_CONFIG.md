# Live2D 模型配置说明

## 如何切换模型

1. 打开 `config.json` 文件
2. 修改 `currentModel` 字段为想要使用的模型名称
3. 刷新网页即可看到新模型

## 可用模型列表

- `greenlive2d` - 绿色Live2D模型
- `xm` - XM模型  
- `yuan721` - 721圆眼模型
- `pink` - 粉色模型
- `pink_eyes` - 粉色眼睛模型

## 配置示例

```json
{
  "currentModel": "yuan721",
  "models": {
    // 模型定义...
  },
  "settings": {
    "defaultMotionGroup": "center",
    "autoCache": false,
    "enableWebSocket": true,
    "wsUrl": "ws://localhost:8002"
  }
}
```

## 添加新模型

1. 将模型文件放入 `public` 目录下的相应文件夹
2. 在 `config.json` 的 `models` 对象中添加新模型配置
3. 修改 `currentModel` 为新模型的键名

## 注意事项

- 修改配置后需要刷新网页才能生效
- 确保模型文件路径正确
- 模型文件夹需要包含完整的 Live2D 资源文件