{"Version": 3, "Meta": {"Duration": 3.633, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 36, "TotalSegmentCount": 313, "TotalPointCount": 895, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.767, 0, 0.8, 1, 0.833, 1, 1, 0.844, 1, 0.856, -1, 0.867, -1, 1, 0.878, -1, 0.889, -1, 0.9, -1, 1, 0.989, -1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.189, 1, 2.244, 1, 2.3, 1, 1, 2.311, 1, 2.322, 0, 2.333, 0, 1, 2.422, 0, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.778, 0, 0.822, 1, 0.867, 1, 1, 0.878, 1, 0.889, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.2, 1, 2.271, 1, 2.333, 0.8, 1, 2.422, 0.513, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.778, 0, 0.822, 1, 0.867, 1, 1, 0.878, 1, 0.889, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.2, 1, 2.271, 1, 2.333, 0.8, 1, 2.422, 0.513, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.03, 0.333, 0.03, 1, 0.467, 0.03, 0.6, 0.03, 0.733, 0.03, 1, 0.789, 0.03, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.489, 0, 1.811, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, -0.03, 0.9, -0.03, 1, 0.989, -0.03, 1.078, -0.03, 1.167, -0.03, 1, 1.489, -0.03, 1.811, -0.03, 2.133, -0.03, 1, 2.289, -0.03, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.489, 0, 1.811, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.8, 0.333, 0.8, 1, 0.467, 0.8, 0.6, 0.8, 0.733, 0.8, 1, 0.789, 0.8, 0.844, 0.8, 0.9, 0.8, 1, 0.989, 0.8, 1.078, 0.8, 1.167, 0.8, 1, 1.489, 0.8, 1.811, 0.8, 2.133, 0.8, 1, 2.2, 0.8, 2.267, 0.678, 2.333, 0.45, 1, 2.422, 0.145, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, -1, 1, 0.111, -1, 0.222, -1, 0.333, -1, 1, 0.467, -1, 0.6, -1, 0.733, -1, 1, 0.789, -1, 0.844, -1, 0.9, -1, 1, 0.989, -1, 1.078, -1, 1.167, -1, 1, 1.489, -1, 1.811, -1, 2.133, -1, 1, 2.289, -1, 2.444, -1, 2.6, -1, 0, 3.633, -1]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.289, 1, 2.444, 1, 2.6, 1, 0, 3.633, 1]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.289, 1, 2.444, 1, 2.6, 1, 0, 3.633, 1]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.289, 1, 2.444, 1, 2.6, 1, 0, 3.633, 1]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.289, 1, 2.444, 1, 2.6, 1, 0, 3.633, 1]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.489, 0, 1.811, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.489, 0, 1.811, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.211, 0, 1.256, -0.15, 1.3, -0.15, 1, 1.356, -0.15, 1.411, 0, 1.467, 0, 1, 1.522, 0, 1.578, -0.15, 1.633, -0.15, 1, 1.689, -0.15, 1.744, 0, 1.8, 0, 1, 1.856, 0, 1.911, -0.15, 1.967, -0.15, 1, 2.022, -0.15, 2.078, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.55, 0.333, 0.55, 1, 0.467, 0.55, 0.6, 0.55, 0.733, 0.55, 1, 0.789, 0.55, 0.844, 0.55, 0.9, 0.55, 1, 0.989, 0.55, 1.078, 0.55, 1.167, 0.55, 1, 1.489, 0.55, 1.811, 0.55, 2.133, 0.55, 1, 2.289, 0.55, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.7, 0.333, 0.7, 1, 0.467, 0.7, 0.6, 0.7, 0.733, 0.7, 1, 0.789, 0.7, 0.844, 0.7, 0.9, 0.7, 1, 0.989, 0.7, 1.078, 0.7, 1.167, 0.7, 1, 1.489, 0.7, 1.811, 0.73, 2.133, 0.73, 1, 2.289, 0.73, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.211, 0, 1.256, -0.2, 1.3, -0.2, 1, 1.356, -0.2, 1.411, 0, 1.467, 0, 1, 1.522, 0, 1.578, -0.2, 1.633, -0.2, 1, 1.689, -0.2, 1.744, 0, 1.8, 0, 1, 1.856, 0, 1.911, -0.2, 1.967, -0.2, 1, 2.022, -0.2, 2.078, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.211, 0, 1.256, -0.1, 1.3, -0.1, 1, 1.356, -0.1, 1.411, 0, 1.467, 0, 1, 1.522, 0, 1.578, -0.1, 1.633, -0.1, 1, 1.689, -0.1, 1.744, 0, 1.8, 0, 1, 1.856, 0, 1.911, -0.1, 1.967, -0.1, 1, 2.022, -0.1, 2.078, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.111, 0, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.289, 1, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0, 0.9, 0, 1, 0.989, 0, 1.078, 0, 1.167, 0, 1, 1.489, 0, 1.811, 0, 2.133, 0, 1, 2.289, 0, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -1, 1, 0.111, -1, 0.222, -1, 0.333, -1, 1, 0.467, -1, 0.6, -1, 0.733, -1, 1, 0.767, -1, 0.8, -1, 0.833, -1, 1, 0.844, -1, 0.856, 1, 0.867, 1, 1, 0.878, 1, 0.889, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.189, 1, 2.244, 1, 2.3, 1, 1, 2.311, 1, 2.322, -1, 2.333, -1, 1, 2.422, -1, 2.511, -1, 2.6, -1, 0, 3.633, -1]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0.064, 0.9, 0.11, 1, 0.989, 0.184, 1.078, 0.2, 1.167, 0.2, 1, 1.211, 0.2, 1.256, -0.2, 1.3, -0.2, 1, 1.356, -0.2, 1.411, 0.2, 1.467, 0.2, 1, 1.522, 0.2, 1.578, -0.2, 1.633, -0.2, 1, 1.689, -0.2, 1.744, 0.2, 1.8, 0.2, 1, 1.856, 0.2, 1.911, -0.2, 1.967, -0.2, 1, 2.022, -0.2, 2.078, 0.2, 2.133, 0.2, 1, 2.289, 0.2, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 0.719, 0.9, 0.51, 1, 0.989, 0.175, 1.078, 0.1, 1.167, 0.1, 1, 1.489, 0.1, 1.811, 0.1, 2.133, 0.1, 1, 2.189, 0.1, 2.244, 0.694, 2.3, 0.8, 1, 2.4, 0.99, 2.5, 1, 2.6, 1, 0, 3.633, 1]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, 0.058, 0.9, 0.11, 1, 0.978, 0.183, 1.056, 0.2, 1.133, 0.2, 1, 1.144, 0.2, 1.156, 0.2, 1.167, 0.2, 1, 1.489, 0.2, 1.811, 0.2, 2.133, 0.2, 1, 2.289, 0.2, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.33, 0.333, 0.33, 1, 0.467, 0.33, 0.6, 0.33, 0.733, 0.33, 1, 0.744, 0.33, 0.756, 0.26, 0.767, 0.26, 1, 0.811, 0.26, 0.856, 0.33, 0.9, 0.33, 1, 0.911, 0.33, 0.922, 0.33, 0.933, 0.33, 1, 1.011, 0.33, 1.089, 0.33, 1.167, 0.33, 1, 1.489, 0.33, 1.811, 0.33, 2.133, 0.33, 1, 2.289, 0.33, 2.444, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.778, 0, 0.822, 1, 0.867, 1, 1, 0.878, 1, 0.889, -1, 0.9, -1, 1, 0.989, -1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.189, 1, 2.244, -1, 2.3, -1, 1, 2.311, -1, 2.322, 0, 2.333, 0, 1, 2.422, 0, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.2, 0.333, 0.2, 1, 0.467, 0.2, 0.6, 0.2, 0.733, 0.2, 1, 0.789, 0.2, 0.844, 0.2, 0.9, 0.2, 1, 0.989, 0.2, 1.078, 0.2, 1.167, 0.2, 1, 1.489, 0.2, 1.811, 0.2, 2.133, 0.2, 1, 2.2, 0.2, 2.267, 0.26, 2.333, 0.26, 1, 2.422, 0.26, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 0.033, 0, 0.067, -0.7, 0.1, -0.7, 1, 0.178, -0.7, 0.256, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.778, 0, 0.822, 0.7, 0.867, 0.7, 1, 0.878, 0.7, 0.889, 0.722, 0.9, 0.68, 1, 0.989, 0.347, 1.078, 0, 1.167, 0, 1, 1.489, 0, 1.811, 0, 2.133, 0, 1, 2.2, 0, 2.267, 0.8, 2.333, 0.8, 1, 2.422, 0.8, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.4, 0.333, 0.4, 1, 0.467, 0.4, 0.6, 0.4, 0.733, 0.4, 1, 0.789, 0.4, 0.844, 0.4, 0.9, 0.4, 1, 0.989, 0.4, 1.078, 0.4, 1.167, 0.4, 1, 1.489, 0.4, 1.811, 0.4, 2.133, 0.4, 1, 2.2, 0.4, 2.267, 0.371, 2.333, 0.26, 1, 2.422, 0.112, 2.511, 0, 2.6, 0, 0, 3.633, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -0.08, 0, 0.333, -0.08, 1, 0.467, -0.08, 0.6, -0.08, 0.733, -0.08, 1, 0.778, -0.08, 0.822, 0.08, 0.867, 0.08, 1, 0.878, 0.08, 0.889, 0.08, 0.9, 0.08, 1, 0.989, 0.08, 1.078, 0.1, 1.167, 0.1, 1, 1.211, 0.1, 1.256, 0, 1.3, 0, 1, 1.356, 0, 1.411, 0.1, 1.467, 0.1, 1, 1.522, 0.1, 1.578, 0, 1.633, 0, 1, 1.689, 0, 1.744, 0.1, 1.8, 0.1, 1, 1.856, 0.1, 1.911, 0, 1.967, 0, 1, 2.022, 0, 2.078, 0.1, 2.133, 0.1, 0, 3.633, 0.1]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0.28, 0, 0.9, 0.28, 1, 0.989, 0.28, 1.078, 0.28, 1.167, 0.28, 1, 1.178, 0.28, 1.189, 0.28, 1.2, 0.28, 1, 1.5, 0.28, 1.8, 0.28, 2.1, 0.28, 1, 2.111, 0.28, 2.122, 0.28, 2.133, 0.28, 1, 2.178, 0.28, 2.222, 0.15, 2.267, 0.15, 1, 2.278, 0.15, 2.289, 0.15, 2.3, 0.15, 0, 3.633, 0.15]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0.8, 0, 0.867, 0.8, 1, 0.878, 0.8, 0.889, 0.813, 0.9, 0.79, 1, 0.989, 0.607, 1.078, 0.317, 1.167, 0, 1, 1.211, -0.158, 1.256, -0.4, 1.3, -0.4, 1, 1.356, -0.4, 1.411, 0, 1.467, 0, 1, 1.522, 0, 1.578, -0.4, 1.633, -0.4, 1, 1.689, -0.4, 1.744, 0, 1.8, 0, 1, 1.856, 0, 1.911, -0.4, 1.967, -0.4, 1, 2.022, -0.4, 2.078, -0.355, 2.133, 0, 1, 2.189, 0.355, 2.244, 0.8, 2.3, 0.8, 0, 3.633, 0.8]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0.8, 0, 0.867, 0.8, 1, 0.878, 0.8, 0.889, 0.813, 0.9, 0.79, 1, 0.989, 0.607, 1.078, 0.317, 1.167, 0, 1, 1.211, -0.158, 1.256, -0.4, 1.3, -0.4, 1, 1.356, -0.4, 1.411, 0, 1.467, 0, 1, 1.522, 0, 1.578, -0.4, 1.633, -0.4, 1, 1.689, -0.4, 1.744, 0, 1.8, 0, 1, 1.856, 0, 1.911, -0.4, 1.967, -0.4, 1, 2.022, -0.4, 2.078, -0.355, 2.133, 0, 1, 2.189, 0.355, 2.244, 0.8, 2.3, 0.8, 0, 3.633, 0.8]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -1, 1, 0.111, -1, 0.222, -1, 0.333, -1, 1, 0.467, -1, 0.6, -1, 0.733, -1, 1, 0.767, -1, 0.8, -1, 0.833, -1, 1, 0.844, -1, 0.856, 1, 0.867, 1, 1, 0.878, 1, 0.889, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.489, 1, 1.811, 1, 2.133, 1, 1, 2.189, 1, 2.244, 1, 2.3, 1, 1, 2.311, 1, 2.322, -1, 2.333, -1, 1, 2.422, -1, 2.511, -1, 2.6, -1, 0, 3.633, -1]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 1, 0.3, 0, 0.6, 0.093, 0.9, 0.18, 1, 0.989, 0.206, 1.078, 0.2, 1.167, 0.2, 1, 1.489, 0.2, 1.811, 0.2, 2.133, 0.2, 1, 2.289, 0.2, 2.444, 0, 2.6, 0, 0, 3.633, 0]}]}