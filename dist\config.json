{"currentModel": "729_HY_new", "models": {"greenlive2d": {"name": "绿色Live2D", "path": "./lvse/green.model3.json", "description": "绿色主题模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "xm": {"name": "XM模型", "path": "./xm/XM.model3.json", "description": "XM主题模型", "defaultMotionGroup": "center", "backgroundColor": "#ffffff"}, "yuan721": {"name": "721圆眼", "path": "./721/721_YUANyan.model3.json", "description": "721圆眼模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "pink_eyes": {"name": "粉色眼睛", "path": "./pink_eyes/Pink_eyes_mod.model3.json", "description": "粉色眼睛模型", "defaultMotionGroup": "sleep", "backgroundColor": "#000000"}, "yanjiuceshi": {"name": "721圆眼", "path": "./yanjiuceshi/666A.model3.json", "description": "721圆眼模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "XMN": {"name": "XM模型", "path": "./XMN/XM.model3.json", "description": "XM主题模型", "defaultMotionGroup": "center", "backgroundColor": "#ffffff"}, "729_TEST": {"name": "729_TEST眼睛", "path": "./729_TEST/20250723biaoqing.model3.json", "description": "粉色眼睛模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "lvse_new": {"name": "lvse_new眼睛", "path": "./lvse_new/GY729.model3.json", "description": "粉色眼睛模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "729_HY_new": {"name": "729_HY_new眼睛", "path": "./729_HY_new/RedEyes.model3.json", "description": "粉色眼睛模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}}, "settings": {"defaultMotionGroup": "center", "defaultBackgroundColor": "#000000", "autoCache": false, "enableWebSocket": true, "wsUrl": "ws://localhost:8002"}}