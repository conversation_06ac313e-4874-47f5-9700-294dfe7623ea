{"version": 3, "file": "js/app.68318076.js", "mappings": "qFAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmBC,MAAO,CAAEC,gBAAiBN,EAAIO,yBAA2B,CAACL,EAAG,SAAS,CAACM,IAAI,gBACvL,EACIC,EAAkB,G,sCCGtBC,OAAAC,KAAAA,EAEA,OACAC,IAAAA,GACA,OACAC,IAAA,KACAC,MAAA,KACAC,aAAA,cACAC,iBAAA,GACAC,WAAA,GACAC,OAAA,KACAX,uBAAA,UAEAY,OAAA,KACAC,SAAA,EACAC,SAAA,eACAC,MAAA,sBAEAC,iBAAA,KAEAC,YAAA,EAEAC,wBAAA,EAEAC,mBAAA,SAEAC,gBAAA,SAEAC,sBAAA,KAEAC,sBAAA,KAEAC,eAAA,KAEAC,iBAAA,EAEA,EAEA,aAAAC,SAEA,KAAAC,aAEA,KAAAC,cACA,KAAAC,UAAA,KAAApB,cAAAqB,KAAA,KAEA,QAAAtB,OAAA,KAAAA,MAAAuB,cAAA,CACAC,QAAAC,IAAA,YAEA,MAAAC,EAAA,qDAEAA,EAAAC,QAAAC,IACA,KAAA5B,MAAA6B,GAAAD,EAAA,KACAJ,QAAAC,IAAA,SAAAG,KACA,KAAAE,yBAGA,KAAA9B,MAAAuB,cAAAQ,eACA,KAAA/B,MAAAuB,cAAAQ,cAAAF,GAAAD,EAAA,KACAJ,QAAAC,IAAA,WAAAG,KACA,KAAAE,0BAIA,IAIAlC,OAAAoC,iBAAA,cAAAC,cAGArC,OAAAoC,iBAAA,eAAAE,eAGA,KAAAC,kBACA,EAEAC,QAAA,CACAhB,WAAAA,GACA,KAAArB,IAAA,IAAAF,EAAAA,YAAA,CACAwC,KAAA,KAAAC,MAAAC,WACAC,WAAA,EACAC,SAAA7C,OACA8C,gBAAA,GAEA,EAEA,eAAArB,CAAAsB,GAEA,KAAA3C,QACA,KAAAD,IAAA6C,MAAAC,YAAA,KAAA7C,OACA,KAAAA,MAAA8C,UACA,KAAA9C,MAAA,KAGA,KAAA+C,yBAGA,IAEA,MAAAC,EAAA,KAAA7C,WAAAwC,IAAA,KAAAxC,WAAAwC,GAAAM,SAAA,mBAAAC,KAAAC,QAGA,KAAAnD,YAAAoD,EAAAA,GAAAC,KAAAL,GAGA,KAAAjD,IAAA6C,MAAAU,SAAA,KAAAtD,OAGA,KAAAuD,mBAGA,KAAAvD,MAAAwD,WAAA,EAGA,KAAAtD,iBAAA,KAAAF,MAAAuB,cAAAkC,SAAAC,QAGA,KAAAxD,iBAAA,KAAAU,sBACA,KAAA+C,iBAAA,KAAA/C,oBAGA,KAAAgD,0BAGApC,QAAAC,IAAA,MAAAkB,SACA,OAAAkB,GACArC,QAAAqC,MAAA,UAAAA,EACA,CACA,EAGAN,gBAAAA,GACA,SAAAvD,MAAA,OAGA,MAAA8D,EAAA,KAAA/D,IAAAgE,SAAAC,MACAC,EAAA,KAAAlE,IAAAgE,SAAAG,OAGAC,EAAA,KAAAnE,MAAAgE,MACAI,EAAA,KAAApE,MAAAkE,OAIAG,EAAAP,EAAAK,EACAG,EAAAL,EAAAG,EAGAG,EAAA,GAAAC,KAAAC,IAAAJ,EAAAC,GAGA,KAAAtE,MAAAuE,MAAAG,IAAAH,GAGA,KAAAvE,MAAA2E,EAAAb,EAAA,EACA,KAAA9D,MAAA4E,EAAAX,EAAA,EAGA,KAAAjE,MAAA6E,OAAAH,IAAA,OAEAlD,QAAAC,IAAA,cAAA8C,UAAA,KAAAvE,MAAA2E,MAAA,KAAA3E,MAAA4E,KACA,EAGA3C,YAAAA,GACA,KAAAjC,OACA,KAAAuD,kBAEA,EAGAuB,uBAAAA,GACA,QAAA9E,OAAA,KAAAA,MAAAuB,cAEA,IACA,MAAAwD,EAAA,KAAA/E,MAAAuB,cAAAwD,UACAhD,EAAA,KAAA/B,MAAAuB,cAAAQ,cAYA,GATAA,IACAA,EAAAiD,iBAEAjD,EAAAkD,qBACAlD,EAAAkD,oBAAAD,kBAKAD,EAAA,CACA,MAAAG,EAAAH,EAAAI,oBAGA,QAAAC,EAAA,EAAAA,EAAAF,EAAAE,IAAA,CACA,MAAAC,EAAAN,EAAAO,yBAAAF,GACAL,EAAAQ,yBAAAH,EAAAC,EACA,CAGA,MAAAG,EAAAT,EAAAU,eACA,QAAAL,EAAA,EAAAA,EAAAI,EAAAJ,IACAL,EAAAW,sBAAAN,EAAA,EAEA,CAGA,KAAApF,MAAAuB,cAAAoE,SAEAnE,QAAAC,IAAA,yBACA,OAAAoC,GACArC,QAAAoE,KAAA,UAAA/B,GAGA,IACA,KAAA7D,MAAAuB,cAAAQ,eACA,KAAA/B,MAAAuB,cAAAQ,cAAAiD,iBAEAxD,QAAAC,IAAA,UACA,OAAAoE,GACArE,QAAAoE,KAAA,WAAAC,EACA,CACA,CACA,EAGAC,UAAAA,CAAAC,EAAAC,GACA,KAAAhG,OAAA,KAAAE,iBAAA6F,KAEAvE,QAAAC,IAAA,WAAAsE,KAAAC,MAGA,KAAAlB,0BAGA,KAAA7D,iBAAA,EAGAgF,WAAA,KACA,IAEA,KAAAhF,iBAAA,EAGA8E,IAAA,KAAAnF,qBACA,KAAAI,eAAAkC,KAAAC,OAIA,KAAAnD,MAAAkG,KAAA,sBACA1E,QAAAC,IAAA,MAAAsE,KAAAC,SACA,KAAA/E,iBAAA,EACA,KAAAa,yBAIA,SAAAiE,EACA,KAAA/F,MAAAmG,OAAA,QAAAH,EAAA,SACA,UAAAD,EACA,KAAA/F,MAAAmG,OAAA,OAAAH,EAAA,SAEA,KAAAhG,MAAAmG,OAAAJ,EAAAC,EAAA,SAGAxE,QAAAC,IAAA,WAAAsE,KAAAC,MAGA,KAAAI,6BAEA,OAAAvC,GACArC,QAAAqC,MAAA,UAAAA,GACA,KAAA5C,iBAAA,CACA,GACA,IACA,EAGA0C,gBAAAA,CAAAoC,GACA,SAAA/F,QAAA,KAAAE,iBAAA6F,GAAA,OAEA,MAAArC,EAAA,KAAAxD,iBAAA6F,GACAC,EAAAxB,KAAA6B,MAAA7B,KAAA8B,SAAA5C,EAAA6C,QAEA/E,QAAAC,IAAA,aAAAsE,KAAAC,MAGA,KAAAlB,0BAGA,KAAA7D,iBAAA,EAGAgF,WAAA,KACA,IAEAF,IAAA,KAAAnF,oBAAAmF,IAAA,KAAAlF,iBAEA,KAAAI,iBAAA,EAGA,KAAAD,eAAAkC,KAAAC,MAGA,KAAAnD,MAAAmG,OAAAJ,EAAAC,EAAA,SAGA,KAAAhG,MAAAkG,KAAA,sBACA1E,QAAAC,IAAA,MAAAsE,KAAAC,iBACA,KAAA/E,iBAAA,EAEA,KAAA6D,0BACAmB,WAAA,KACA,KAAA/F,iBAAA,KAAAU,qBACA,KAAAZ,MAAAmG,OAAA,KAAAvF,mBAAA,aAEA,OAIA,KAAAZ,MAAAmG,OAAAJ,EAAAC,EAAA,SAGAxE,QAAAC,IAAA,aAAAsE,KAAAC,KAEA,OAAAnC,GACArC,QAAAqC,MAAA,YAAAA,GACA,KAAA5C,iBAAA,CACA,GACA,GACA,EAGAmF,2BAAAA,GAEA,KAAAtF,uBACA0F,aAAA,KAAA1F,uBAIA,KAAAA,sBAAAmF,WAAA,KAEA,QAAAjG,OACA,KAAAA,MAAAuB,eACA,KAAAvB,MAAAuB,cAAAQ,cAAA0E,aAAA,CACAjF,QAAAC,IAAA,kBAGA,MAAAiF,EAAA,KAAA1G,MAAAuB,cAAAQ,cAAAgE,MACAY,EAAA,KAAA3G,MAAAuB,cAAAQ,cAAAiE,MAGA,KAAAvF,mBACA,KAAAP,iBAAA,KAAAU,qBACA8F,IAAA,KAAA9F,oBAAA,IAAA+F,IACAnF,QAAAC,IAAA,WAEA,KAAAzB,MAAAmG,OAAA,KAAAvF,mBAAA,YAGA,KAAAI,eAAAkC,KAAAC,MAEA,MAEA,KAAAiD,+BAEA,IACA,EAGAxC,sBAAAA,GAEA,KAAAb,wBAGA,MAAA6D,EAAA,KAGAC,EAAA,IAGA,KAAA7F,iBACA,KAAAA,eAAAkC,KAAAC,OAIA,KAAApC,sBAAA+F,YAAA,KACA,MAAAC,EAAA7D,KAAAC,MAGA,QAAAnD,OACA,KAAAA,MAAAuB,eACA,KAAAvB,MAAAuB,cAAAQ,eACA,KAAA/B,MAAAuB,cAAAQ,cAAA0E,aAAA,CAGA,MAAAC,EAAA,KAAA1G,MAAAuB,cAAAQ,cAAAgE,MAGAiB,EAAAD,EAAA,KAAA/F,eAGAgG,GAAAH,EAEAH,GAAAA,IAAA,KAAA9F,qBAQA,KAAAZ,MAAAmG,OAAA,KAAAvF,mBAAA,WAIA,KAAAI,eAAA+F,GAIAL,GAAAA,IAAA,KAAA9F,qBACAY,QAAAC,IAAA,YACA,KAAAzB,MAAAmG,OAAA,KAAAvF,mBAAA,WAEA,GACAgG,EAGA,EAGA7D,qBAAAA,GACA,KAAAhC,wBACAkG,cAAA,KAAAlG,uBACA,KAAAA,sBAAA,KACAS,QAAAC,IAAA,YAEA,EAGA,iBAAAyF,CAAAvE,GACA,QAAA1C,eAAA0C,EAAA,OAEA,KAAA1C,aAAA0C,EAGA,MAAAwE,EAAA,KAAA/G,OAAAgH,OAAAzE,GACA,KAAA/B,mBAAAuG,GAAAvG,oBAAA,KAAAR,OAAAqD,SAAA7C,mBACA,KAAAnB,uBAAA0H,GAAA3H,iBAAA,KAAAY,OAAAqD,SAAA4D,uBAEA7F,QAAAC,IAAA,UAAAkB,aAAA,KAAA/B,4BAAA,KAAAnB,gCAEA,KAAA4B,UAAAsB,EACA,EAGA,wBAAA2E,CAAAC,GACA,QAAAvH,MAAA,CAEA,UAAAmG,KAAAoB,QACA,IAAAC,QAAAC,IACA,KAAAzH,MAAAkG,KAAA,iBAAAuB,GACA,kBAAAtB,EACA,KAAAxC,iBAAAwC,GAEA,KAAAL,WAAAK,EAAAJ,MAAAI,EAAAH,UAMA,KAAAvF,kBAAA,KAAAP,iBAAA,KAAAU,qBACA,KAAAZ,MAAAmG,OAAA,KAAAvF,mBAAA,OAfA,CAiBA,EAGA8G,aAAAA,GAEA,KAAAC,eAGA,MAAAC,EAAAC,OAAAC,KAAA,KAAA5H,kBACA,GAAA0H,EAAArB,OAAA,GACA,MAAAwB,EAAAH,EAAApD,KAAA6B,MAAA7B,KAAA8B,SAAAsB,EAAArB,SACA,KAAA5C,iBAAAoE,EACA,CAGA,mBAAAxH,WACA,KAAAE,iBAAAqG,YAAA,KAEA,MAAAc,EAAAC,OAAAC,KAAA,KAAA5H,kBACA,GAAA0H,EAAArB,OAAA,GACA,MAAAwB,EAAAH,EAAApD,KAAA6B,MAAA7B,KAAA8B,SAAAsB,EAAArB,SACA,KAAA5C,iBAAAoE,EACA,GACA,KAEA,EAEAJ,YAAAA,GACA,KAAAlH,mBACAwG,cAAA,KAAAxG,kBACA,KAAAA,iBAAA,KAEA,EAGA0B,gBAAAA,GACA,SAAA9B,QAAA,KAAAA,OAAA2H,aAAAC,UAAAC,MAAA,KAAA7H,OAAA2H,aAAAC,UAAAE,WAAA,CAKA,KAAA5H,SAAA,aAEA,IACA,KAAAF,OAAA,IAAA4H,UAAA,KAAAzH,OAEA,KAAAH,OAAA+H,OAAA,KACA5G,QAAAC,IAAA,iBACA,KAAAlB,SAAA,YAGAiB,QAAAC,IAAA,0BAGA,KAAAkG,gBAGA,KAAAtH,OAAAgI,UAAAC,IACA,KAAAC,uBAAAD,EAAAxI,OAGA,KAAAO,OAAAmI,QAAA3E,IACArC,QAAAqC,MAAA,eAAAA,GACA,KAAAtD,SAAA,SAMA,KAAAF,OAAAoI,QAAA,KACAjH,QAAAC,IAAA,iBACA,KAAAlB,SAAA,eAMA0F,WAAA,KACA,KAAA9D,oBACA,KAEA,OAAA0B,GACArC,QAAAqC,MAAA,iBAAAA,GACA,KAAAtD,SAAA,OAIA,CAhDA,MAFAiB,QAAAC,IAAA,qBAmDA,EAEAiH,oBAAAA,CAAAC,GACA,KAAAtI,QAAA,KAAAA,OAAA2H,aAAAC,UAAAC,KACA,KAAA7H,OAAAuI,KAAAC,KAAAC,UAAAH,IAEAnH,QAAAoE,KAAA,sBAEA,EAEA2C,sBAAAA,CAAAzI,GACA,IACA,MAAA6I,EAAAE,KAAAE,MAAAjJ,GAIA,OAHA0B,QAAAC,IAAA,iBAAAkH,GAGAA,EAAAK,MACA,iBAEAL,EAAA5C,YAAAkD,IAAAN,EAAA3C,OAUA,KAAAF,WAAA6C,EAAA5C,MAAA4C,EAAA3C,OAGA,MAEA,uBAEA2C,EAAA5C,OACA,KAAApC,iBAAAgF,EAAA5C,OAEA,MAEA,mBAEA4C,EAAApB,UAAA2B,MAAAC,QAAAR,EAAApB,WACA,KAAAD,mBAAAqB,EAAApB,UAEA,MAEA,kBAEAoB,EAAA3I,OAAA,KAAAG,WAAAwI,EAAA3I,QACA,KAAAkH,YAAAyB,EAAA3I,OAEA,MAEA,QACAwB,QAAAoE,KAAA,oBAAA+C,EAAAK,MAEA,OAAAnF,GACArC,QAAAqC,MAAA,mBAAAA,EACA,CACA,EAGAuF,sBAAAA,GACA,KAAA1I,YAAA,KAAAA,UACA,EAGAwB,aAAAA,CAAAoG,GAEA,UAAAA,EAAAe,MACA,KAAAC,qBAEA,EAGAA,mBAAAA,GACA,MAAA1B,EAAAC,OAAAC,KAAA,KAAA5H,kBACA,OAAA0H,EAAArB,OAAA,OAGA,KAAA5F,yBAAA,KAAAA,wBAAA,GAAAiH,EAAArB,OACA,MAAAgD,EAAA3B,EAAA,KAAAjH,yBACAa,QAAAC,IAAA,UAAA8H,IAAA,KAAAjJ,SAGA,KAAAwE,0BAEAmB,WAAA,KACA,KAAAtC,iBAAA4F,GACA/H,QAAAC,IAAA,WAAA8H,MACA,GACA,EAGAzH,oBAAAA,GAIA,GAHAN,QAAAC,IAAA,gBAGA,KAAAzB,QAAA,KAAAA,MAAAuB,gBAAA,KAAAvB,MAAAuB,cAAAQ,cACA,OAIA,MAAA2E,EAAA,KAAA1G,MAAAuB,cAAAQ,cAAAgE,MACAY,EAAA,KAAA3G,MAAAuB,cAAAQ,cAAAiE,MAGA,KAAAvF,mBACA,KAAAP,iBAAA,KAAAU,qBACA8F,IAAA,KAAA9F,oBAAA,IAAA+F,IACAnF,QAAAC,IAAA,sBAAAb,sBAGA,KAAAZ,MAAAmG,OAAA,KAAAvF,mBAAA,YAGA,KAAA4I,qBAEA,EAGAA,kBAAAA,GACA,KAAAxJ,OAAA,KAAAA,MAAAuB,gBAGA,KAAAgC,mBAEA/B,QAAAC,IAAA,cACA,EAEA,gBAAAN,GACA,IACA,MAAAsI,QAAAC,MAAA,mBAAAxG,KAAAC,SACA,KAAA/C,aAAAqJ,EAAAE,OAGA,KAAA1J,aAAA,KAAAG,OAAAH,aAGA,KAAAE,WAAA,GACA0H,OAAAC,KAAA,KAAA1H,OAAAgH,QAAAzF,QAAAiI,IACA,MAAA5J,EAAA,KAAAI,OAAAgH,OAAAwC,GACA,KAAAzJ,WAAAyJ,GAAA,KAAAxJ,OAAAqD,SAAAoG,UACA7J,EAAA8J,KACA,GAAA9J,EAAA8J,UAAA5G,KAAAC,UAIA,MAAA4G,EAAA,KAAA3J,OAAAgH,OAAA,KAAAnH,cACA,KAAAW,mBAAAmJ,GAAAnJ,oBAAA,KAAAR,OAAAqD,SAAA7C,mBACA,KAAAnB,uBAAAsK,GAAAvK,iBAAA,KAAAY,OAAAqD,SAAA4D,uBAEA,KAAA7G,MAAA,KAAAJ,OAAAqD,SAAAjD,MAEAgB,QAAAC,IAAA,eAAArB,QACAoB,QAAAC,IAAA,aAAAxB,cACAuB,QAAAC,IAAA,cAAAb,oBACAY,QAAAC,IAAA,YAAAhC,uBACA,OAAAoE,GACArC,QAAAqC,MAAA,iBAAAA,GACA,KAAA5D,aAAA,cACA,KAAAW,mBAAA,SACA,KAAAnB,uBAAA,UACA,KAAAU,WAAA,CACA6J,YAAA,8BAAA9G,KAAAC,QAEA,CACA,GAGA8G,aAAAA,GAEA,KAAAjK,OACA,KAAAA,MAAA8C,UAEA,KAAA/C,KACA,KAAAA,IAAA+C,SAAA,GAIA,KAAA6E,eAGA,KAAA5E,wBAGA,KAAAjC,uBACA0F,aAAA,KAAA1F,uBAIA,KAAAT,SACA,KAAAA,OAAA6J,QACA,KAAA7J,OAAA,MAIAT,OAAAuK,oBAAA,cAAAlI,cACArC,OAAAuK,oBAAA,eAAAjI,cACA,GC7vByO,I,UCQrOkI,GAAY,OACd,EACAnL,EACAU,GACA,EACA,KACA,WACA,MAIF,EAAeyK,E,QChBfC,EAAAA,GAAIjK,OAAOkK,eAAgB,EAE3B,IAAID,EAAAA,GAAI,CACNpL,OAAQsL,GAAKA,EAAEC,KACdC,OAAO,O,GCNNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB3B,IAAjB4B,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CACjDI,GAAIJ,EACJK,QAAQ,EACRH,QAAS,CAAC,GAUX,OANAI,EAAoBN,GAAUO,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG3EI,EAAOE,QAAS,EAGTF,EAAOD,OACf,CAGAH,EAAoBS,EAAIF,E,WC5BxB,IAAIG,EAAW,GACfV,EAAoBW,EAAI,SAASC,EAAQC,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASxG,EAAI,EAAGA,EAAIiG,EAAS9E,OAAQnB,IAAK,CACrCoG,EAAWH,EAASjG,GAAG,GACvBqG,EAAKJ,EAASjG,GAAG,GACjBsG,EAAWL,EAASjG,GAAG,GAE3B,IAJA,IAGIyG,GAAY,EACPC,EAAI,EAAGA,EAAIN,EAASjF,OAAQuF,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAa7D,OAAOC,KAAK6C,EAAoBW,GAAGS,MAAM,SAASnC,GAAO,OAAOe,EAAoBW,EAAE1B,GAAK4B,EAASM,GAAK,GAChKN,EAASQ,OAAOF,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbR,EAASW,OAAO5G,IAAK,GACrB,IAAI6G,EAAIR,SACExC,IAANgD,IAAiBV,EAASU,EAC/B,CACD,CACA,OAAOV,CArBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAItG,EAAIiG,EAAS9E,OAAQnB,EAAI,GAAKiG,EAASjG,EAAI,GAAG,GAAKsG,EAAUtG,IAAKiG,EAASjG,GAAKiG,EAASjG,EAAI,GACrGiG,EAASjG,GAAK,CAACoG,EAAUC,EAAIC,EAwB/B,C,eC5BAf,EAAoBuB,EAAI,SAASnB,GAChC,IAAIoB,EAASpB,GAAUA,EAAOqB,WAC7B,WAAa,OAAOrB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAJ,EAAoB0B,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNAxB,EAAoB0B,EAAI,SAASvB,EAASyB,GACzC,IAAI,IAAI3C,KAAO2C,EACX5B,EAAoB6B,EAAED,EAAY3C,KAASe,EAAoB6B,EAAE1B,EAASlB,IAC5E/B,OAAO4E,eAAe3B,EAASlB,EAAK,CAAE8C,YAAY,EAAMC,IAAKJ,EAAW3C,IAG3E,C,eCPAe,EAAoBiC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO1N,MAAQ,IAAI2N,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,kBAAXnN,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxB+K,EAAoB6B,EAAI,SAASQ,EAAKC,GAAQ,OAAOpF,OAAOqF,UAAUC,eAAehC,KAAK6B,EAAKC,EAAO,C,eCCtGtC,EAAoBsB,EAAI,SAASnB,GACX,qBAAXsC,QAA0BA,OAAOC,aAC1CxF,OAAO4E,eAAe3B,EAASsC,OAAOC,YAAa,CAAEC,MAAO,WAE7DzF,OAAO4E,eAAe3B,EAAS,aAAc,CAAEwC,OAAO,GACvD,C,eCNA3C,EAAoB4C,IAAM,SAASxC,GAGlC,OAFAA,EAAOyC,MAAQ,GACVzC,EAAO0C,WAAU1C,EAAO0C,SAAW,IACjC1C,CACR,C,eCCA,IAAI2C,EAAkB,CACrB,IAAK,GAaN/C,EAAoBW,EAAEQ,EAAI,SAAS6B,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4B/N,GAC/D,IAKI8K,EAAU+C,EALVnC,EAAW1L,EAAK,GAChBgO,EAAchO,EAAK,GACnBiO,EAAUjO,EAAK,GAGIsF,EAAI,EAC3B,GAAGoG,EAASwC,KAAK,SAAShD,GAAM,OAA+B,IAAxB0C,EAAgB1C,EAAW,GAAI,CACrE,IAAIJ,KAAYkD,EACZnD,EAAoB6B,EAAEsB,EAAalD,KACrCD,EAAoBS,EAAER,GAAYkD,EAAYlD,IAGhD,GAAGmD,EAAS,IAAIxC,EAASwC,EAAQpD,EAClC,CAEA,IADGkD,GAA4BA,EAA2B/N,GACrDsF,EAAIoG,EAASjF,OAAQnB,IACzBuI,EAAUnC,EAASpG,GAChBuF,EAAoB6B,EAAEkB,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOhD,EAAoBW,EAAEC,EAC9B,EAEI0C,EAAqBC,KAAK,sBAAwBA,KAAK,uBAAyB,GACpFD,EAAmBtM,QAAQiM,EAAqBO,KAAK,KAAM,IAC3DF,EAAmBG,KAAOR,EAAqBO,KAAK,KAAMF,EAAmBG,KAAKD,KAAKF,G,IC/CvF,IAAII,EAAsB1D,EAAoBW,OAAErC,EAAW,CAAC,KAAM,WAAa,OAAO0B,EAAoB,KAAO,GACjH0D,EAAsB1D,EAAoBW,EAAE+C,E", "sources": ["webpack://live2d/./src/App.vue", "webpack://live2d/src/App.vue", "webpack://live2d/./src/App.vue?c036", "webpack://live2d/./src/App.vue?0e40", "webpack://live2d/./src/main.js", "webpack://live2d/webpack/bootstrap", "webpack://live2d/webpack/runtime/chunk loaded", "webpack://live2d/webpack/runtime/compat get default export", "webpack://live2d/webpack/runtime/define property getters", "webpack://live2d/webpack/runtime/global", "webpack://live2d/webpack/runtime/hasOwnProperty shorthand", "webpack://live2d/webpack/runtime/make namespace object", "webpack://live2d/webpack/runtime/node module decorator", "webpack://live2d/webpack/runtime/jsonp chunk loading", "webpack://live2d/webpack/startup"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"live2d-container\",style:({ backgroundColor: _vm.currentBackgroundColor })},[_c('canvas',{ref:\"liveCanvas\"})])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\n<script>\nimport * as PIXI from 'pixi.js';\nimport { Live2DModel } from 'pixi-live2d-display/cubism4';\nwindow.PIXI = PIXI; // 为了pixi-live2d-display内部调用\n\nexport default {\n  data() {\n    return {\n      app: null,\n      model: null,\n      currentModel: 'greenlive2d',\n      availableMotions: {},\n      modelPaths: {},\n      config: null,\n      currentBackgroundColor: '#000000',\n      // WebSocket相关\n      socket: null,\n      isfirst: true,\n      wsStatus: 'disconnected',\n      wsUrl: 'ws://localhost:8002',\n      // 自动播放控制\n      autoPlayInterval: null,\n      // 状态显示控制\n      showStatus: false,\n      // 当前动作组索引\n      currentMotionGroupIndex: 0,\n      // 默认动作组\n      defaultMotionGroup: 'center',\n      // 循环动作组\n      loopMotionGroup: 'center',\n      // 定时器引用\n      _returnToDefaultTimer: null,\n      // 默认动作循环控制\n      defaultMotionInterval: null,\n      // 上次执行动作的时间\n      lastMotionTime: null,\n      // 动作播放状态锁定\n      isMotionPlaying: false\n    };\n  },\n  \n  async mounted() {\n    // 先加载配置文件\n    await this.loadConfig();\n    \n    this.initPixiApp();\n    this.loadModel(this.currentModel).then(() => {\n      // 模型加载完成后，添加全局动作监听\n      if (this.model && this.model.internalModel) {\n        console.log('添加全局动作监听');\n        // 尝试多种可能的事件名称\n        const possibleEventNames = ['motionFinished', 'motionEnd', 'finished', 'complete'];\n        \n        possibleEventNames.forEach(eventName => {\n          this.model.on(eventName, () => {\n            console.log(`事件触发: ${eventName}`);\n            this.handleMotionFinished();\n          });\n          \n          if (this.model.internalModel.motionManager) {\n            this.model.internalModel.motionManager.on(eventName, () => {\n              console.log(`内部事件触发: ${eventName}`);\n              this.handleMotionFinished();\n            });\n          }\n        });\n      }\n    });\n    \n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize);\n    \n    // 添加键盘事件监听\n    window.addEventListener('keydown', this.handleKeyDown);\n    \n    // 自动连接WebSocket\n    this.connectWebSocket();\n  },\n  \n  methods: {\n    initPixiApp() {\n      this.app = new PIXI.Application({\n        view: this.$refs.liveCanvas,\n        autoStart: true,\n        resizeTo: window,\n        backgroundAlpha: 0,\n      });\n    },\n    \n    async loadModel(modelName) {\n      // 如果已有模型，先销毁\n      if (this.model) {\n        this.app.stage.removeChild(this.model);\n        this.model.destroy();\n        this.model = null;\n        \n        // 停止默认动作循环\n        this.stopDefaultMotionLoop();\n      }\n      \n      try {\n        // 添加时间戳防止缓存\n        const modelPath = this.modelPaths[modelName] + (this.modelPaths[modelName].includes('?') ? '&' : '?') + `t=${Date.now()}`;\n        \n        // 加载新模型\n        this.model = await Live2DModel.from(modelPath);\n        \n        // 添加到舞台\n        this.app.stage.addChild(this.model);\n        \n        // 设置模型位置和缩放\n        this.fitModelToScreen();\n        \n        // 禁用拖拽\n        this.model.draggable = false;\n        \n        // 获取可用的动作列表\n        this.availableMotions = this.model.internalModel.settings.motions;\n        \n        // 默认播放center动作\n        if (this.availableMotions[this.defaultMotionGroup]) {\n          this.playRandomMotion(this.defaultMotionGroup);\n          \n          // 启动默认动作循环\n          this.startDefaultMotionLoop();\n        }\n        \n        console.log(`模型 ${modelName} 加载成功`);\n      } catch (error) {\n        console.error(`模型加载失败:`, error);\n      }\n    },\n    \n    // 适配屏幕的方法\n    fitModelToScreen() {\n      if (!this.model) return;\n      \n      // 获取屏幕尺寸\n      const screenWidth = this.app.renderer.width;\n      const screenHeight = this.app.renderer.height;\n      \n      // 获取模型原始尺寸\n      const modelWidth = this.model.width;\n      const modelHeight = this.model.height;\n      \n      // 计算适合屏幕的缩放比例\n      // 使用较小的缩放比例确保模型完全在屏幕内\n      const scaleX = screenWidth / modelWidth;\n      const scaleY = screenHeight / modelHeight;\n      \n      // 使用较小的缩放比例确保模型完全在屏幕内\n      const scale = Math.min(scaleX, scaleY) * 0.9; // 乘以0.9留出一些边距\n      \n      // 应用缩放\n      this.model.scale.set(scale);\n      \n      // 调整模型位置，使其居中\n      this.model.x = screenWidth / 2;\n      this.model.y = screenHeight / 2; // 居中显示\n      \n      // 设置锚点为中心，这样缩放和位置会以模型中心为基准\n      this.model.anchor.set(0.5, 0.5);\n      \n      console.log(`模型适配屏幕: 缩放=${scale}, 位置=(${this.model.x}, ${this.model.y})`);\n    },\n    \n    // 处理窗口大小变化\n    handleResize() {\n      if (this.model) {\n        this.fitModelToScreen();\n      }\n    },\n    \n    // 强制重置所有Live2D参数到0并清理状态\n    forceResetAllParameters() {\n      if (!this.model || !this.model.internalModel) return;\n      \n      try {\n        const coreModel = this.model.internalModel.coreModel;\n        const motionManager = this.model.internalModel.motionManager;\n        \n        // 强制停止所有动作\n        if (motionManager) {\n          motionManager.stopAllMotions();\n          // 清理动作管理器状态\n          if (motionManager._motionQueueManager) {\n            motionManager._motionQueueManager.stopAllMotions();\n          }\n        }\n        \n        // 使用正确的Live2D Cubism API\n        if (coreModel) {\n          const parameterCount = coreModel.getParameterCount();\n          \n          // 重置所有参数到默认值\n          for (let i = 0; i < parameterCount; i++) {\n            const defaultValue = coreModel.getParameterDefaultValue(i);\n            coreModel.setParameterValueByIndex(i, defaultValue);\n          }\n          \n          // 重置所有部件透明度到1.0\n          const partCount = coreModel.getPartCount();\n          for (let i = 0; i < partCount; i++) {\n            coreModel.setPartOpacityByIndex(i, 1.0);\n          }\n        }\n        \n        // 强制更新模型\n        this.model.internalModel.update();\n        \n        console.log('所有Live2D参数已重置到默认值并清理状态');\n      } catch (error) {\n        console.warn('参数重置失败:', error);\n        \n        // 如果API调用失败，尝试简单的动作停止\n        try {\n          if (this.model.internalModel.motionManager) {\n            this.model.internalModel.motionManager.stopAllMotions();\n          }\n          console.log('已停止所有动作');\n        } catch (fallbackError) {\n          console.warn('动作停止也失败:', fallbackError);\n        }\n      }\n    },\n\n    // 播放指定组的指定索引动作\n    playMotion(group, index) {\n      if (!this.model || !this.availableMotions[group]) return;\n      \n      console.log(`准备播放动作: ${group}[${index}]`);\n      \n      // 强制停止所有动作并归零参数\n      this.forceResetAllParameters();\n      \n      // 重置播放状态\n      this.isMotionPlaying = false;\n      \n      // 短暂延迟确保状态清理完成\n      setTimeout(() => {\n        try {\n          // 设置动作播放锁定\n          this.isMotionPlaying = true;\n          \n          // 如果要播放的不是默认动作，更新上次动作时间\n          if (group !== this.defaultMotionGroup) {\n            this.lastMotionTime = Date.now();\n          }\n\n          // 为动作添加完成监听\n          this.model.once('motionFinished', () => {\n            console.log(`动作 ${group}[${index}] 完成`);\n            this.isMotionPlaying = false;\n            this.handleMotionFinished();\n          });\n\n          // 处理左右方向的特殊情况\n          if(group === 'left'){\n            this.model.motion('right', index, 3, true, 0.8);\n          } else if(group === 'right'){\n            this.model.motion('left', index, 3, true, 0.8);\n          } else {\n            this.model.motion(group, index, 3, true, 0.8);\n          }\n          \n          console.log(`开始播放动作: ${group}[${index}]`);\n          \n          // 确保动作完成后回到默认动作\n          this.ensureReturnToDefaultMotion();\n          \n        } catch (error) {\n          console.error('播放动作失败:', error);\n          this.isMotionPlaying = false;\n        }\n      }, 50); // 50ms延迟确保状态清理\n    },\n    \n    // 播放指定组的随机动作\n    playRandomMotion(group) {\n      if (!this.model || !this.availableMotions[group]) return;\n      \n      const motions = this.availableMotions[group];\n      const index = Math.floor(Math.random() * motions.length);\n      \n      console.log(`准备播放随机动作: ${group}[${index}]`);\n      \n      // 强制停止所有动作并归零参数\n      this.forceResetAllParameters();\n      \n      // 重置播放状态\n      this.isMotionPlaying = false;\n      \n      // 短暂延迟确保状态清理完成\n      setTimeout(() => {\n        try {\n          // 如果要播放的不是默认动作和循环动作，更新上次动作时间\n          if (group !== this.defaultMotionGroup && group !== this.loopMotionGroup) {\n            // 设置动作播放锁定\n            this.isMotionPlaying = true;\n            \n            // 更新上次动作时间，重新开始20秒计时\n            this.lastMotionTime = Date.now();\n            \n            // 增加淡入淡出时间，使过渡更平滑\n            this.model.motion(group, index, 3, true, 0.8);\n            \n            // 为非默认动作添加动作完成监听\n            this.model.once('motionFinished', () => {\n              console.log(`动作 ${group}[${index}] 完成，切换到默认动作`);\n              this.isMotionPlaying = false;\n              // 切换到默认动作前也要归零\n              this.forceResetAllParameters();\n              setTimeout(() => {\n                if (this.availableMotions[this.defaultMotionGroup]) {\n                  this.model.motion(this.defaultMotionGroup, 0, 1, false, 1.5);\n                }\n              }, 50);\n            });\n          } else {\n            // 如果是默认动作或循环动作，直接播放\n            this.model.motion(group, index, 3, true, 0.8);\n          }\n          \n          console.log(`开始播放随机动作: ${group}[${index}]`);\n          \n        } catch (error) {\n          console.error('播放随机动作失败:', error);\n          this.isMotionPlaying = false;\n        }\n      }, 50); // 50ms延迟确保状态清理\n    },\n    \n    // 确保动作完成后回到默认动作\n    ensureReturnToDefaultMotion() {\n      // 清除之前的定时器，避免多个定时器同时触发\n      if (this._returnToDefaultTimer) {\n        clearTimeout(this._returnToDefaultTimer);\n      }\n      \n      // 使用setTimeout检测动作是否完成\n      this._returnToDefaultTimer = setTimeout(() => {\n        // 检查动作是否已完成\n        if (this.model && \n            this.model.internalModel && \n            this.model.internalModel.motionManager.isFinished()) {\n          console.log('动作已完成（通过定时器检测）');\n          \n          // 获取当前正在播放的动作组和索引\n          const currentGroup = this.model.internalModel.motionManager.group;\n          const currentIndex = this.model.internalModel.motionManager.index;\n          \n          // 如果当前不是自动播放模式且不是已经在播放默认动作，才切换到默认动作\n          if (!this.autoPlayInterval && \n              this.availableMotions[this.defaultMotionGroup] && \n              (currentGroup !== this.defaultMotionGroup || currentIndex !== 0)) {\n            console.log('切换到默认动作');\n            // 使用较长的淡入时间，使过渡更平滑\n            this.model.motion(this.defaultMotionGroup, 0, 1, false, 1.5);\n            \n            // 更新上次动作时间，重新开始20秒计时\n            this.lastMotionTime = Date.now();\n          }\n        } else {\n          // 如果动作还没完成，继续检测\n          this.ensureReturnToDefaultMotion();\n        }\n      }, 2000); // 设置一个合理的延迟，根据动作的平均持续时间调整\n    },\n    \n    // 启动默认动作循环\n    startDefaultMotionLoop() {\n      // 先停止可能存在的循环\n      this.stopDefaultMotionLoop();\n      \n      // 设置默认动作循环间隔（较短，例如5秒）\n      const defaultLoopInterval = 1500; // 5秒\n      \n      // 设置yun动作循环间隔（20秒）\n      const yunLoopInterval = 20000; // 20秒\n      \n      // 记录上次执行动作的时间\n      if (!this.lastMotionTime) {\n        this.lastMotionTime = Date.now();\n      }\n      \n      // 启动循环\n      this.defaultMotionInterval = setInterval(() => {\n        const currentTime = Date.now();\n        \n        // 检查当前是否可以播放动作\n        if (this.model && \n            this.model.internalModel && \n            this.model.internalModel.motionManager &&\n            this.model.internalModel.motionManager.isFinished()) {\n          \n          // 获取当前正在播放的动作组\n          const currentGroup = this.model.internalModel.motionManager.group;\n          \n          // 计算距离上次动作的时间\n          const timeSinceLastMotion = currentTime - this.lastMotionTime;\n          \n          // 如果距离上次动作已经过了20秒，播放yun动作\n          if (timeSinceLastMotion >= yunLoopInterval) {\n            // 只有当前没有播放其他指令动作时才播放yun动作\n            if (!currentGroup || currentGroup === this.defaultMotionGroup) {\n              // console.log('循环播放yun动作，距离上次动作时间：' + timeSinceLastMotion + 'ms');\n              \n              // 检查是否有yun动作组\n              // if (this.availableMotions[this.loopMotionGroup]) {\n              //   this.model.motion(this.loopMotionGroup, 0, 1, false, 0.5);\n              // } else {\n                // 如果没有yun动作组，则播放默认动作\n                this.model.motion(this.defaultMotionGroup, 0, 1, false, 0.5);\n              // }\n              \n              // 更新上次动作时间\n              this.lastMotionTime = currentTime;\n            }\n          } \n          // 否则，如果当前没有播放动作或已经播放完成，播放默认动作\n          else if (!currentGroup || currentGroup === this.defaultMotionGroup) {\n            console.log('循环播放默认动作');\n            this.model.motion(this.defaultMotionGroup, 0, 1, false, 0.5);\n          }\n        }\n      }, defaultLoopInterval); // 每5秒检查一次\n      \n      // console.log(`启动默认动作循环，默认间隔: ${defaultLoopInterval}ms，yun间隔: ${yunLoopInterval}ms`);\n    },\n    \n    // 停止默认动作循环\n    stopDefaultMotionLoop() {\n      if (this.defaultMotionInterval) {\n        clearInterval(this.defaultMotionInterval);\n        this.defaultMotionInterval = null;\n        console.log('停止默认动作循环');\n      }\n    },\n    \n    // 切换模型\n    async switchModel(modelName) {\n      if (this.currentModel === modelName) return;\n      \n      this.currentModel = modelName;\n      \n      // 更新默认动作组和背景色\n      const modelConfig = this.config.models[modelName];\n      this.defaultMotionGroup = modelConfig?.defaultMotionGroup || this.config.settings.defaultMotionGroup;\n      this.currentBackgroundColor = modelConfig?.backgroundColor || this.config.settings.defaultBackgroundColor;\n      \n      console.log(`切换到模型: ${modelName}, 默认动作组: ${this.defaultMotionGroup}, 背景色: ${this.currentBackgroundColor}`);\n      \n      await this.loadModel(modelName);\n    },\n    \n    // 播放动作序列\n    async playMotionSequence(sequence) {\n      if (!this.model) return;\n      \n      for (const motion of sequence) {\n        await new Promise(resolve => {\n          this.model.once('motionFinished', resolve);\n          if (typeof motion === 'string') {\n            this.playRandomMotion(motion);\n          } else {\n            this.playMotion(motion.group, motion.index);\n          }\n        });\n      }\n      \n      // 序列播放完成后，确保回到默认动作\n      if (!this.autoPlayInterval && this.availableMotions[this.defaultMotionGroup]) {\n        this.model.motion(this.defaultMotionGroup, 0, 1, false);\n      }\n    },\n    \n    // 自动播放控制\n    startAutoPlay() {\n      // 清除可能存在的定时器\n      this.stopAutoPlay();\n\n      // 随机选择一个动作组\n      const groups = Object.keys(this.availableMotions);\n          if (groups.length > 0) {\n            const randomGroup = groups[Math.floor(Math.random() * groups.length)];\n            this.playRandomMotion(randomGroup);\n          }\n      \n      // 只有在WebSocket未连接时才启动自动播放\n      if (this.wsStatus !== 'connected') {\n        this.autoPlayInterval = setInterval(() => {\n          // 随机选择一个动作组\n          const groups = Object.keys(this.availableMotions);\n          if (groups.length > 0) {\n            const randomGroup = groups[Math.floor(Math.random() * groups.length)];\n            this.playRandomMotion(randomGroup);\n          }\n        }, 4000); // 每2秒随机播放一个动作\n      }\n    },\n    \n    stopAutoPlay() {\n      if (this.autoPlayInterval) {\n        clearInterval(this.autoPlayInterval);\n        this.autoPlayInterval = null;\n      }\n    },\n    \n    // WebSocket相关方法\n    connectWebSocket() {\n      if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {\n        console.log('WebSocket已连接或正在连接中');\n        return;\n      }\n      \n      this.wsStatus = 'connecting';\n      \n      try {\n        this.socket = new WebSocket(this.wsUrl);\n        \n        this.socket.onopen = () => {\n          console.log('WebSocket连接成功');\n          this.wsStatus = 'connected';\n          \n          // 不发送初始化消息，只接收\n          console.log('WebSocket已连接，等待接收消息...');\n          \n          // 停止自动播放\n          this.stopAutoPlay();\n        };\n        \n        this.socket.onmessage = (event) => {\n          this.handleWebSocketMessage(event.data);\n        };\n        \n        this.socket.onerror = (error) => {\n          console.error('WebSocket错误:', error);\n          this.wsStatus = 'error';\n          \n          // 启动自动播放\n          // this.startAutoPlay();\n        };\n        \n        this.socket.onclose = () => {\n          console.log('WebSocket连接关闭');\n          this.wsStatus = 'disconnected';\n          \n          // 启动自动播放\n          // this.startAutoPlay();\n          \n          // 尝试重新连接\n          setTimeout(() => {\n            this.connectWebSocket();\n          }, 5000); // 5秒后尝试重新连接\n        };\n      } catch (error) {\n        console.error('WebSocket连接失败:', error);\n        this.wsStatus = 'error';\n        \n        // 启动自动播放\n        // this.startAutoPlay();\n      }\n    },\n    \n    sendWebSocketMessage(message) {\n      if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n        this.socket.send(JSON.stringify(message));\n      } else {\n        console.warn('WebSocket未连接，无法发送消息');\n      }\n    },\n    \n    handleWebSocketMessage(data) {\n      try {\n        const message = JSON.parse(data);\n        console.log('收到WebSocket消息:', message);\n        \n        // 根据消息类型执行不同操作\n        switch (message.type) {\n          case 'playMotion':\n            // 播放指定动作\n            if (message.group && message.index !== undefined) {\n              //这里两个参数需要传\n              // {\n                  //  \"type\": \"playMotion\",\n                  //  \"group\": \"Tap\",    // 动作组名称，如\"Idle\"、\"Tap\"、\"FlickUp\"等\n                  //  \"index\": 1         // 该组中的动作索引，从0开始\n              // }\n              // if(message.group === 'center'){\n              //   this.playRandomMotion('sleep', message.index);\n              // } else {\n                this.playMotion(message.group, message.index);\n              // }\n            }\n            break;\n            \n          case 'playRandomMotion':\n            // 播放随机动作\n            if (message.group) {\n              this.playRandomMotion(message.group);\n            }\n            break;\n            \n          case 'playSequence':\n            // 播放动作序列\n            if (message.sequence && Array.isArray(message.sequence)) {\n              this.playMotionSequence(message.sequence);\n            }\n            break;\n            \n          case 'switchModel':\n            // 切换模型\n            if (message.model && this.modelPaths[message.model]) {\n              this.switchModel(message.model);\n            }\n            break;\n            \n          default:\n            console.warn('未知的WebSocket消息类型:', message.type);\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    },\n    \n    // 切换状态显示\n    toggleStatusVisibility() {\n      this.showStatus = !this.showStatus;\n    },\n    \n    // 处理键盘事件\n    handleKeyDown(event) {\n      // 空格键切换动作\n      if (event.code === 'Space') {\n        this.playNextMotionGroup();\n      }\n    },\n    \n    // 播放下一个动作组的随机动作\n    playNextMotionGroup() {\n      const groups = Object.keys(this.availableMotions);\n      if (groups.length === 0) return;\n      \n      // 循环切换到下一个动作组\n      this.currentMotionGroupIndex = (this.currentMotionGroupIndex + 1) % groups.length;\n      const nextGroup = groups[this.currentMotionGroupIndex];\n      console.log(`当前动作组: ${nextGroup}`,this.isfirst);\n\n      // 强制归零后播放\n      this.forceResetAllParameters();\n      \n      setTimeout(() => {\n        this.playRandomMotion(nextGroup);\n        console.log(`切换到动作组: ${nextGroup}`);\n      }, 50);\n    },\n    \n    // 处理动作完成事件\n    handleMotionFinished() {\n      console.log('动作完成处理函数被调用');\n      \n      // 确保模型和动作管理器存在\n      if (!this.model || !this.model.internalModel || !this.model.internalModel.motionManager) {\n        return;\n      }\n      \n      // 获取当前正在播放的动作组和索引\n      const currentGroup = this.model.internalModel.motionManager.group;\n      const currentIndex = this.model.internalModel.motionManager.index;\n      \n      // 如果当前不是自动播放模式且不是已经在播放默认动作，才切换到默认动作\n      if (!this.autoPlayInterval && \n          this.availableMotions[this.defaultMotionGroup] && \n          (currentGroup !== this.defaultMotionGroup || currentIndex !== 0)) {\n        console.log(`动作完成，重置到默认动作: ${this.defaultMotionGroup}`);\n        \n        // 使用较长的淡入时间，使过渡更平滑\n        this.model.motion(this.defaultMotionGroup, 0, 1, false, 1.5);\n        \n        // 重置模型位置和姿态\n        this.resetModelPosition();\n      }\n    },\n\n    // 重置模型到默认位置和姿态\n    resetModelPosition() {\n      if (!this.model || !this.model.internalModel) return;\n      \n      // 重置模型的变换\n      this.fitModelToScreen();\n      \n      console.log('模型已重置到默认位置');\n    },\n    // 加载配置文件\n    async loadConfig() {\n      try {\n        const response = await fetch(`./config.json?t=${Date.now()}`);\n        this.config = await response.json();\n        \n        // 设置当前模型\n        this.currentModel = this.config.currentModel;\n        \n        // 构建模型路径对象\n        this.modelPaths = {};\n        Object.keys(this.config.models).forEach(key => {\n          const model = this.config.models[key];\n          this.modelPaths[key] = this.config.settings.autoCache \n            ? model.path \n            : `${model.path}?t=${Date.now()}`;\n        });\n        \n        // 设置当前模型的默认动作组和背景色\n        const currentModelConfig = this.config.models[this.currentModel];\n        this.defaultMotionGroup = currentModelConfig?.defaultMotionGroup || this.config.settings.defaultMotionGroup;\n        this.currentBackgroundColor = currentModelConfig?.backgroundColor || this.config.settings.defaultBackgroundColor;\n        \n        this.wsUrl = this.config.settings.wsUrl;\n        \n        console.log('配置加载成功:', this.config);\n        console.log('当前模型:', this.currentModel);\n        console.log('默认动作组:', this.defaultMotionGroup);\n        console.log('背景色:', this.currentBackgroundColor);\n      } catch (error) {\n        console.error('配置加载失败，使用默认配置:', error);\n        this.currentModel = 'greenlive2d';\n        this.defaultMotionGroup = 'center';\n        this.currentBackgroundColor = '#000000';\n        this.modelPaths = {\n          greenlive2d: `./lvse/green.model3.json?t=${Date.now()}`\n        };\n      }\n    }\n  },\n  \n  beforeUnmount() {\n    // 清理资源\n    if (this.model) {\n      this.model.destroy();\n    }\n    if (this.app) {\n      this.app.destroy(true);\n    }\n    \n    // 停止自动播放\n    this.stopAutoPlay();\n    \n    // 停止默认动作循环\n    this.stopDefaultMotionLoop();\n    \n    // 清除定时器\n    if (this._returnToDefaultTimer) {\n      clearTimeout(this._returnToDefaultTimer);\n    }\n    \n    // 断开WebSocket连接\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    \n    // 移除事件监听\n    window.removeEventListener('resize', this.handleResize);\n    window.removeEventListener('keydown', this.handleKeyDown);\n  }\n}\n</script>\n<template>\n  <div class=\"live2d-container\" :style=\"{ backgroundColor: currentBackgroundColor }\">\n    <canvas ref=\"liveCanvas\"></canvas>\n    \n    <!-- <div class=\"websocket-status\" v-if=\"showStatus\">\n      WebSocket: {{ wsStatus }}\n      <button @click=\"toggleStatusVisibility\">隐藏</button>\n    </div>\n    <div class=\"status-toggle\" v-else>\n      <button @click=\"toggleStatusVisibility\">显示状态</button>\n    </div> -->\n  </div>\n</template>\n\n<style scoped>\n.live2d-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  overflow: hidden;\n}\n\ncanvas {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  width: 100%;\n  height: 100%;\n  display: block;\n}\n\n.websocket-status {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 10px;\n  border-radius: 5px;\n  color: white;\n  z-index: 10;\n}\n\n.status-toggle {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  z-index: 10;\n}\n\nbutton {\n  padding: 5px 10px;\n  background: #4CAF50;\n  border: none;\n  border-radius: 3px;\n  color: white;\n  cursor: pointer;\n  margin-left: 5px;\n}\n\nbutton:hover {\n  background: #45a049;\n}\n\nbutton:disabled {\n  background: #cccccc;\n  cursor: not-allowed;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "import mod from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=5412bb43&scoped=true\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=5412bb43&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5412bb43\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport App from './App.vue'\n\nVue.config.productionTip = false\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = function(module) {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunklive2d\"] = self[\"webpackChunklive2d\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(4761); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "style", "backgroundColor", "currentBackgroundColor", "ref", "staticRenderFns", "window", "PIXI", "data", "app", "model", "currentModel", "availableMotions", "modelPaths", "config", "socket", "isfirst", "wsStatus", "wsUrl", "autoPlayInterval", "showStatus", "currentMotionGroupIndex", "defaultMotionGroup", "loopMotionGroup", "_returnToDefaultTimer", "defaultMotionInterval", "lastMotionTime", "isMotionPlaying", "mounted", "loadConfig", "initPixiApp", "loadModel", "then", "internalModel", "console", "log", "possibleEventNames", "for<PERSON>ach", "eventName", "on", "handleMotionFinished", "motionManager", "addEventListener", "handleResize", "handleKeyDown", "connectWebSocket", "methods", "view", "$refs", "liveCanvas", "autoStart", "resizeTo", "backgroundAlpha", "modelName", "stage", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "stopDefaultMotionLoop", "modelPath", "includes", "Date", "now", "Live2DModel", "from", "<PERSON><PERSON><PERSON><PERSON>", "fitModelToScreen", "draggable", "settings", "motions", "playRandomMotion", "startDefaultMotionLoop", "error", "screenWidth", "renderer", "width", "screenHeight", "height", "modelWidth", "modelHeight", "scaleX", "scaleY", "scale", "Math", "min", "set", "x", "y", "anchor", "forceResetAllParameters", "coreModel", "stopAllMotions", "_motionQueueManager", "parameterCount", "getParameterCount", "i", "defaultValue", "getParameterDefaultValue", "setParameterValueByIndex", "partCount", "getPartCount", "setPartOpacityByIndex", "update", "warn", "fallback<PERSON><PERSON>r", "playMotion", "group", "index", "setTimeout", "once", "motion", "ensureReturnToDefaultMotion", "floor", "random", "length", "clearTimeout", "isFinished", "currentGroup", "currentIndex", "defaultLoopInterval", "yunLoopInterval", "setInterval", "currentTime", "timeSinceLastMotion", "clearInterval", "switchModel", "modelConfig", "models", "defaultBackgroundColor", "playMotionSequence", "sequence", "Promise", "resolve", "startAutoPlay", "stopAutoPlay", "groups", "Object", "keys", "randomGroup", "readyState", "WebSocket", "OPEN", "CONNECTING", "onopen", "onmessage", "event", "handleWebSocketMessage", "onerror", "onclose", "sendWebSocketMessage", "message", "send", "JSON", "stringify", "parse", "type", "undefined", "Array", "isArray", "toggleStatusVisibility", "code", "playNextMotionGroup", "nextGroup", "resetModelPosition", "response", "fetch", "json", "key", "autoCache", "path", "currentModelConfig", "greenlive2d", "beforeUnmount", "close", "removeEventListener", "component", "<PERSON><PERSON>", "productionTip", "h", "App", "$mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "id", "loaded", "__webpack_modules__", "call", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "every", "splice", "r", "n", "getter", "__esModule", "d", "a", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "e", "obj", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "value", "nmd", "paths", "children", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "bind", "push", "__webpack_exports__"], "sourceRoot": ""}