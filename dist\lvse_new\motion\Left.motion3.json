{"Version": 3, "Meta": {"Duration": 6.133, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 21, "TotalSegmentCount": 170, "TotalPointCount": 489, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 1.444, 0, 2.889, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 1.444, 0, 2.889, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 1.344, 0, 2.689, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 1.344, 0, 2.689, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.022, 0, 0.044, -30, 0.067, -30, 1, 0.1, -30, 0.133, -30, 0.167, -30, 1, 0.256, -30, 0.344, -30, 0.433, -30, 1, 0.5, -30, 0.567, -30, 0.633, -30, 1, 0.733, -30, 0.833, -30, 0.933, -30, 1, 1.122, -30, 1.311, -30, 1.5, -30, 1, 1.622, -30, 1.744, -30, 1.867, -30, 1, 2.033, -30, 2.2, -30, 2.367, -30, 1, 2.5, -30, 2.633, -30, 2.767, -30, 1, 2.911, -30, 3.056, -30, 3.2, -30, 1, 3.322, -30, 3.444, -30, 3.567, -30, 1, 3.722, -30, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 1.344, 0, 2.689, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 1.344, 0, 2.689, -30, 4.033, -30, 1, 4.133, -30, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 1.344, 0, 2.689, 30, 4.033, 30, 1, 4.133, 30, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 1.344, 0, 2.689, -30, 4.033, -30, 1, 4.133, -30, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 1.444, 0, 2.889, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.022, 0, 0.044, -30, 0.067, -30, 1, 0.1, -30, 0.133, -30, 0.167, -30, 1, 0.256, -30, 0.344, -30, 0.433, -30, 1, 0.5, -30, 0.567, -30, 0.633, -30, 1, 0.733, -30, 0.833, -30, 0.933, -30, 1, 1.122, -30, 1.311, -30, 1.5, -30, 1, 1.622, -30, 1.744, -30, 1.867, -30, 1, 2.033, -30, 2.2, -30, 2.367, -30, 1, 2.5, -30, 2.633, -30, 2.767, -30, 1, 2.911, -30, 3.056, -30, 3.2, -30, 1, 3.322, -30, 3.444, -30, 3.567, -30, 1, 3.722, -30, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.1, 0, 0.133, 0, 0.167, 0, 1, 0.256, 0, 0.344, 0, 0.433, 0, 1, 0.5, 0, 0.567, 30, 0.633, 30, 1, 0.733, 30, 0.833, 30, 0.933, 30, 1, 1.122, 30, 1.311, -30, 1.5, -30, 1, 1.622, -30, 1.744, -30, 1.867, -30, 1, 2.033, -30, 2.2, 30, 2.367, 30, 1, 2.5, 30, 2.633, 30, 2.767, 30, 1, 2.911, 30, 3.056, -30, 3.2, -30, 1, 3.322, -30, 3.444, -30, 3.567, -30, 1, 3.722, -30, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.1, 0, 0.133, 0, 0.167, 0, 1, 0.256, 0, 0.344, 0, 0.433, 0, 1, 1.633, 0, 2.833, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.022, 0, 0.044, -30, 0.067, -30, 1, 0.1, -30, 0.133, -30, 0.167, -30, 1, 0.256, -30, 0.344, -30, 0.433, -30, 1, 0.5, -30, 0.567, -30, 0.633, -30, 1, 0.733, -30, 0.833, -30, 0.933, -30, 1, 1.122, -30, 1.311, -30, 1.5, -30, 1, 1.622, -30, 1.744, -30, 1.867, -30, 1, 2.033, -30, 2.2, -30, 2.367, -30, 1, 2.5, -30, 2.633, -30, 2.767, -30, 1, 2.911, -30, 3.056, -30, 3.2, -30, 1, 3.322, -30, 3.444, -30, 3.567, -30, 1, 3.722, -30, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 1.444, 0, 2.889, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.022, 0, 0.044, -0.5, 0.067, -0.5, 1, 0.1, -0.5, 0.133, -0.5, 0.167, -0.5, 1, 0.256, -0.5, 0.344, -0.5, 0.433, -0.5, 1, 0.5, -0.5, 0.567, -0.5, 0.633, -0.5, 1, 0.733, -0.5, 0.833, -0.5, 0.933, -0.5, 1, 1.122, -0.5, 1.311, -0.5, 1.5, -0.5, 1, 1.622, -0.5, 1.744, -0.5, 1.867, -0.5, 1, 2.033, -0.5, 2.2, -0.5, 2.367, -0.5, 1, 2.5, -0.5, 2.633, -0.5, 2.767, -0.5, 1, 2.911, -0.5, 3.056, -0.5, 3.2, -0.5, 1, 3.322, -0.5, 3.444, -0.5, 3.567, -0.5, 1, 3.722, -0.5, 3.878, 1, 4.033, 1, 1, 4.133, 1, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.022, 0, 0.044, -0.5, 0.067, -0.5, 1, 0.1, -0.5, 0.133, -0.5, 0.167, -0.5, 1, 0.256, -0.5, 0.344, -0.5, 0.433, -0.5, 1, 0.5, -0.5, 0.567, -0.5, 0.633, -0.5, 1, 0.733, -0.5, 0.833, -0.5, 0.933, -0.5, 1, 1.122, -0.5, 1.311, -0.5, 1.5, -0.5, 1, 1.622, -0.5, 1.744, -0.5, 1.867, -0.5, 1, 2.033, -0.5, 2.2, -0.5, 2.367, -0.5, 1, 2.5, -0.5, 2.633, -0.5, 2.767, -0.5, 1, 2.911, -0.5, 3.056, -0.5, 3.2, -0.5, 1, 3.322, -0.5, 3.444, -0.5, 3.567, -0.5, 1, 3.722, -0.5, 3.878, 1, 4.033, 1, 1, 4.133, 1, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.1, 0.167, -0.1, 1, 0.256, -0.1, 0.344, -0.1, 0.433, -0.1, 1, 0.5, -0.1, 0.567, -0.6, 0.633, -0.6, 1, 0.733, -0.6, 0.833, -0.6, 0.933, -0.6, 1, 1.122, -0.6, 1.311, 1, 1.5, 1, 1, 1.622, 1, 1.744, 1, 1.867, 1, 1, 2.033, 1, 2.2, -0.6, 2.367, -0.6, 1, 2.5, -0.6, 2.633, -0.6, 2.767, -0.6, 1, 2.911, -0.6, 3.056, 1, 3.2, 1, 1, 3.322, 1, 3.444, 1, 3.567, 1, 1, 3.722, 1, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.022, 0, 0.044, -1, 0.067, -1, 1, 0.1, -1, 0.133, -1, 0.167, -1, 1, 0.256, -1, 0.344, -1, 0.433, -1, 1, 0.5, -1, 0.567, -0.6, 0.633, -0.6, 1, 0.733, -0.6, 0.833, -0.6, 0.933, -0.6, 1, 1.122, -0.6, 1.311, -1, 1.5, -1, 1, 1.622, -1, 1.744, -1, 1.867, -1, 1, 2.033, -1, 2.2, -0.6, 2.367, -0.6, 1, 2.5, -0.6, 2.633, -0.6, 2.767, -0.6, 1, 2.911, -0.6, 3.056, -1, 3.2, -1, 1, 3.322, -1, 3.444, -1, 3.567, -1, 1, 3.722, -1, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.1, 0.167, -0.1, 1, 0.256, -0.1, 0.344, -0.1, 0.433, -0.1, 1, 0.5, -0.1, 0.567, -0.6, 0.633, -0.6, 1, 0.733, -0.6, 0.833, -0.6, 0.933, -0.6, 1, 1.122, -0.6, 1.311, 1, 1.5, 1, 1, 1.622, 1, 1.744, 1, 1.867, 1, 1, 2.033, 1, 2.2, -0.6, 2.367, -0.6, 1, 2.5, -0.6, 2.633, -0.6, 2.767, -0.6, 1, 2.911, -0.6, 3.056, 1, 3.2, 1, 1, 3.322, 1, 3.444, 1, 3.567, 1, 1, 3.722, 1, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.022, 0, 0.044, -1, 0.067, -1, 1, 0.1, -1, 0.133, -1, 0.167, -1, 1, 0.256, -1, 0.344, -1, 0.433, -1, 1, 0.5, -1, 0.567, -0.6, 0.633, -0.6, 1, 0.733, -0.6, 0.833, -0.6, 0.933, -0.6, 1, 1.122, -0.6, 1.311, -1, 1.5, -1, 1, 1.622, -1, 1.744, -1, 1.867, -1, 1, 2.033, -1, 2.2, -0.6, 2.367, -0.6, 1, 2.5, -0.6, 2.633, -0.6, 2.767, -0.6, 1, 2.911, -0.6, 3.056, -1, 3.2, -1, 1, 3.322, -1, 3.444, -1, 3.567, -1, 1, 3.722, -1, 3.878, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 6.133, 0]}]}