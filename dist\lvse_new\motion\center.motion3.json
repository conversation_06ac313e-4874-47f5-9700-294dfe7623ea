{"Version": 3, "Meta": {"Duration": 5.9, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 18, "TotalSegmentCount": 189, "TotalPointCount": 547, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 3.467, 0, 1, 3.522, 0, 3.578, -30, 3.633, -30, 1, 3.689, -30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -30, 4, -30, 1, 4.067, -30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -30, 4.4, -30, 1, 4.467, -30, 4.533, 0, 4.6, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.022, 0, 3.244, 0, 3.467, 0, 1, 3.522, 0, 3.578, -30, 3.633, -30, 1, 3.689, -30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -30, 4, -30, 1, 4.067, -30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -30, 4.4, -30, 1, 4.467, -30, 4.533, 0, 4.6, 0, 1, 4.833, 0, 5.067, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 2.911, 0, 3.022, 0, 3.133, 0, 1, 3.3, 0, 3.467, 30, 3.633, 30, 1, 3.689, 30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, 30, 4, 30, 1, 4.067, 30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, 30, 4.4, 30, 1, 4.467, 30, 4.533, 30, 4.6, 30, 1, 4.733, 30, 4.867, 0, 5, 0, 1, 5.1, 0, 5.2, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 2.911, 0, 3.022, 0, 3.133, 0, 1, 3.3, 0, 3.467, 30, 3.633, 30, 1, 3.689, 30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, 30, 4, 30, 1, 4.067, 30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, 30, 4.4, 30, 1, 4.467, 30, 4.533, 30, 4.6, 30, 1, 4.733, 30, 4.867, 0, 5, 0, 1, 5.011, 0, 5.022, 0, 5.033, 0, 1, 5.122, 0, 5.211, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.078, 0, 3.356, -30, 3.633, -30, 1, 3.689, -30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -30, 4, -30, 1, 4.067, -30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -30, 4.4, -30, 1, 4.467, -30, 4.533, 30, 4.6, 30, 1, 4.833, 30, 5.067, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.078, 0, 3.356, -30, 3.633, -30, 1, 3.689, -30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -30, 4, -30, 1, 4.067, -30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -30, 4.4, -30, 1, 4.467, -30, 4.533, 30, 4.6, 30, 1, 4.833, 30, 5.067, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.078, 0, 3.356, -30, 3.633, -30, 1, 3.689, -30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -30, 4, -30, 1, 4.067, -30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -30, 4.4, -30, 1, 4.467, -30, 4.533, 30, 4.6, 30, 1, 4.833, 30, 5.067, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.078, 0, 3.356, -30, 3.633, -30, 1, 3.689, -30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -30, 4, -30, 1, 4.067, -30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -30, 4.4, -30, 1, 4.467, -30, 4.533, 30, 4.6, 30, 1, 4.833, 30, 5.067, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.078, 0, 3.356, -30, 3.633, -30, 1, 3.689, -30, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -30, 4, -30, 1, 4.067, -30, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -30, 4.4, -30, 1, 4.467, -30, 4.533, 30, 4.6, 30, 1, 4.833, 30, 5.067, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.378, 0, 0.389, 30, 0.4, 30, 1, 0.544, 30, 0.689, 30, 0.833, 30, 1, 1.289, 30, 1.744, 30, 2.2, 30, 1, 2.511, 30, 2.822, 30, 3.133, 30, 1, 3.3, 30, 3.467, 30, 3.633, 30, 1, 4.089, 30, 4.544, 30, 5, 30, 1, 5.1, 30, 5.2, 30, 5.3, 30, 0, 5.9, 30]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.378, 0, 0.389, 30, 0.4, 30, 1, 0.544, 30, 0.689, 30, 0.833, 30, 1, 1.289, 30, 1.744, 30, 2.2, 30, 1, 2.511, 30, 2.822, 30, 3.133, 30, 1, 3.3, 30, 3.467, 30, 3.633, 30, 1, 4.089, 30, 4.544, 30, 5, 30, 1, 5.1, 30, 5.2, 30, 5.3, 30, 0, 5.9, 30]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 2.911, 0, 3.022, 0, 3.133, 0, 1, 3.144, 0, 3.156, 30, 3.167, 30, 1, 3.322, 30, 3.478, 30, 3.633, 30, 1, 4.089, 30, 4.544, 30, 5, 30, 1, 5.011, 30, 5.022, 20, 5.033, 0, 1, 5.044, -20, 5.056, -30, 5.067, -30, 1, 5.144, -30, 5.222, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.078, 0, 3.356, -2, 3.633, -2, 1, 3.689, -2, 3.744, 30, 3.8, 30, 1, 3.867, 30, 3.933, -2, 4, -2, 1, 4.067, -2, 4.133, 30, 4.2, 30, 1, 4.267, 30, 4.333, -2, 4.4, -2, 1, 4.467, -2, 4.533, 30, 4.6, 30, 1, 4.833, 30, 5.067, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.378, 0, 0.389, -30, 0.4, -30, 1, 0.544, -30, 0.689, 30, 0.833, 30, 1, 1.111, 30, 1.389, -30, 1.667, -30, 1, 1.844, -30, 2.022, 30, 2.2, 30, 1, 2.4, 30, 2.6, -30, 2.8, -30, 1, 2.911, -30, 3.022, 30, 3.133, 30, 1, 3.3, 30, 3.467, -30, 3.633, -30, 1, 3.956, -30, 4.278, -30, 4.6, -30, 1, 4.733, -30, 4.867, 28, 5, 28, 1, 5.1, 28, 5.2, -30, 5.3, -30, 0, 5.9, -30]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.378, 0, 0.389, 30, 0.4, 30, 1, 0.544, 30, 0.689, -30, 0.833, -30, 1, 1.111, -30, 1.389, 30, 1.667, 30, 1, 1.844, 30, 2.022, -30, 2.2, -30, 1, 2.4, -30, 2.6, 30, 2.8, 30, 1, 2.911, 30, 3.022, -30, 3.133, -30, 1, 3.3, -30, 3.467, 30, 3.633, 30, 1, 3.956, 30, 4.278, 30, 4.6, 30, 1, 4.733, 30, 4.867, -28, 5, -28, 1, 5.1, -28, 5.2, 30, 5.3, 30, 0, 5.9, 30]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 2.911, 0, 3.022, 0, 3.133, 0, 1, 3.3, 0, 3.467, -15, 3.633, -15, 1, 3.689, -15, 3.744, 2, 3.8, 2, 1, 3.867, 2, 3.933, -8, 4, -8, 1, 4.067, -8, 4.133, 2, 4.2, 2, 1, 4.267, 2, 4.333, -8, 4.4, -8, 1, 4.467, -8, 4.533, -4.616, 4.6, 0, 1, 4.733, 9.232, 4.867, 13, 5, 13, 1, 5.1, 13, 5.2, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.633, 0, 4.467, 0, 5.3, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.8, 0, 1.233, 0, 1.667, 0, 1, 2.044, 0, 2.422, 0, 2.8, 0, 1, 3.633, 0, 4.467, 0, 5.3, 0, 0, 5.9, 0]}]}