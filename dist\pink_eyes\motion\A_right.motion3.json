{"Version": 3, "Meta": {"Duration": 7.667, "Fps": 30.0, "FadeInTime": 0.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 26, "TotalSegmentCount": 183, "TotalPointCount": 503, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 7.667, -30]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, -30, 0, 7.667, -30]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, -30, 0, 0.267, -30, 1, 0.278, -30, 0.289, 30, 0.3, 30, 1, 2.067, 30, 3.833, 30, 5.6, 30, 1, 5.611, 30, 5.622, -30, 5.633, -30, 0, 7.667, -30]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0, 0.3, 0, 1, 0.4, 0, 0.5, 4, 0.6, 4, 1, 0.744, 4, 0.889, 4, 1.033, 4, 1, 1.078, 4, 1.122, 4, 1.167, 4, 1, 1.289, 4, 1.411, 4, 1.533, 4, 1, 1.989, 4, 2.444, 4, 2.9, 4, 1, 3.044, 4, 3.189, 4, 3.333, 4, 1, 3.811, 4, 4.289, 4, 4.767, 4, 1, 4.911, 4, 5.056, 4, 5.2, 4, 1, 5.333, 4, 5.467, 0, 5.6, 0, 1, 5.611, 0, 5.622, 0, 5.633, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0, 0.3, 0, 1, 0.4, 0, 0.5, 4, 0.6, 4, 1, 0.744, 4, 0.889, 4, 1.033, 4, 1, 1.078, 4, 1.122, 4, 1.167, 4, 1, 1.289, 4, 1.411, 4, 1.533, 4, 1, 1.989, 4, 2.444, 4, 2.9, 4, 1, 3.044, 4, 3.189, 4, 3.333, 4, 1, 3.811, 4, 4.289, 4, 4.767, 4, 1, 4.911, 4, 5.056, 4, 5.2, 4, 1, 5.333, 4, 5.467, 0, 5.6, 0, 1, 5.611, 0, 5.622, 0, 5.633, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 27, 0, 0.267, 27, 1, 0.278, 27, 0.289, 27, 0.3, 27, 1, 0.4, 27, 0.5, 15, 0.6, 15, 1, 0.744, 15, 0.889, 15, 1.033, 15, 1, 1.078, 15, 1.122, 15, 1.167, 15, 1, 1.289, 15, 1.411, 15, 1.533, 15, 1, 1.989, 15, 2.444, 15, 2.9, 15, 1, 3.044, 15, 3.189, 15, 3.333, 15, 1, 3.811, 15, 4.289, 15, 4.767, 15, 1, 4.911, 15, 5.056, 15, 5.2, 15, 1, 5.333, 15, 5.467, 27, 5.6, 27, 1, 5.611, 27, 5.622, 27, 5.633, 27, 0, 7.667, 27]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0, 0.3, 0, 1, 0.4, 0, 0.5, 18, 0.6, 18, 1, 0.744, 18, 0.889, 18, 1.033, 18, 1, 1.078, 18, 1.122, 18, 1.167, 18, 1, 1.289, 18, 1.411, 18, 1.533, 18, 1, 1.989, 18, 2.444, 18, 2.9, 18, 1, 3.044, 18, 3.189, 18, 3.333, 18, 1, 3.811, 18, 4.289, 18, 4.767, 18, 1, 4.911, 18, 5.056, 18, 5.2, 18, 1, 5.333, 18, 5.467, 0, 5.6, 0, 1, 5.611, 0, 5.622, 0, 5.633, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0, 0.3, 0, 1, 0.4, 0, 0.5, 0, 0.6, 0, 1, 0.744, 0, 0.889, 0, 1.033, 0, 1, 1.078, 0, 1.122, 0, 1.167, 0, 1, 1.289, 0, 1.411, 0, 1.533, 0, 1, 1.989, 0, 2.444, 0, 2.9, 0, 1, 3.044, 0, 3.189, 0, 3.333, 0, 1, 3.811, 0, 4.289, 0, 4.767, 0, 1, 4.911, 0, 5.056, 0, 5.2, 0, 1, 5.333, 0, 5.467, 0, 5.6, 0, 1, 5.611, 0, 5.622, 0, 5.633, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0, 0.3, 0, 1, 0.4, 0, 0.5, 16, 0.6, 16, 1, 0.744, 16, 0.889, 16, 1.033, 16, 1, 1.078, 16, 1.122, 16, 1.167, 16, 1, 1.289, 16, 1.411, 16, 1.533, 16, 1, 2.889, 16, 4.244, 0, 5.6, 0, 1, 5.611, 0, 5.622, 0, 5.633, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0, 0.3, 0, 1, 0.4, 0, 0.5, -16, 0.6, -16, 1, 0.744, -16, 0.889, -16, 1.033, -16, 1, 1.078, -16, 1.122, -16, 1.167, -16, 1, 1.289, -16, 1.411, -16, 1.533, -16, 1, 2.889, -16, 4.244, 0, 5.6, 0, 1, 5.611, 0, 5.622, 0, 5.633, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -30, 0, 0.267, -30, 1, 0.278, -30, 0.289, -30, 0.3, -30, 1, 0.4, -30, 0.5, 30, 0.6, 30, 1, 0.744, 30, 0.889, 30, 1.033, 30, 1, 1.078, 30, 1.122, 30, 1.167, 30, 1, 1.289, 30, 1.411, 30, 1.533, 30, 1, 1.7, 30, 1.867, 30, 2.033, 30, 1, 2.167, 30, 2.3, 30, 2.433, 30, 1, 2.589, 30, 2.744, 30, 2.9, 30, 1, 3.044, 30, 3.189, 30, 3.333, 30, 1, 3.489, 30, 3.644, 30, 3.8, 30, 1, 3.978, 30, 4.156, 30, 4.333, 30, 1, 4.478, 30, 4.622, 30, 4.767, 30, 1, 4.911, 30, 5.056, 30, 5.2, 30, 1, 5.333, 30, 5.467, -30, 5.6, -30, 1, 5.611, -30, 5.622, -30, 5.633, -30, 0, 7.667, -30]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -20, 0, 0.267, -20, 1, 0.278, -20, 0.289, -20, 0.3, -20, 1, 0.4, -20, 0.5, -2, 0.6, -2, 1, 0.744, -2, 0.889, -2, 1.033, -2, 1, 1.078, -2, 1.122, -2, 1.167, -2, 1, 1.289, -2, 1.411, -2, 1.533, -2, 1, 1.7, -2, 1.867, 24.42, 2.033, 24.42, 1, 2.167, 24.42, 2.3, 24.42, 2.433, 24.42, 1, 2.589, 24.42, 2.744, -13, 2.9, -13, 1, 3.044, -13, 3.189, -13, 3.333, -13, 1, 3.489, -13, 3.644, 24.42, 3.8, 24.42, 1, 3.978, 24.42, 4.156, 24.42, 4.333, 24.42, 1, 4.478, 24.42, 4.622, -13, 4.767, -13, 1, 4.911, -13, 5.056, -13, 5.2, -13, 1, 5.333, -13, 5.467, -20, 5.6, -20, 1, 5.611, -20, 5.622, -20, 5.633, -20, 0, 7.667, -20]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.289, 1, 0.311, -1, 0.333, -1, 1, 2.078, -1, 3.822, -1, 5.567, -1, 1, 5.589, -1, 5.611, 1, 5.633, 1, 1, 5.722, 1, 5.811, 1, 5.9, 1, 0, 7.667, 1]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0.2, 0.267, 0.2, 1, 0.278, 0.2, 0.289, 0.2, 0.3, 0.2, 1, 2.067, 0.2, 3.833, 0.2, 5.6, 0.2, 1, 5.611, 0.2, 5.622, 0.2, 5.633, 0.2, 1, 5.722, 0.2, 5.811, 0, 5.9, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 1, 1, 0.089, 1, 0.178, -0.073, 0.267, -0.4, 1, 0.278, -0.441, 0.289, -0.41, 0.3, -0.41, 1, 2.067, -0.41, 3.833, -0.41, 5.6, -0.41, 1, 5.611, -0.41, 5.622, -0.441, 5.633, -0.4, 1, 5.722, -0.073, 5.811, 1, 5.9, 1, 0, 7.667, 1]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 1, 1, 0.089, 1, 0.178, -0.073, 0.267, -0.4, 1, 0.278, -0.441, 0.289, -0.41, 0.3, -0.41, 1, 2.067, -0.41, 3.833, -0.41, 5.6, -0.41, 1, 5.611, -0.41, 5.622, -0.441, 5.633, -0.4, 1, 5.722, -0.073, 5.811, 1, 5.9, 1, 0, 7.667, 1]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0.2, 0.267, 0.2, 1, 0.278, 0.2, 0.289, 0.2, 0.3, 0.2, 1, 2.067, 0.2, 3.833, 0.2, 5.6, 0.2, 1, 5.611, 0.2, 5.622, 0.2, 5.633, 0.2, 1, 5.722, 0.2, 5.811, 0, 5.9, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0.05, 0.3, 0.05, 1, 2.067, 0.05, 3.833, 0.05, 5.6, 0.05, 1, 5.611, 0.05, 5.622, 0, 5.633, 0, 1, 5.722, 0, 5.811, 0, 5.9, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.278, 0, 0.289, 0.05, 0.3, 0.05, 1, 2.067, 0.05, 3.833, 0.05, 5.6, 0.05, 1, 5.611, 0.05, 5.622, 0, 5.633, 0, 1, 5.722, 0, 5.811, 0, 5.9, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.089, 0, 0.178, -0.3, 0.267, -0.3, 1, 0.278, -0.3, 0.289, -0.3, 0.3, -0.3, 1, 2.067, -0.3, 3.833, -0.3, 5.6, -0.3, 1, 5.611, -0.3, 5.622, -0.3, 5.633, -0.3, 1, 5.722, -0.3, 5.811, 0, 5.9, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 1, 0.089, 0, 0.178, -0.3, 0.267, -0.3, 1, 0.278, -0.3, 0.289, -0.3, 0.3, -0.3, 1, 2.067, -0.3, 3.833, -0.3, 5.6, -0.3, 1, 5.611, -0.3, 5.622, -0.3, 5.633, -0.3, 1, 5.722, -0.3, 5.811, 0, 5.9, 0, 0, 7.667, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, -30, 0, 7.667, -30]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, -1, 0, 7.667, -1]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, -1, 0, 7.667, -1]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 1, 0, 7.667, 1]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, -1, 0, 7.667, -1]}]}