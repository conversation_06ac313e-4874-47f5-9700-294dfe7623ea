{"Version": 3, "Meta": {"Duration": 6.867, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 12, "TotalSegmentCount": 135, "TotalPointCount": 389, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 0.233, 0, 1, 0.333, 0, 0.433, 0.12, 0.533, 0.12, 1, 0.656, 0.12, 0.778, 0.12, 0.9, 0.12, 1, 0.978, 0.12, 1.056, 0.12, 1.133, 0.12, 1, 1.322, 0.12, 1.511, 0.15, 1.7, 0.15, 1, 1.811, 0.15, 1.922, 0.15, 2.033, 0.15, 1, 2.189, 0.15, 2.344, 0.15, 2.5, 0.15, 1, 3.211, 0.15, 3.922, 0.15, 4.633, 0.15, 1, 4.778, 0.15, 4.922, 0.15, 5.067, 0.15, 1, 5.267, 0.15, 5.467, 0.15, 5.667, 0.15, 1, 5.8, 0.15, 5.933, 0.15, 6.067, 0.15, 1, 6.222, 0.15, 6.378, 0, 6.533, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.333, 0, 0.433, 0, 0.533, 0, 1, 0.656, 0, 0.778, 0, 0.9, 0, 1, 0.978, 0, 1.056, 0, 1.133, 0, 1, 1.322, 0, 1.511, -0.15, 1.7, -0.15, 1, 1.811, -0.15, 1.922, -0.15, 2.033, -0.15, 1, 2.189, -0.15, 2.344, -0.15, 2.5, -0.15, 1, 2.7, -0.15, 2.9, 0.15, 3.1, 0.15, 1, 3.167, 0.15, 3.233, 0.15, 3.3, 0.15, 1, 3.411, 0.15, 3.522, 0.15, 3.633, 0.15, 1, 3.711, 0.15, 3.789, 0.15, 3.867, 0.15, 1, 3.944, 0.15, 4.022, 0.15, 4.1, 0.15, 1, 4.278, 0.15, 4.456, -0.15, 4.633, -0.15, 1, 4.778, -0.15, 4.922, -0.15, 5.067, -0.15, 1, 5.267, -0.15, 5.467, 0.15, 5.667, 0.15, 1, 5.8, 0.15, 5.933, 0.15, 6.067, 0.15, 1, 6.222, 0.15, 6.378, 0, 6.533, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 0, 0.233, 0, 1, 0.333, 0, 0.433, 0.12, 0.533, 0.12, 1, 0.656, 0.12, 0.778, 0.12, 0.9, 0.12, 1, 0.978, 0.12, 1.056, 0.12, 1.133, 0.12, 1, 1.322, 0.12, 1.511, 0.13, 1.7, 0.13, 1, 1.811, 0.13, 1.922, 0.13, 2.033, 0.13, 1, 2.189, 0.13, 2.344, 0.13, 2.5, 0.13, 1, 3.211, 0.13, 3.922, 0.13, 4.633, 0.13, 1, 4.778, 0.13, 4.922, 0.13, 5.067, 0.13, 1, 5.267, 0.13, 5.467, 0.15, 5.667, 0.15, 1, 5.8, 0.15, 5.933, 0.15, 6.067, 0.15, 1, 6.222, 0.15, 6.378, 0, 6.533, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.333, 0, 0.433, 0, 0.533, 0, 1, 0.656, 0, 0.778, 0, 0.9, 0, 1, 0.978, 0, 1.056, 0, 1.133, 0, 1, 1.322, 0, 1.511, -0.15, 1.7, -0.15, 1, 1.811, -0.15, 1.922, -0.15, 2.033, -0.15, 1, 2.189, -0.15, 2.344, -0.15, 2.5, -0.15, 1, 2.7, -0.15, 2.9, 0.15, 3.1, 0.15, 1, 3.167, 0.15, 3.233, 0.15, 3.3, 0.15, 1, 3.411, 0.15, 3.522, 0.15, 3.633, 0.15, 1, 3.711, 0.15, 3.789, 0.15, 3.867, 0.15, 1, 3.944, 0.15, 4.022, 0.15, 4.1, 0.15, 1, 4.278, 0.15, 4.456, -0.15, 4.633, -0.15, 1, 4.778, -0.15, 4.922, -0.15, 5.067, -0.15, 1, 5.267, -0.15, 5.467, 0.15, 5.667, 0.15, 1, 5.8, 0.15, 5.933, 0.15, 6.067, 0.15, 1, 6.222, 0.15, 6.378, 0, 6.533, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 1, 1, 0.078, 1, 0.156, 1, 0.233, 1, 0, 6.867, 1]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 1, 1, 0.078, 1, 0.156, 1, 0.233, 1, 0, 6.867, 1]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 1, 1, 0.078, 1, 0.156, 1, 0.233, 1, 0, 6.867, 1]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.289, 0, 0.344, 1, 0.4, 1, 1, 0.444, 1, 0.489, 0, 0.533, 0, 1, 0.656, 0, 0.778, 0, 0.9, 0, 1, 0.978, 0, 1.056, 0, 1.133, 0, 1, 1.2, 0, 1.267, 1, 1.333, 1, 1, 1.456, 1, 1.578, 0, 1.7, 0, 1, 1.811, 0, 1.922, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 1, 2.589, 0, 2.678, 1, 2.767, 1, 1, 2.878, 1, 2.989, 0, 3.1, 0, 1, 3.167, 0, 3.233, 0, 3.3, 0, 1, 3.411, 0, 3.522, 0, 3.633, 0, 1, 3.789, 0, 3.944, 0, 4.1, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.289, 0, 0.344, 1, 0.4, 1, 1, 0.444, 1, 0.489, 0, 0.533, 0, 1, 0.656, 0, 0.778, 0, 0.9, 0, 1, 0.978, 0, 1.056, 0, 1.133, 0, 1, 1.2, 0, 1.267, 1, 1.333, 1, 1, 1.456, 1, 1.578, 0, 1.7, 0, 1, 1.811, 0, 1.922, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 1, 2.589, 0, 2.678, 1, 2.767, 1, 1, 2.878, 1, 2.989, 0, 3.1, 0, 1, 3.167, 0, 3.233, 0, 3.3, 0, 1, 3.411, 0, 3.522, 0, 3.633, 0, 1, 3.789, 0, 3.944, 0, 4.1, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.333, 0, 0.433, 0.28, 0.533, 0.28, 1, 0.656, 0.28, 0.778, 0.28, 0.9, 0.28, 1, 0.978, 0.28, 1.056, 0.28, 1.133, 0.28, 1, 1.322, 0.28, 1.511, 0.28, 1.7, 0.28, 1, 1.811, 0.28, 1.922, 0.28, 2.033, 0.28, 1, 2.189, 0.28, 2.344, 0.28, 2.5, 0.28, 1, 2.7, 0.28, 2.9, 0.28, 3.1, 0.28, 1, 3.167, 0.28, 3.233, 0.28, 3.3, 0.28, 1, 3.411, 0.28, 3.522, 0.28, 3.633, 0.28, 1, 3.711, 0.28, 3.789, 0.28, 3.867, 0.28, 1, 3.944, 0.28, 4.022, 0.28, 4.1, 0.28, 1, 4.278, 0.28, 4.456, 0.28, 4.633, 0.28, 1, 4.778, 0.28, 4.922, 0.28, 5.067, 0.28, 1, 5.267, 0.28, 5.467, 0.28, 5.667, 0.28, 1, 5.8, 0.28, 5.933, 0.28, 6.067, 0.28, 1, 6.222, 0.28, 6.378, 0, 6.533, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.333, 0, 0.433, 0.27, 0.533, 0.27, 1, 0.656, 0.27, 0.778, 0.27, 0.9, 0.27, 1, 0.978, 0.27, 1.056, 0.27, 1.133, 0.27, 1, 1.322, 0.27, 1.511, 0.28, 1.7, 0.28, 1, 1.811, 0.28, 1.922, 0.28, 2.033, 0.28, 1, 2.189, 0.28, 2.344, 0.28, 2.5, 0.28, 1, 2.7, 0.28, 2.9, 0.28, 3.1, 0.28, 1, 3.167, 0.28, 3.233, 0.28, 3.3, 0.28, 1, 3.411, 0.28, 3.522, 0.28, 3.633, 0.28, 1, 3.711, 0.28, 3.789, 0.28, 3.867, 0.28, 1, 3.944, 0.28, 4.022, 0.28, 4.1, 0.28, 1, 4.278, 0.28, 4.456, 0.28, 4.633, 0.28, 1, 4.778, 0.28, 4.922, 0.28, 5.067, 0.28, 1, 5.267, 0.28, 5.467, 0.28, 5.667, 0.28, 1, 5.8, 0.28, 5.933, 0.28, 6.067, 0.28, 1, 6.222, 0.28, 6.378, 0, 6.533, 0, 0, 6.867, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, -1, 0, 6.867, -1]}]}