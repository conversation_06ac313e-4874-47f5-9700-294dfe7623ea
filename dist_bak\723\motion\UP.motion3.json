{"Version": 3, "Meta": {"Duration": 9.067, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 17, "TotalSegmentCount": 252, "TotalPointCount": 727, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param26", "Segments": [0, 1, 0, 9.067, 1]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 1, 0, 9.067, 1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 1.067, 0, 1, 1.189, 0, 1.311, 0, 1.433, 0, 1, 1.633, 0, 1.833, -0.5, 2.033, -0.5, 1, 2.078, -0.5, 2.122, -0.5, 2.167, -0.5, 1, 2.378, -0.5, 2.589, 0, 2.8, 0, 1, 2.889, 0, 2.978, 0, 3.067, 0, 1, 3.167, 0, 3.267, 0, 3.367, 0, 1, 3.6, 0, 3.833, 0, 4.067, 0, 1, 4.167, 0, 4.267, 0, 4.367, 0, 1, 4.656, 0, 4.944, 0, 5.233, 0, 1, 5.311, 0, 5.389, 0.008, 5.467, -0.1, 1, 5.556, -0.223, 5.644, -0.5, 5.733, -0.5, 1, 5.867, -0.5, 6, -0.5, 6.133, -0.5, 1, 6.244, -0.5, 6.356, -0.219, 6.467, -0.1, 1, 6.567, 0.008, 6.667, 0, 6.767, 0, 1, 6.9, 0, 7.033, 0, 7.167, 0, 1, 7.267, 0, 7.367, 0, 7.467, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.4, 0.167, 0.4, 1, 0.3, 0.4, 0.433, 0.4, 0.567, 0.4, 1, 0.733, 0.4, 0.9, 0.4, 1.067, 0.4, 1, 1.189, 0.4, 1.311, 0.4, 1.433, 0.4, 1, 1.533, 0.4, 1.633, -0.3, 1.733, -0.3, 1, 1.833, -0.3, 1.933, -0.1, 2.033, -0.1, 1, 2.078, -0.1, 2.122, -0.1, 2.167, -0.1, 1, 2.267, -0.1, 2.367, -0.3, 2.467, -0.3, 1, 2.578, -0.3, 2.689, 0.4, 2.8, 0.4, 1, 2.889, 0.4, 2.978, 0.4, 3.067, 0.4, 1, 3.167, 0.4, 3.267, 0.4, 3.367, 0.4, 1, 3.6, 0.4, 3.833, 0.4, 4.067, 0.4, 1, 4.167, 0.4, 4.267, 0.4, 4.367, 0.4, 1, 4.656, 0.4, 4.944, 0.4, 5.233, 0.4, 1, 5.311, 0.4, 5.389, 0.067, 5.467, -0.1, 1, 5.556, -0.291, 5.644, -0.3, 5.733, -0.3, 1, 5.867, -0.3, 6, -0.3, 6.133, -0.3, 1, 6.244, -0.3, 6.356, -0.291, 6.467, -0.1, 1, 6.567, 0.072, 6.667, 0.4, 6.767, 0.4, 1, 6.9, 0.4, 7.033, 0.4, 7.167, 0.4, 1, 7.267, 0.4, 7.367, 0.4, 7.467, 0.4, 1, 7.6, 0.4, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 1.067, 0, 1, 1.189, 0, 1.311, 0, 1.433, 0, 1, 1.633, 0, 1.833, -0.5, 2.033, -0.5, 1, 2.078, -0.5, 2.122, -0.5, 2.167, -0.5, 1, 2.378, -0.5, 2.589, 0, 2.8, 0, 1, 2.889, 0, 2.978, 0, 3.067, 0, 1, 3.167, 0, 3.267, 0, 3.367, 0, 1, 3.6, 0, 3.833, 0, 4.067, 0, 1, 4.167, 0, 4.267, 0, 4.367, 0, 1, 4.656, 0, 4.944, 0, 5.233, 0, 1, 5.311, 0, 5.389, 0.008, 5.467, -0.1, 1, 5.556, -0.223, 5.644, -0.5, 5.733, -0.5, 1, 5.867, -0.5, 6, -0.5, 6.133, -0.5, 1, 6.244, -0.5, 6.356, -0.219, 6.467, -0.1, 1, 6.567, 0.008, 6.667, 0, 6.767, 0, 1, 6.9, 0, 7.033, 0, 7.167, 0, 1, 7.267, 0, 7.367, 0, 7.467, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.4, 0.167, 0.4, 1, 0.3, 0.4, 0.433, 0.4, 0.567, 0.4, 1, 0.733, 0.4, 0.9, 0.4, 1.067, 0.4, 1, 1.189, 0.4, 1.311, 0.4, 1.433, 0.4, 1, 1.533, 0.4, 1.633, -0.3, 1.733, -0.3, 1, 1.833, -0.3, 1.933, -0.1, 2.033, -0.1, 1, 2.078, -0.1, 2.122, -0.1, 2.167, -0.1, 1, 2.267, -0.1, 2.367, -0.3, 2.467, -0.3, 1, 2.578, -0.3, 2.689, 0.4, 2.8, 0.4, 1, 2.889, 0.4, 2.978, 0.4, 3.067, 0.4, 1, 3.167, 0.4, 3.267, 0.4, 3.367, 0.4, 1, 3.6, 0.4, 3.833, 0.4, 4.067, 0.4, 1, 4.167, 0.4, 4.267, 0.4, 4.367, 0.4, 1, 4.656, 0.4, 4.944, 0.4, 5.233, 0.4, 1, 5.311, 0.4, 5.389, 0.067, 5.467, -0.1, 1, 5.556, -0.291, 5.644, -0.3, 5.733, -0.3, 1, 5.867, -0.3, 6, -0.3, 6.133, -0.3, 1, 6.244, -0.3, 6.356, -0.291, 6.467, -0.1, 1, 6.567, 0.072, 6.667, 0.4, 6.767, 0.4, 1, 6.9, 0.4, 7.033, 0.4, 7.167, 0.4, 1, 7.267, 0.4, 7.367, 0.4, 7.467, 0.4, 1, 7.6, 0.4, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.2, 0.167, -0.2, 1, 0.3, -0.2, 0.433, -0.2, 0.567, -0.2, 1, 0.733, -0.2, 0.9, -0.2, 1.067, -0.2, 1, 1.189, -0.2, 1.311, -0.2, 1.433, -0.2, 1, 1.633, -0.2, 1.833, -0.2, 2.033, -0.2, 1, 2.078, -0.2, 2.122, -0.2, 2.167, -0.2, 1, 2.378, -0.2, 2.589, -0.2, 2.8, -0.2, 1, 2.889, -0.2, 2.978, -0.2, 3.067, -0.2, 1, 3.167, -0.2, 3.267, -0.2, 3.367, -0.2, 1, 3.6, -0.2, 3.833, -0.2, 4.067, -0.2, 1, 4.167, -0.2, 4.267, -0.2, 4.367, -0.2, 1, 4.656, -0.2, 4.944, -0.2, 5.233, -0.2, 1, 5.4, -0.2, 5.567, -0.2, 5.733, -0.2, 1, 5.867, -0.2, 6, -0.2, 6.133, -0.2, 1, 6.344, -0.2, 6.556, -0.2, 6.767, -0.2, 1, 6.9, -0.2, 7.033, -0.2, 7.167, -0.2, 1, 7.267, -0.2, 7.367, -0.2, 7.467, -0.2, 1, 7.6, -0.2, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.2, 0.167, -0.2, 1, 0.3, -0.2, 0.433, -0.2, 0.567, -0.2, 1, 0.733, -0.2, 0.9, -0.2, 1.067, -0.2, 1, 1.189, -0.2, 1.311, -0.2, 1.433, -0.2, 1, 1.633, -0.2, 1.833, -0.2, 2.033, -0.2, 1, 2.078, -0.2, 2.122, -0.2, 2.167, -0.2, 1, 2.378, -0.2, 2.589, -0.2, 2.8, -0.2, 1, 2.889, -0.2, 2.978, -0.2, 3.067, -0.2, 1, 3.167, -0.2, 3.267, -0.2, 3.367, -0.2, 1, 3.6, -0.2, 3.833, -0.2, 4.067, -0.2, 1, 4.167, -0.2, 4.267, -0.2, 4.367, -0.2, 1, 4.656, -0.2, 4.944, -0.2, 5.233, -0.2, 1, 5.4, -0.2, 5.567, -0.2, 5.733, -0.2, 1, 5.867, -0.2, 6, -0.2, 6.133, -0.2, 1, 6.344, -0.2, 6.556, -0.2, 6.767, -0.2, 1, 6.9, -0.2, 7.033, -0.2, 7.167, -0.2, 1, 7.267, -0.2, 7.367, -0.2, 7.467, -0.2, 1, 7.6, -0.2, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.6, 0.167, 0.6, 1, 0.3, 0.6, 0.433, 0.6, 0.567, 0.6, 1, 0.733, 0.6, 0.9, 0.6, 1.067, 0.6, 1, 1.189, 0.6, 1.311, 0.6, 1.433, 0.6, 1, 1.633, 0.6, 1.833, 0.6, 2.033, 0.6, 1, 2.078, 0.6, 2.122, 0.6, 2.167, 0.6, 1, 2.378, 0.6, 2.589, 0.6, 2.8, 0.6, 1, 2.889, 0.6, 2.978, 0.6, 3.067, 0.6, 1, 3.167, 0.6, 3.267, 0.6, 3.367, 0.6, 1, 3.6, 0.6, 3.833, 0.6, 4.067, 0.6, 1, 4.167, 0.6, 4.267, 0.6, 4.367, 0.6, 1, 4.656, 0.6, 4.944, 0.6, 5.233, 0.6, 1, 5.4, 0.6, 5.567, 0.6, 5.733, 0.6, 1, 5.867, 0.6, 6, 0.6, 6.133, 0.6, 1, 6.344, 0.6, 6.556, 0.6, 6.767, 0.6, 1, 6.9, 0.6, 7.033, 0.6, 7.167, 0.6, 1, 7.267, 0.6, 7.367, 0.6, 7.467, 0.6, 1, 7.6, 0.6, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.6, 0.167, 0.6, 1, 0.3, 0.6, 0.433, 0.6, 0.567, 0.6, 1, 0.733, 0.6, 0.9, 0.6, 1.067, 0.6, 1, 1.189, 0.6, 1.311, 0.6, 1.433, 0.6, 1, 1.633, 0.6, 1.833, 0.6, 2.033, 0.6, 1, 2.078, 0.6, 2.122, 0.6, 2.167, 0.6, 1, 2.378, 0.6, 2.589, 0.6, 2.8, 0.6, 1, 2.889, 0.6, 2.978, 0.6, 3.067, 0.6, 1, 3.167, 0.6, 3.267, 0.6, 3.367, 0.6, 1, 3.6, 0.6, 3.833, 0.6, 4.067, 0.6, 1, 4.167, 0.6, 4.267, 0.6, 4.367, 0.6, 1, 4.656, 0.6, 4.944, 0.6, 5.233, 0.6, 1, 5.4, 0.6, 5.567, 0.6, 5.733, 0.6, 1, 5.867, 0.6, 6, 0.6, 6.133, 0.6, 1, 6.344, 0.6, 6.556, 0.6, 6.767, 0.6, 1, 6.9, 0.6, 7.033, 0.6, 7.167, 0.6, 1, 7.267, 0.6, 7.367, 0.6, 7.467, 0.6, 1, 7.6, 0.6, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 1.067, 0, 1, 1.189, 0, 1.311, 0, 1.433, 0, 1, 1.633, 0, 1.833, -0.2, 2.033, -0.2, 1, 2.078, -0.2, 2.122, -0.2, 2.167, -0.2, 1, 2.378, -0.2, 2.589, -0.2, 2.8, -0.2, 1, 2.889, -0.2, 2.978, -0.2, 3.067, -0.2, 1, 3.167, -0.2, 3.267, -0.2, 3.367, -0.2, 1, 3.6, -0.2, 3.833, -0.2, 4.067, -0.2, 1, 4.167, -0.2, 4.267, 0.2, 4.367, 0.2, 1, 4.656, 0.2, 4.944, 0.2, 5.233, 0.2, 1, 5.4, 0.2, 5.567, 0.2, 5.733, 0.2, 1, 5.867, 0.2, 6, 0.2, 6.133, 0.2, 1, 6.344, 0.2, 6.556, 0.2, 6.767, 0.2, 1, 6.9, 0.2, 7.033, 0.2, 7.167, 0.2, 1, 7.267, 0.2, 7.367, 0.2, 7.467, 0.2, 1, 7.6, 0.2, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 1.067, 0, 1, 1.189, 0, 1.311, 0, 1.433, 0, 1, 1.633, 0, 1.833, -0.2, 2.033, -0.2, 1, 2.078, -0.2, 2.122, -0.2, 2.167, -0.2, 1, 2.378, -0.2, 2.589, -0.2, 2.8, -0.2, 1, 2.889, -0.2, 2.978, -0.2, 3.067, -0.2, 1, 3.167, -0.2, 3.267, -0.2, 3.367, -0.2, 1, 3.6, -0.2, 3.833, -0.2, 4.067, -0.2, 1, 4.167, -0.2, 4.267, 0.2, 4.367, 0.2, 1, 4.656, 0.2, 4.944, 0.2, 5.233, 0.2, 1, 5.4, 0.2, 5.567, 0.2, 5.733, 0.2, 1, 5.867, 0.2, 6, 0.2, 6.133, 0.2, 1, 6.344, 0.2, 6.556, 0.2, 6.767, 0.2, 1, 6.9, 0.2, 7.033, 0.2, 7.167, 0.2, 1, 7.267, 0.2, 7.367, 0.2, 7.467, 0.2, 1, 7.6, 0.2, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 0, 0.567, 0, 1, 0.656, 0, 0.744, 1, 0.833, 1, 1, 0.911, 1, 0.989, 0, 1.067, 0, 1, 1.189, 0, 1.311, 0, 1.433, 0, 1, 1.633, 0, 1.833, 0, 2.033, 0, 1, 2.078, 0, 2.122, 0, 2.167, 0, 1, 2.378, 0, 2.589, 0, 2.8, 0, 1, 2.889, 0, 2.978, 0, 3.067, 0, 1, 3.1, 0, 3.133, 1, 3.167, 1, 1, 3.233, 1, 3.3, 0, 3.367, 0, 1, 3.6, 0, 3.833, 0, 4.067, 0, 1, 4.167, 0, 4.267, 0, 4.367, 0, 1, 4.656, 0, 4.944, 0, 5.233, 0, 1, 5.4, 0, 5.567, 0, 5.733, 0, 1, 5.867, 0, 6, 0, 6.133, 0, 1, 6.244, 0, 6.356, 0, 6.467, 0, 1, 6.567, 0, 6.667, 0, 6.767, 0, 1, 6.833, 0, 6.9, 1, 6.967, 1, 1, 7.033, 1, 7.1, 0, 7.167, 0, 1, 7.267, 0, 7.367, 0, 7.467, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 0.567, 0, 1, 0.656, 0, 0.744, 1, 0.833, 1, 1, 0.911, 1, 0.989, 0, 1.067, 0, 1, 1.189, 0, 1.311, 0, 1.433, 0, 1, 1.633, 0, 1.833, 0, 2.033, 0, 1, 2.078, 0, 2.122, 0, 2.167, 0, 1, 2.378, 0, 2.589, 0, 2.8, 0, 1, 2.889, 0, 2.978, 0, 3.067, 0, 1, 3.1, 0, 3.133, 1, 3.167, 1, 1, 3.233, 1, 3.3, 0, 3.367, 0, 1, 3.6, 0, 3.833, 0, 4.067, 0, 1, 4.167, 0, 4.267, 0, 4.367, 0, 1, 4.656, 0, 4.944, 0, 5.233, 0, 1, 5.4, 0, 5.567, 0, 5.733, 0, 1, 5.867, 0, 6, 0, 6.133, 0, 1, 6.244, 0, 6.356, 0, 6.467, 0, 1, 6.567, 0, 6.667, 0, 6.767, 0, 1, 6.833, 0, 6.9, 1, 6.967, 1, 1, 7.033, 1, 7.1, 0, 7.167, 0, 1, 7.267, 0, 7.367, 0, 7.467, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.2, 0.167, -0.2, 1, 2.5, -0.2, 4.833, -0.2, 7.167, -0.2, 1, 7.267, -0.2, 7.367, -0.2, 7.467, -0.2, 1, 7.6, -0.2, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.2, 0.167, -0.2, 1, 2.5, -0.2, 4.833, -0.2, 7.167, -0.2, 1, 7.267, -0.2, 7.367, -0.2, 7.467, -0.2, 1, 7.6, -0.2, 7.733, 0, 7.867, 0, 0, 9.067, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.2, 0.167, -0.2, 1, 0.3, -0.2, 0.433, -0.2, 0.567, -0.2, 1, 2.767, -0.2, 4.967, -0.2, 7.167, -0.2, 1, 7.267, -0.2, 7.367, -0.2, 7.467, -0.2, 1, 7.6, -0.2, 7.733, 0, 7.867, 0, 0, 9.067, 0]}]}