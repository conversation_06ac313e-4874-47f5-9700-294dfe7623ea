{"Version": 3, "Meta": {"Duration": 5.2, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 18, "TotalSegmentCount": 134, "TotalPointCount": 384, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param26", "Segments": [0, 1, 0, 5.2, 1]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 1, 0, 5.2, 1]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.389, 0, 0.644, 0, 0.9, 0, 1, 1.856, 0, 2.811, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.389, 0, 0.644, 0, 0.9, 0, 1, 1.856, 0, 2.811, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.389, 0, 0.644, 0, 0.9, 0, 1, 1.022, 0, 1.144, -0.3, 1.267, -0.3, 1, 1.467, -0.3, 1.667, -0.3, 1.867, -0.3, 1, 2, -0.3, 2.133, 0.3, 2.267, 0.3, 1, 2.489, 0.3, 2.711, 0.3, 2.933, 0.3, 1, 3.211, 0.3, 3.489, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.389, 0, 0.644, 0, 0.9, 0, 1, 1.022, 0, 1.144, -0.3, 1.267, -0.3, 1, 1.467, -0.3, 1.667, -0.3, 1.867, -0.3, 1, 2, -0.3, 2.133, 0.3, 2.267, 0.3, 1, 2.489, 0.3, 2.711, 0.3, 2.933, 0.3, 1, 3.211, 0.3, 3.489, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.6, 0.133, 0.6, 1, 0.389, 0.6, 0.644, 0.6, 0.9, 0.6, 1, 1.022, 0.6, 1.144, 0.6, 1.267, 0.6, 1, 1.467, 0.6, 1.667, 0.6, 1.867, 0.6, 1, 2, 0.6, 2.133, 0.6, 2.267, 0.6, 1, 2.489, 0.6, 2.711, 0.6, 2.933, 0.6, 1, 3.211, 0.6, 3.489, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.6, 0.133, 0.6, 1, 0.389, 0.6, 0.644, 0.6, 0.9, 0.6, 1, 1.022, 0.6, 1.144, 0.6, 1.267, 0.6, 1, 1.467, 0.6, 1.667, 0.6, 1.867, 0.6, 1, 2, 0.6, 2.133, 0.6, 2.267, 0.6, 1, 2.489, 0.6, 2.711, 0.6, 2.933, 0.6, 1, 3.211, 0.6, 3.489, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.044, 0, 0.089, -0.2, 0.133, -0.2, 1, 0.389, -0.2, 0.644, -0.2, 0.9, -0.2, 1, 1.022, -0.2, 1.144, -0.2, 1.267, -0.2, 1, 1.467, -0.2, 1.667, -0.2, 1.867, -0.2, 1, 2, -0.2, 2.133, -0.2, 2.267, -0.2, 1, 2.489, -0.2, 2.711, -0.2, 2.933, -0.2, 1, 3.211, -0.2, 3.489, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.044, 0, 0.089, -0.2, 0.133, -0.2, 1, 0.389, -0.2, 0.644, -0.2, 0.9, -0.2, 1, 1.022, -0.2, 1.144, -0.2, 1.267, -0.2, 1, 1.467, -0.2, 1.667, -0.2, 1.867, -0.2, 1, 2, -0.2, 2.133, -0.2, 2.267, -0.2, 1, 2.489, -0.2, 2.711, -0.2, 2.933, -0.2, 1, 3.211, -0.2, 3.489, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.044, 0, 0.089, -0.036, 0.133, 0.1, 1, 0.211, 0.339, 0.289, 1, 0.367, 1, 1, 0.456, 1, 0.544, 0.1, 0.633, 0.1, 1, 0.722, 0.1, 0.811, 0.1, 0.9, 0.1, 1, 1.022, 0.1, 1.144, -0.2, 1.267, -0.2, 1, 1.467, -0.2, 1.667, -0.2, 1.867, -0.2, 1, 2, -0.2, 2.133, 0.1, 2.267, 0.1, 1, 2.489, 0.1, 2.711, 0.1, 2.933, 0.1, 1, 3.011, 0.1, 3.089, 1, 3.167, 1, 1, 3.367, 1, 3.567, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.044, 0, 0.089, -0.036, 0.133, 0.1, 1, 0.211, 0.339, 0.289, 1, 0.367, 1, 1, 0.456, 1, 0.544, 0.1, 0.633, 0.1, 1, 0.722, 0.1, 0.811, 0.1, 0.9, 0.1, 1, 1.022, 0.1, 1.144, -0.2, 1.267, -0.2, 1, 1.467, -0.2, 1.667, -0.2, 1.867, -0.2, 1, 2, -0.2, 2.133, 0.1, 2.267, 0.1, 1, 2.489, 0.1, 2.711, 0.1, 2.933, 0.1, 1, 3.011, 0.1, 3.089, 1, 3.167, 1, 1, 3.367, 1, 3.567, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.389, 0, 0.644, 0, 0.9, 0, 1, 1.022, 0, 1.144, 0, 1.267, 0, 1, 1.467, 0, 1.667, 0, 1.867, 0, 1, 2, 0, 2.133, 0, 2.267, 0, 1, 2.489, 0, 2.711, 0, 2.933, 0, 1, 3.011, 0, 3.089, 0, 3.167, 0, 1, 3.367, 0, 3.567, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.389, 0, 0.644, 0, 0.9, 0, 1, 1.022, 0, 1.144, 0, 1.267, 0, 1, 1.467, 0, 1.667, 0, 1.867, 0, 1, 2, 0, 2.133, 0, 2.267, 0, 1, 2.489, 0, 2.711, 0, 2.933, 0, 1, 3.011, 0, 3.089, 0, 3.167, 0, 1, 3.367, 0, 3.567, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.044, 0, 0.089, -0.25, 0.133, -0.25, 1, 0.389, -0.25, 0.644, -0.25, 0.9, -0.25, 1, 1.022, -0.25, 1.144, -0.25, 1.267, -0.25, 1, 1.467, -0.25, 1.667, -0.25, 1.867, -0.25, 1, 2, -0.25, 2.133, -0.25, 2.267, -0.25, 1, 2.489, -0.25, 2.711, -0.25, 2.933, -0.25, 1, 3.011, -0.25, 3.089, -0.25, 3.167, -0.25, 1, 3.367, -0.25, 3.567, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.4, 0.133, 0.4, 1, 0.389, 0.4, 0.644, 0.4, 0.9, 0.4, 1, 1.022, 0.4, 1.144, 0.4, 1.267, 0.4, 1, 1.467, 0.4, 1.667, 0.4, 1.867, 0.4, 1, 2, 0.4, 2.133, 0.4, 2.267, 0.4, 1, 2.489, 0.4, 2.711, 0.4, 2.933, 0.4, 1, 3.011, 0.4, 3.089, 0.4, 3.167, 0.4, 1, 3.367, 0.4, 3.567, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.044, 0, 0.089, -0.35, 0.133, -0.35, 1, 0.389, -0.35, 0.644, -0.35, 0.9, -0.35, 1, 1.022, -0.35, 1.144, -0.35, 1.267, -0.35, 1, 1.467, -0.35, 1.667, -0.35, 1.867, -0.35, 1, 2, -0.35, 2.133, -0.35, 2.267, -0.35, 1, 2.489, -0.35, 2.711, -0.35, 2.933, -0.35, 1, 3.011, -0.35, 3.089, -0.35, 3.167, -0.35, 1, 3.367, -0.35, 3.567, 0, 3.767, 0, 0, 5.2, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.389, 0, 0.644, 0, 0.9, 0, 1, 1.022, 0, 1.144, 0, 1.267, 0, 1, 1.467, 0, 1.667, 0, 1.867, 0, 1, 2, 0, 2.133, 0, 2.267, 0, 1, 2.489, 0, 2.711, 0, 2.933, 0, 1, 3.011, 0, 3.089, 0, 3.167, 0, 1, 3.367, 0, 3.567, 0, 3.767, 0, 0, 5.2, 0]}]}