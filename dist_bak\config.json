{"currentModel": "tororo", "models": {"greenlive2d": {"name": "绿色Live2D", "path": "./lvse/green.model3.json", "description": "绿色主题模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "xm": {"name": "XM模型", "path": "./xm/XM.model3.json", "description": "XM主题模型", "defaultMotionGroup": "center", "backgroundColor": "#ffffff"}, "yuan721": {"name": "721圆眼", "path": "./721/721_YUANyan.model3.json", "description": "721圆眼模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "pink_eyes": {"name": "粉色眼睛", "path": "./pink_eyes/Pink_eyes_mod.model3.json", "description": "粉色眼睛模型", "defaultMotionGroup": "sleep", "backgroundColor": "#000000"}, "    ": {"name": "723红眼", "path": "./723/REDeyes.model3.json", "description": "723红眼模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "lanse": {"name": "蓝眼睛", "path": "./lanse/lanse.model3.json", "description": "蓝眼睛模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "huiyan": {"name": "灰眼睛", "path": "./huiyan/huiyan.model3.json", "description": "灰眼睛模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "725_test": {"name": "725模型", "path": "./725_test/LY723.model3.json", "description": "725模型", "defaultMotionGroup": "center", "backgroundColor": "#000000"}, "tororo": {"name": "tororo模型", "path": "./tororo/runtime/tororo.model3.json", "description": "tororo模型", "defaultMotionGroup": "Idle", "backgroundColor": "#ffffff"}}, "settings": {"defaultMotionGroup": "center", "defaultBackgroundColor": "#000000", "autoCache": false, "enableWebSocket": true, "wsUrl": "ws://localhost:8002"}}