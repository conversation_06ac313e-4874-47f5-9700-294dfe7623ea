{"Version": 3, "Meta": {"Duration": 4.4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 20, "TotalSegmentCount": 153, "TotalPointCount": 431, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 1.278, 0, 2.056, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 1.278, 0, 2.056, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 1.278, 0, 2.056, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 1.278, 0, 2.056, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.4, 0.2, -0.4, 1, 0.3, -0.4, 0.4, -0.4, 0.5, -0.4, 1, 1.278, -0.4, 2.056, -0.4, 2.833, -0.4, 1, 3.033, -0.4, 3.233, -0.4, 3.433, -0.4, 1, 3.611, -0.4, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0.25, 0.2, 0.25, 1, 0.3, 0.25, 0.4, 0.25, 0.5, 0.25, 1, 1.278, 0.25, 2.056, 0.25, 2.833, 0.25, 1, 3.033, 0.25, 3.233, 0.25, 3.433, 0.25, 1, 3.611, 0.25, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0, 1.667, 0, 1, 2.056, 0, 2.444, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0, 1.667, 0, 1, 2.056, 0, 2.444, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0.1, 1.667, 0.1, 1, 1.756, 0.1, 1.844, 0.089, 1.933, 0, 1, 1.978, -0.044, 2.022, -0.2, 2.067, -0.2, 1, 2.111, -0.2, 2.156, 0, 2.2, 0, 1, 2.244, 0, 2.289, -0.2, 2.333, -0.2, 1, 2.389, -0.2, 2.444, 0, 2.5, 0, 1, 2.544, 0, 2.589, -0.2, 2.633, -0.2, 1, 2.7, -0.2, 2.767, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0.1, 1.667, 0.1, 1, 1.756, 0.1, 1.844, 0.089, 1.933, 0, 1, 1.978, -0.044, 2.022, -0.2, 2.067, -0.2, 1, 2.111, -0.2, 2.156, 0, 2.2, 0, 1, 2.244, 0, 2.289, -0.2, 2.333, -0.2, 1, 2.389, -0.2, 2.444, 0, 2.5, 0, 1, 2.544, 0, 2.589, -0.2, 2.633, -0.2, 1, 2.7, -0.2, 2.767, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, 1, 0.767, 1, 1, 0.844, 1, 0.922, -0.4, 1, -0.4, 1, 1.078, -0.4, 1.156, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0, 1.667, 0, 1, 2.433, 0, 3.2, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, 1, 0.767, 1, 1, 0.844, 1, 0.922, -0.4, 1, -0.4, 1, 1.078, -0.4, 1.156, 0, 1.233, 0, 1, 2.144, 0, 3.056, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0.1, 1.667, 0.1, 1, 1.756, 0.1, 1.844, 0.089, 1.933, 0, 1, 1.978, -0.044, 2.022, -0.2, 2.067, -0.2, 1, 2.111, -0.2, 2.156, 0, 2.2, 0, 1, 2.244, 0, 2.289, -0.2, 2.333, -0.2, 1, 2.389, -0.2, 2.444, 0, 2.5, 0, 1, 2.544, 0, 2.589, -0.2, 2.633, -0.2, 1, 2.7, -0.2, 2.767, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0.1, 1.667, 0.1, 1, 1.756, 0.1, 1.844, 0.089, 1.933, 0, 1, 1.978, -0.044, 2.022, -0.2, 2.067, -0.2, 1, 2.111, -0.2, 2.156, 0, 2.2, 0, 1, 2.244, 0, 2.289, -0.2, 2.333, -0.2, 1, 2.389, -0.2, 2.444, 0, 2.5, 0, 1, 2.544, 0, 2.589, -0.2, 2.633, -0.2, 1, 2.7, -0.2, 2.767, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0, 1.667, 0, 1, 2.056, 0, 2.444, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.25, 0.2, -0.25, 1, 0.3, -0.25, 0.4, -0.25, 0.5, -0.25, 1, 1.278, -0.25, 2.056, -0.25, 2.833, -0.25, 1, 3.033, -0.25, 3.233, -0.25, 3.433, -0.25, 1, 3.611, -0.25, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.45, 0.2, -0.45, 1, 0.3, -0.45, 0.4, -0.45, 0.5, -0.45, 1, 1.278, -0.45, 2.056, -0.45, 2.833, -0.45, 1, 3.033, -0.45, 3.233, -0.45, 3.433, -0.45, 1, 3.611, -0.45, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0, 1.667, 0, 1, 2.056, 0, 2.444, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.3, 0, 0.4, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0, 1.667, 0, 1, 2.056, 0, 2.444, 0, 2.833, 0, 1, 3.033, 0, 3.233, 0, 3.433, 0, 1, 3.611, 0, 3.789, 0, 3.967, 0, 0, 4.4, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.3, 0.2, -0.3, 1, 0.3, -0.3, 0.4, -0.3, 0.5, -0.3, 1, 1.278, -0.3, 2.056, -0.3, 2.833, -0.3, 1, 3.033, -0.3, 3.233, -0.3, 3.433, -0.3, 1, 3.611, -0.3, 3.789, 0, 3.967, 0, 0, 4.4, 0]}]}