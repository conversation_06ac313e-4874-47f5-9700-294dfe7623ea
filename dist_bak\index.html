<!doctype html><html lang=""><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"><meta http-equiv="Pragma" content="no-cache"><meta http-equiv="Expires" content="0"><link rel="icon" href="favicon.ico?v=1753151662453"><title>live2d</title><script>// 禁用所有缓存
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(function(registrations) {
          for(let registration of registrations) {
            registration.unregister();
          }
        });
      }
      
      // 重写fetch以添加缓存控制
      const originalFetch = window.fetch;
      window.fetch = function(...args) {
        if (args[1]) {
          args[1].headers = {
            ...args[1].headers,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          };
        } else {
          args[1] = {
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          };
        }
        return originalFetch.apply(this, args);
      };</script><script defer="defer" src="js/chunk-vendors.e104aaf2.js"></script><script defer="defer" src="js/app.0f641d9c.js"></script><link href="css/app.5bb1cb97.css" rel="stylesheet"></head><body><noscript><strong>We're sorry but live2d doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div><script src="live2dcubismcore.min.js?v=1753151662453"></script></body><style>body {
      margin: 0;
      padding: 0;
      overflow: hidden;
    }</style></html>