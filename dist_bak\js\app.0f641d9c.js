(function(){var o={2634:function(){},9596:function(o,t,e){"use strict";var i=e(6848),n=function(){var o=this,t=o._self._c;return t("div",{staticClass:"live2d-container",style:{backgroundColor:o.currentBackgroundColor}},[t("canvas",{ref:"liveCanvas"})])},s=[],l=(e(8111),e(7588),e(9968)),a=e(3188);window.PIXI=l;var r={data(){return{app:null,model:null,currentModel:"greenlive2d",availableMotions:{},modelPaths:{},config:null,currentBackgroundColor:"#000000",socket:null,isfirst:!0,wsStatus:"disconnected",wsUrl:"ws://localhost:8002",autoPlayInterval:null,showStatus:!1,currentMotionGroupIndex:0,defaultMotionGroup:"center",loopMotionGroup:"center",_returnToDefaultTimer:null,defaultMotionInterval:null,lastMotionTime:null}},async mounted(){await this.loadConfig(),this.initPixiApp(),this.loadModel(this.currentModel).then(()=>{if(this.model&&this.model.internalModel){console.log("添加全局动作监听");const o=["motionFinished","motionEnd","finished","complete"];o.forEach(o=>{this.model.on(o,()=>{console.log(`事件触发: ${o}`),this.handleMotionFinished()}),this.model.internalModel.motionManager&&this.model.internalModel.motionManager.on(o,()=>{console.log(`内部事件触发: ${o}`),this.handleMotionFinished()})})}}),window.addEventListener("resize",this.handleResize),window.addEventListener("keydown",this.handleKeyDown),this.connectWebSocket()},methods:{initPixiApp(){this.app=new l.Application({view:this.$refs.liveCanvas,autoStart:!0,resizeTo:window,backgroundAlpha:0})},async loadModel(o){this.model&&(this.app.stage.removeChild(this.model),this.model.destroy(),this.model=null,this.stopDefaultMotionLoop());try{const t=this.modelPaths[o]+(this.modelPaths[o].includes("?")?"&":"?")+`t=${Date.now()}`;this.model=await a.c0.from(t),this.app.stage.addChild(this.model),this.fitModelToScreen(),this.model.draggable=!1,this.availableMotions=this.model.internalModel.settings.motions,this.availableMotions[this.defaultMotionGroup]&&(this.playRandomMotion(this.defaultMotionGroup),this.startDefaultMotionLoop()),console.log(`模型 ${o} 加载成功`)}catch(t){console.error("模型加载失败:",t)}},fitModelToScreen(){if(!this.model)return;const o=this.app.renderer.width,t=this.app.renderer.height,e=this.model.width,i=this.model.height,n=o/e,s=t/i,l=.9*Math.min(n,s);this.model.scale.set(l),this.model.x=o/2,this.model.y=t/2,this.model.anchor.set(.5,.5),console.log(`模型适配屏幕: 缩放=${l}, 位置=(${this.model.x}, ${this.model.y})`)},handleResize(){this.model&&this.fitModelToScreen()},playMotion(o,t){if(!this.model||!this.availableMotions[o])return;const e=this.model.internalModel.motionManager.group,i=this.model.internalModel.motionManager.index;e!==o||i!==t?(o!==this.defaultMotionGroup&&(this.lastMotionTime=Date.now()),this.model.once("motionFinished",()=>{console.log(`动作 ${o}[${t}] 完成`),this.handleMotionFinished()}),"left"===o?this.model.motion("right",t,3,!0,.8):"right"===o?this.model.motion("left",t,3,!0,.8):this.model.motion(o,t,3,!0,.8),this.ensureReturnToDefaultMotion()):console.log(`已经在播放动作: ${o}[${t}]，跳过`)},playRandomMotion(o){if(!this.model||!this.availableMotions[o])return;const t=this.availableMotions[o],e=Math.floor(Math.random()*t.length),i=this.model.internalModel.motionManager.group,n=this.model.internalModel.motionManager.index;i!==o||n!==e?o!==this.defaultMotionGroup&&o!==this.loopMotionGroup?(this.lastMotionTime=Date.now(),this.model.motion(o,e,3,!0,.8),this.model.once("motionFinished",()=>{console.log(`动作 ${o}[${e}] 完成，切换到默认动作`),this.availableMotions[this.defaultMotionGroup]&&this.model.motion(this.defaultMotionGroup,0,1,!1,1.5)})):this.model.motion(o,e,3,!0,.8):console.log(`已经在播放动作: ${o}[${e}]，跳过`)},ensureReturnToDefaultMotion(){this._returnToDefaultTimer&&clearTimeout(this._returnToDefaultTimer),this._returnToDefaultTimer=setTimeout(()=>{if(this.model&&this.model.internalModel&&this.model.internalModel.motionManager.isFinished()){console.log("动作已完成（通过定时器检测）");const o=this.model.internalModel.motionManager.group,t=this.model.internalModel.motionManager.index;this.autoPlayInterval||!this.availableMotions[this.defaultMotionGroup]||o===this.defaultMotionGroup&&0===t||(console.log("切换到默认动作"),this.model.motion(this.defaultMotionGroup,0,1,!1,1.5),this.lastMotionTime=Date.now())}else this.ensureReturnToDefaultMotion()},2e3)},startDefaultMotionLoop(){this.stopDefaultMotionLoop();const o=1500,t=2e4;this.lastMotionTime||(this.lastMotionTime=Date.now()),this.defaultMotionInterval=setInterval(()=>{const o=Date.now();if(this.model&&this.model.internalModel&&this.model.internalModel.motionManager&&this.model.internalModel.motionManager.isFinished()){const e=this.model.internalModel.motionManager.group,i=o-this.lastMotionTime;i>=t?e&&e!==this.defaultMotionGroup||(this.model.motion(this.defaultMotionGroup,0,1,!1,.5),this.lastMotionTime=o):e&&e!==this.defaultMotionGroup||(console.log("循环播放默认动作"),this.model.motion(this.defaultMotionGroup,0,1,!1,.5))}},o)},stopDefaultMotionLoop(){this.defaultMotionInterval&&(clearInterval(this.defaultMotionInterval),this.defaultMotionInterval=null,console.log("停止默认动作循环"))},async switchModel(o){if(this.currentModel===o)return;this.currentModel=o;const t=this.config.models[o];this.defaultMotionGroup=t?.defaultMotionGroup||this.config.settings.defaultMotionGroup,this.currentBackgroundColor=t?.backgroundColor||this.config.settings.defaultBackgroundColor,console.log(`切换到模型: ${o}, 默认动作组: ${this.defaultMotionGroup}, 背景色: ${this.currentBackgroundColor}`),await this.loadModel(o)},async playMotionSequence(o){if(this.model){for(const t of o)await new Promise(o=>{this.model.once("motionFinished",o),"string"===typeof t?this.playRandomMotion(t):this.playMotion(t.group,t.index)});!this.autoPlayInterval&&this.availableMotions[this.defaultMotionGroup]&&this.model.motion(this.defaultMotionGroup,0,1,!1)}},startAutoPlay(){this.stopAutoPlay();const o=Object.keys(this.availableMotions);if(o.length>0){const t=o[Math.floor(Math.random()*o.length)];this.playRandomMotion(t)}"connected"!==this.wsStatus&&(this.autoPlayInterval=setInterval(()=>{const o=Object.keys(this.availableMotions);if(o.length>0){const t=o[Math.floor(Math.random()*o.length)];this.playRandomMotion(t)}},4e3))},stopAutoPlay(){this.autoPlayInterval&&(clearInterval(this.autoPlayInterval),this.autoPlayInterval=null)},connectWebSocket(){if(!this.socket||this.socket.readyState!==WebSocket.OPEN&&this.socket.readyState!==WebSocket.CONNECTING){this.wsStatus="connecting";try{this.socket=new WebSocket(this.wsUrl),this.socket.onopen=()=>{console.log("WebSocket连接成功"),this.wsStatus="connected",console.log("WebSocket已连接，等待接收消息..."),this.stopAutoPlay()},this.socket.onmessage=o=>{this.handleWebSocketMessage(o.data)},this.socket.onerror=o=>{console.error("WebSocket错误:",o),this.wsStatus="error"},this.socket.onclose=()=>{console.log("WebSocket连接关闭"),this.wsStatus="disconnected",setTimeout(()=>{this.connectWebSocket()},5e3)}}catch(o){console.error("WebSocket连接失败:",o),this.wsStatus="error"}}else console.log("WebSocket已连接或正在连接中")},sendWebSocketMessage(o){this.socket&&this.socket.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(o)):console.warn("WebSocket未连接，无法发送消息")},handleWebSocketMessage(o){try{const t=JSON.parse(o);switch(console.log("收到WebSocket消息:",t),t.type){case"playMotion":t.group&&void 0!==t.index&&this.playMotion(t.group,t.index);break;case"playRandomMotion":t.group&&this.playRandomMotion(t.group);break;case"playSequence":t.sequence&&Array.isArray(t.sequence)&&this.playMotionSequence(t.sequence);break;case"switchModel":t.model&&this.modelPaths[t.model]&&this.switchModel(t.model);break;default:console.warn("未知的WebSocket消息类型:",t.type)}}catch(t){console.error("处理WebSocket消息失败:",t)}},toggleStatusVisibility(){this.showStatus=!this.showStatus},handleKeyDown(o){"Space"===o.code&&this.playNextMotionGroup()},playNextMotionGroup(){const o=Object.keys(this.availableMotions);if(0===o.length)return;this.currentMotionGroupIndex=(this.currentMotionGroupIndex+1)%o.length;const t=o[this.currentMotionGroupIndex];console.log(`当前动作组: ${t}`,this.isfirst),this.playRandomMotion(t),console.log(`切换到动作组: ${t}`)},handleMotionFinished(){if(console.log("动作完成处理函数被调用"),!this.model||!this.model.internalModel||!this.model.internalModel.motionManager)return;const o=this.model.internalModel.motionManager.group,t=this.model.internalModel.motionManager.index;this.autoPlayInterval||!this.availableMotions[this.defaultMotionGroup]||o===this.defaultMotionGroup&&0===t||(console.log(`动作完成，重置到默认动作: ${this.defaultMotionGroup}`),this.model.motion(this.defaultMotionGroup,0,1,!1,1.5),this.resetModelPosition())},resetModelPosition(){this.model&&this.model.internalModel&&(this.fitModelToScreen(),console.log("模型已重置到默认位置"))},async loadConfig(){try{const o=await fetch(`./config.json?t=${Date.now()}`);this.config=await o.json(),this.currentModel=this.config.currentModel,this.modelPaths={},Object.keys(this.config.models).forEach(o=>{const t=this.config.models[o];this.modelPaths[o]=this.config.settings.autoCache?t.path:`${t.path}?t=${Date.now()}`});const t=this.config.models[this.currentModel];this.defaultMotionGroup=t?.defaultMotionGroup||this.config.settings.defaultMotionGroup,this.currentBackgroundColor=t?.backgroundColor||this.config.settings.defaultBackgroundColor,this.wsUrl=this.config.settings.wsUrl,console.log("配置加载成功:",this.config),console.log("当前模型:",this.currentModel),console.log("默认动作组:",this.defaultMotionGroup),console.log("背景色:",this.currentBackgroundColor)}catch(o){console.error("配置加载失败，使用默认配置:",o),this.currentModel="greenlive2d",this.defaultMotionGroup="center",this.currentBackgroundColor="#000000",this.modelPaths={greenlive2d:`./lvse/green.model3.json?t=${Date.now()}`}}}},beforeUnmount(){this.model&&this.model.destroy(),this.app&&this.app.destroy(!0),this.stopAutoPlay(),this.stopDefaultMotionLoop(),this._returnToDefaultTimer&&clearTimeout(this._returnToDefaultTimer),this.socket&&(this.socket.close(),this.socket=null),window.removeEventListener("resize",this.handleResize),window.removeEventListener("keydown",this.handleKeyDown)}},h=r,d=e(1656),c=(0,d.A)(h,n,s,!1,null,"8d46e566",null),u=c.exports;i.Ay.config.productionTip=!1,new i.Ay({render:o=>o(u)}).$mount("#app")}},t={};function e(i){var n=t[i];if(void 0!==n)return n.exports;var s=t[i]={id:i,loaded:!1,exports:{}};return o[i].call(s.exports,s,s.exports,e),s.loaded=!0,s.exports}e.m=o,function(){var o=[];e.O=function(t,i,n,s){if(!i){var l=1/0;for(d=0;d<o.length;d++){i=o[d][0],n=o[d][1],s=o[d][2];for(var a=!0,r=0;r<i.length;r++)(!1&s||l>=s)&&Object.keys(e.O).every(function(o){return e.O[o](i[r])})?i.splice(r--,1):(a=!1,s<l&&(l=s));if(a){o.splice(d--,1);var h=n();void 0!==h&&(t=h)}}return t}s=s||0;for(var d=o.length;d>0&&o[d-1][2]>s;d--)o[d]=o[d-1];o[d]=[i,n,s]}}(),function(){e.n=function(o){var t=o&&o.__esModule?function(){return o["default"]}:function(){return o};return e.d(t,{a:t}),t}}(),function(){e.d=function(o,t){for(var i in t)e.o(t,i)&&!e.o(o,i)&&Object.defineProperty(o,i,{enumerable:!0,get:t[i]})}}(),function(){e.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(o){if("object"===typeof window)return window}}()}(),function(){e.o=function(o,t){return Object.prototype.hasOwnProperty.call(o,t)}}(),function(){e.r=function(o){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(o,"__esModule",{value:!0})}}(),function(){e.nmd=function(o){return o.paths=[],o.children||(o.children=[]),o}}(),function(){var o={524:0};e.O.j=function(t){return 0===o[t]};var t=function(t,i){var n,s,l=i[0],a=i[1],r=i[2],h=0;if(l.some(function(t){return 0!==o[t]})){for(n in a)e.o(a,n)&&(e.m[n]=a[n]);if(r)var d=r(e)}for(t&&t(i);h<l.length;h++)s=l[h],e.o(o,s)&&o[s]&&o[s][0](),o[s]=0;return e.O(d)},i=self["webpackChunklive2d"]=self["webpackChunklive2d"]||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))}();var i=e.O(void 0,[504],function(){return e(9596)});i=e.O(i)})();
//# sourceMappingURL=app.0f641d9c.js.map