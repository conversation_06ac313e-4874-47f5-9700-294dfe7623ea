{"version": 3, "file": "js/app.0f641d9c.js", "mappings": "qFAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmBC,MAAO,CAAEC,gBAAiBN,EAAIO,yBAA2B,CAACL,EAAG,SAAS,CAACM,IAAI,gBACvL,EACIC,EAAkB,G,sCCGtBC,OAAAC,KAAAA,EAEA,OACAC,IAAAA,GACA,OACAC,IAAA,KACAC,MAAA,KACAC,aAAA,cACAC,iBAAA,GACAC,WAAA,GACAC,OAAA,KACAX,uBAAA,UAEAY,OAAA,KACAC,SAAA,EACAC,SAAA,eACAC,MAAA,sBAEAC,iBAAA,KAEAC,YAAA,EAEAC,wBAAA,EAEAC,mBAAA,SAEAC,gBAAA,SAEAC,sBAAA,KAEAC,sBAAA,KAEAC,eAAA,KAEA,EAEA,aAAAC,SAEA,KAAAC,aAEA,KAAAC,cACA,KAAAC,UAAA,KAAAnB,cAAAoB,KAAA,KAEA,QAAArB,OAAA,KAAAA,MAAAsB,cAAA,CACAC,QAAAC,IAAA,YAEA,MAAAC,EAAA,qDAEAA,EAAAC,QAAAC,IACA,KAAA3B,MAAA4B,GAAAD,EAAA,KACAJ,QAAAC,IAAA,SAAAG,KACA,KAAAE,yBAGA,KAAA7B,MAAAsB,cAAAQ,eACA,KAAA9B,MAAAsB,cAAAQ,cAAAF,GAAAD,EAAA,KACAJ,QAAAC,IAAA,WAAAG,KACA,KAAAE,0BAIA,IAIAjC,OAAAmC,iBAAA,cAAAC,cAGApC,OAAAmC,iBAAA,eAAAE,eAGA,KAAAC,kBACA,EAEAC,QAAA,CACAhB,WAAAA,GACA,KAAApB,IAAA,IAAAF,EAAAA,YAAA,CACAuC,KAAA,KAAAC,MAAAC,WACAC,WAAA,EACAC,SAAA5C,OACA6C,gBAAA,GAEA,EAEA,eAAArB,CAAAsB,GAEA,KAAA1C,QACA,KAAAD,IAAA4C,MAAAC,YAAA,KAAA5C,OACA,KAAAA,MAAA6C,UACA,KAAA7C,MAAA,KAGA,KAAA8C,yBAGA,IAEA,MAAAC,EAAA,KAAA5C,WAAAuC,IAAA,KAAAvC,WAAAuC,GAAAM,SAAA,mBAAAC,KAAAC,QAGA,KAAAlD,YAAAmD,EAAAA,GAAAC,KAAAL,GAGA,KAAAhD,IAAA4C,MAAAU,SAAA,KAAArD,OAGA,KAAAsD,mBAGA,KAAAtD,MAAAuD,WAAA,EAGA,KAAArD,iBAAA,KAAAF,MAAAsB,cAAAkC,SAAAC,QAGA,KAAAvD,iBAAA,KAAAU,sBACA,KAAA8C,iBAAA,KAAA9C,oBAGA,KAAA+C,0BAGApC,QAAAC,IAAA,MAAAkB,SACA,OAAAkB,GACArC,QAAAqC,MAAA,UAAAA,EACA,CACA,EAGAN,gBAAAA,GACA,SAAAtD,MAAA,OAGA,MAAA6D,EAAA,KAAA9D,IAAA+D,SAAAC,MACAC,EAAA,KAAAjE,IAAA+D,SAAAG,OAGAC,EAAA,KAAAlE,MAAA+D,MACAI,EAAA,KAAAnE,MAAAiE,OAIAG,EAAAP,EAAAK,EACAG,EAAAL,EAAAG,EAGAG,EAAA,GAAAC,KAAAC,IAAAJ,EAAAC,GAGA,KAAArE,MAAAsE,MAAAG,IAAAH,GAGA,KAAAtE,MAAA0E,EAAAb,EAAA,EACA,KAAA7D,MAAA2E,EAAAX,EAAA,EAGA,KAAAhE,MAAA4E,OAAAH,IAAA,OAEAlD,QAAAC,IAAA,cAAA8C,UAAA,KAAAtE,MAAA0E,MAAA,KAAA1E,MAAA2E,KACA,EAGA3C,YAAAA,GACA,KAAAhC,OACA,KAAAsD,kBAEA,EAGAuB,UAAAA,CAAAC,EAAAC,GACA,SAAA/E,QAAA,KAAAE,iBAAA4E,GAAA,OAGA,MAAAE,EAAA,KAAAhF,MAAAsB,cAAAQ,cAAAgD,MACAG,EAAA,KAAAjF,MAAAsB,cAAAQ,cAAAiD,MAGAC,IAAAF,GAAAG,IAAAF,GAMAD,IAAA,KAAAlE,qBAEA,KAAAI,eAAAiC,KAAAC,OAIA,KAAAlD,MAAAkF,KAAA,sBACA3D,QAAAC,IAAA,MAAAsD,KAAAC,SACA,KAAAlD,yBAIA,SAAAiD,EACA,KAAA9E,MAAAmF,OAAA,QAAAJ,EAAA,SACA,UAAAD,EACA,KAAA9E,MAAAmF,OAAA,OAAAJ,EAAA,SAGA,KAAA/E,MAAAmF,OAAAL,EAAAC,EAAA,SAIA,KAAAK,+BA3BA7D,QAAAC,IAAA,YAAAsD,KAAAC,QA4BA,EAGArB,gBAAAA,CAAAoB,GACA,SAAA9E,QAAA,KAAAE,iBAAA4E,GAAA,OAEA,MAAArB,EAAA,KAAAvD,iBAAA4E,GACAC,EAAAR,KAAAc,MAAAd,KAAAe,SAAA7B,EAAA8B,QAGAP,EAAA,KAAAhF,MAAAsB,cAAAQ,cAAAgD,MACAG,EAAA,KAAAjF,MAAAsB,cAAAQ,cAAAiD,MAGAC,IAAAF,GAAAG,IAAAF,EAMAD,IAAA,KAAAlE,oBAAAkE,IAAA,KAAAjE,iBAEA,KAAAG,eAAAiC,KAAAC,MAGA,KAAAlD,MAAAmF,OAAAL,EAAAC,EAAA,SAGA,KAAA/E,MAAAkF,KAAA,sBACA3D,QAAAC,IAAA,MAAAsD,KAAAC,iBAEA,KAAA7E,iBAAA,KAAAU,qBACA,KAAAZ,MAAAmF,OAAA,KAAAvE,mBAAA,eAKA,KAAAZ,MAAAmF,OAAAL,EAAAC,EAAA,SAtBAxD,QAAAC,IAAA,YAAAsD,KAAAC,QAwBA,EAGAK,2BAAAA,GAEA,KAAAtE,uBACA0E,aAAA,KAAA1E,uBAIA,KAAAA,sBAAA2E,WAAA,KAEA,QAAAzF,OACA,KAAAA,MAAAsB,eACA,KAAAtB,MAAAsB,cAAAQ,cAAA4D,aAAA,CACAnE,QAAAC,IAAA,kBAGA,MAAAwD,EAAA,KAAAhF,MAAAsB,cAAAQ,cAAAgD,MACAG,EAAA,KAAAjF,MAAAsB,cAAAQ,cAAAiD,MAGA,KAAAtE,mBACA,KAAAP,iBAAA,KAAAU,qBACAoE,IAAA,KAAApE,oBAAA,IAAAqE,IACA1D,QAAAC,IAAA,WAEA,KAAAxB,MAAAmF,OAAA,KAAAvE,mBAAA,YAGA,KAAAI,eAAAiC,KAAAC,MAEA,MAEA,KAAAkC,+BAEA,IACA,EAGAzB,sBAAAA,GAEA,KAAAb,wBAGA,MAAA6C,EAAA,KAGAC,EAAA,IAGA,KAAA5E,iBACA,KAAAA,eAAAiC,KAAAC,OAIA,KAAAnC,sBAAA8E,YAAA,KACA,MAAAC,EAAA7C,KAAAC,MAGA,QAAAlD,OACA,KAAAA,MAAAsB,eACA,KAAAtB,MAAAsB,cAAAQ,eACA,KAAA9B,MAAAsB,cAAAQ,cAAA4D,aAAA,CAGA,MAAAV,EAAA,KAAAhF,MAAAsB,cAAAQ,cAAAgD,MAGAiB,EAAAD,EAAA,KAAA9E,eAGA+E,GAAAH,EAEAZ,GAAAA,IAAA,KAAApE,qBAQA,KAAAZ,MAAAmF,OAAA,KAAAvE,mBAAA,WAIA,KAAAI,eAAA8E,GAIAd,GAAAA,IAAA,KAAApE,qBACAW,QAAAC,IAAA,YACA,KAAAxB,MAAAmF,OAAA,KAAAvE,mBAAA,WAEA,GACA+E,EAGA,EAGA7C,qBAAAA,GACA,KAAA/B,wBACAiF,cAAA,KAAAjF,uBACA,KAAAA,sBAAA,KACAQ,QAAAC,IAAA,YAEA,EAGA,iBAAAyE,CAAAvD,GACA,QAAAzC,eAAAyC,EAAA,OAEA,KAAAzC,aAAAyC,EAGA,MAAAwD,EAAA,KAAA9F,OAAA+F,OAAAzD,GACA,KAAA9B,mBAAAsF,GAAAtF,oBAAA,KAAAR,OAAAoD,SAAA5C,mBACA,KAAAnB,uBAAAyG,GAAA1G,iBAAA,KAAAY,OAAAoD,SAAA4C,uBAEA7E,QAAAC,IAAA,UAAAkB,aAAA,KAAA9B,4BAAA,KAAAnB,gCAEA,KAAA2B,UAAAsB,EACA,EAGA,wBAAA2D,CAAAC,GACA,QAAAtG,MAAA,CAEA,UAAAmF,KAAAmB,QACA,IAAAC,QAAAC,IACA,KAAAxG,MAAAkF,KAAA,iBAAAsB,GACA,kBAAArB,EACA,KAAAzB,iBAAAyB,GAEA,KAAAN,WAAAM,EAAAL,MAAAK,EAAAJ,UAMA,KAAAtE,kBAAA,KAAAP,iBAAA,KAAAU,qBACA,KAAAZ,MAAAmF,OAAA,KAAAvE,mBAAA,OAfA,CAiBA,EAGA6F,aAAAA,GAEA,KAAAC,eAGA,MAAAC,EAAAC,OAAAC,KAAA,KAAA3G,kBACA,GAAAyG,EAAApB,OAAA,GACA,MAAAuB,EAAAH,EAAApC,KAAAc,MAAAd,KAAAe,SAAAqB,EAAApB,SACA,KAAA7B,iBAAAoD,EACA,CAGA,mBAAAvG,WACA,KAAAE,iBAAAoF,YAAA,KAEA,MAAAc,EAAAC,OAAAC,KAAA,KAAA3G,kBACA,GAAAyG,EAAApB,OAAA,GACA,MAAAuB,EAAAH,EAAApC,KAAAc,MAAAd,KAAAe,SAAAqB,EAAApB,SACA,KAAA7B,iBAAAoD,EACA,GACA,KAEA,EAEAJ,YAAAA,GACA,KAAAjG,mBACAuF,cAAA,KAAAvF,kBACA,KAAAA,iBAAA,KAEA,EAGAyB,gBAAAA,GACA,SAAA7B,QAAA,KAAAA,OAAA0G,aAAAC,UAAAC,MAAA,KAAA5G,OAAA0G,aAAAC,UAAAE,WAAA,CAKA,KAAA3G,SAAA,aAEA,IACA,KAAAF,OAAA,IAAA2G,UAAA,KAAAxG,OAEA,KAAAH,OAAA8G,OAAA,KACA5F,QAAAC,IAAA,iBACA,KAAAjB,SAAA,YAGAgB,QAAAC,IAAA,0BAGA,KAAAkF,gBAGA,KAAArG,OAAA+G,UAAAC,IACA,KAAAC,uBAAAD,EAAAvH,OAGA,KAAAO,OAAAkH,QAAA3D,IACArC,QAAAqC,MAAA,eAAAA,GACA,KAAArD,SAAA,SAMA,KAAAF,OAAAmH,QAAA,KACAjG,QAAAC,IAAA,iBACA,KAAAjB,SAAA,eAMAkF,WAAA,KACA,KAAAvD,oBACA,KAEA,OAAA0B,GACArC,QAAAqC,MAAA,iBAAAA,GACA,KAAArD,SAAA,OAIA,CAhDA,MAFAgB,QAAAC,IAAA,qBAmDA,EAEAiG,oBAAAA,CAAAC,GACA,KAAArH,QAAA,KAAAA,OAAA0G,aAAAC,UAAAC,KACA,KAAA5G,OAAAsH,KAAAC,KAAAC,UAAAH,IAEAnG,QAAAuG,KAAA,sBAEA,EAEAR,sBAAAA,CAAAxH,GACA,IACA,MAAA4H,EAAAE,KAAAG,MAAAjI,GAIA,OAHAyB,QAAAC,IAAA,iBAAAkG,GAGAA,EAAAM,MACA,iBAEAN,EAAA5C,YAAAmD,IAAAP,EAAA3C,OAUA,KAAAF,WAAA6C,EAAA5C,MAAA4C,EAAA3C,OAGA,MAEA,uBAEA2C,EAAA5C,OACA,KAAApB,iBAAAgE,EAAA5C,OAEA,MAEA,mBAEA4C,EAAApB,UAAA4B,MAAAC,QAAAT,EAAApB,WACA,KAAAD,mBAAAqB,EAAApB,UAEA,MAEA,kBAEAoB,EAAA1H,OAAA,KAAAG,WAAAuH,EAAA1H,QACA,KAAAiG,YAAAyB,EAAA1H,OAEA,MAEA,QACAuB,QAAAuG,KAAA,oBAAAJ,EAAAM,MAEA,OAAApE,GACArC,QAAAqC,MAAA,mBAAAA,EACA,CACA,EAGAwE,sBAAAA,GACA,KAAA1H,YAAA,KAAAA,UACA,EAGAuB,aAAAA,CAAAoF,GAEA,UAAAA,EAAAgB,MACA,KAAAC,qBAEA,EAGAA,mBAAAA,GACA,MAAA3B,EAAAC,OAAAC,KAAA,KAAA3G,kBACA,OAAAyG,EAAApB,OAAA,OAGA,KAAA5E,yBAAA,KAAAA,wBAAA,GAAAgG,EAAApB,OACA,MAAAgD,EAAA5B,EAAA,KAAAhG,yBACAY,QAAAC,IAAA,UAAA+G,IAAA,KAAAjI,SAEA,KAAAoD,iBAAA6E,GAGAhH,QAAAC,IAAA,WAAA+G,IACA,EAGA1G,oBAAAA,GAIA,GAHAN,QAAAC,IAAA,gBAGA,KAAAxB,QAAA,KAAAA,MAAAsB,gBAAA,KAAAtB,MAAAsB,cAAAQ,cACA,OAIA,MAAAkD,EAAA,KAAAhF,MAAAsB,cAAAQ,cAAAgD,MACAG,EAAA,KAAAjF,MAAAsB,cAAAQ,cAAAiD,MAGA,KAAAtE,mBACA,KAAAP,iBAAA,KAAAU,qBACAoE,IAAA,KAAApE,oBAAA,IAAAqE,IACA1D,QAAAC,IAAA,sBAAAZ,sBAGA,KAAAZ,MAAAmF,OAAA,KAAAvE,mBAAA,YAGA,KAAA4H,qBAEA,EAGAA,kBAAAA,GACA,KAAAxI,OAAA,KAAAA,MAAAsB,gBAGA,KAAAgC,mBAEA/B,QAAAC,IAAA,cACA,EAEA,gBAAAN,GACA,IACA,MAAAuH,QAAAC,MAAA,mBAAAzF,KAAAC,SACA,KAAA9C,aAAAqI,EAAAE,OAGA,KAAA1I,aAAA,KAAAG,OAAAH,aAGA,KAAAE,WAAA,GACAyG,OAAAC,KAAA,KAAAzG,OAAA+F,QAAAzE,QAAAkH,IACA,MAAA5I,EAAA,KAAAI,OAAA+F,OAAAyC,GACA,KAAAzI,WAAAyI,GAAA,KAAAxI,OAAAoD,SAAAqF,UACA7I,EAAA8I,KACA,GAAA9I,EAAA8I,UAAA7F,KAAAC,UAIA,MAAA6F,EAAA,KAAA3I,OAAA+F,OAAA,KAAAlG,cACA,KAAAW,mBAAAmI,GAAAnI,oBAAA,KAAAR,OAAAoD,SAAA5C,mBACA,KAAAnB,uBAAAsJ,GAAAvJ,iBAAA,KAAAY,OAAAoD,SAAA4C,uBAEA,KAAA5F,MAAA,KAAAJ,OAAAoD,SAAAhD,MAEAe,QAAAC,IAAA,eAAApB,QACAmB,QAAAC,IAAA,aAAAvB,cACAsB,QAAAC,IAAA,cAAAZ,oBACAW,QAAAC,IAAA,YAAA/B,uBACA,OAAAmE,GACArC,QAAAqC,MAAA,iBAAAA,GACA,KAAA3D,aAAA,cACA,KAAAW,mBAAA,SACA,KAAAnB,uBAAA,UACA,KAAAU,WAAA,CACA6I,YAAA,8BAAA/F,KAAAC,QAEA,CACA,GAGA+F,aAAAA,GAEA,KAAAjJ,OACA,KAAAA,MAAA6C,UAEA,KAAA9C,KACA,KAAAA,IAAA8C,SAAA,GAIA,KAAA6D,eAGA,KAAA5D,wBAGA,KAAAhC,uBACA0E,aAAA,KAAA1E,uBAIA,KAAAT,SACA,KAAAA,OAAA6I,QACA,KAAA7I,OAAA,MAIAT,OAAAuJ,oBAAA,cAAAnH,cACApC,OAAAuJ,oBAAA,eAAAlH,cACA,GCxqByO,I,UCQrOmH,GAAY,OACd,EACAnK,EACAU,GACA,EACA,KACA,WACA,MAIF,EAAeyJ,E,QChBfC,EAAAA,GAAIjJ,OAAOkJ,eAAgB,EAE3B,IAAID,EAAAA,GAAI,CACNpK,OAAQsK,GAAKA,EAAEC,KACdC,OAAO,O,GCNNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB3B,IAAjB4B,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CACjDI,GAAIJ,EACJK,QAAQ,EACRH,QAAS,CAAC,GAUX,OANAI,EAAoBN,GAAUO,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG3EI,EAAOE,QAAS,EAGTF,EAAOD,OACf,CAGAH,EAAoBS,EAAIF,E,WC5BxB,IAAIG,EAAW,GACfV,EAAoBW,EAAI,SAASC,EAAQC,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIR,EAAS9E,OAAQsF,IAAK,CACrCL,EAAWH,EAASQ,GAAG,GACvBJ,EAAKJ,EAASQ,GAAG,GACjBH,EAAWL,EAASQ,GAAG,GAE3B,IAJA,IAGIC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAASjF,OAAQwF,MACpB,EAAXL,GAAsBC,GAAgBD,IAAa9D,OAAOC,KAAK8C,EAAoBW,GAAGU,MAAM,SAASpC,GAAO,OAAOe,EAAoBW,EAAE1B,GAAK4B,EAASO,GAAK,GAChKP,EAASS,OAAOF,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbT,EAASY,OAAOJ,IAAK,GACrB,IAAIK,EAAIT,SACExC,IAANiD,IAAiBX,EAASW,EAC/B,CACD,CACA,OAAOX,CArBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIR,EAAS9E,OAAQsF,EAAI,GAAKR,EAASQ,EAAI,GAAG,GAAKH,EAAUG,IAAKR,EAASQ,GAAKR,EAASQ,EAAI,GACrGR,EAASQ,GAAK,CAACL,EAAUC,EAAIC,EAwB/B,C,eC5BAf,EAAoBwB,EAAI,SAASpB,GAChC,IAAIqB,EAASrB,GAAUA,EAAOsB,WAC7B,WAAa,OAAOtB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAJ,EAAoB2B,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNAzB,EAAoB2B,EAAI,SAASxB,EAAS0B,GACzC,IAAI,IAAI5C,KAAO4C,EACX7B,EAAoB8B,EAAED,EAAY5C,KAASe,EAAoB8B,EAAE3B,EAASlB,IAC5EhC,OAAO8E,eAAe5B,EAASlB,EAAK,CAAE+C,YAAY,EAAMC,IAAKJ,EAAW5C,IAG3E,C,eCPAe,EAAoBkC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO3M,MAAQ,IAAI4M,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,kBAAXpM,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxB+J,EAAoB8B,EAAI,SAASQ,EAAKC,GAAQ,OAAOtF,OAAOuF,UAAUC,eAAejC,KAAK8B,EAAKC,EAAO,C,eCCtGvC,EAAoBuB,EAAI,SAASpB,GACX,qBAAXuC,QAA0BA,OAAOC,aAC1C1F,OAAO8E,eAAe5B,EAASuC,OAAOC,YAAa,CAAEC,MAAO,WAE7D3F,OAAO8E,eAAe5B,EAAS,aAAc,CAAEyC,OAAO,GACvD,C,eCNA5C,EAAoB6C,IAAM,SAASzC,GAGlC,OAFAA,EAAO0C,MAAQ,GACV1C,EAAO2C,WAAU3C,EAAO2C,SAAW,IACjC3C,CACR,C,eCCA,IAAI4C,EAAkB,CACrB,IAAK,GAaNhD,EAAoBW,EAAES,EAAI,SAAS6B,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4BhN,GAC/D,IAKI8J,EAAUgD,EALVpC,EAAW1K,EAAK,GAChBiN,EAAcjN,EAAK,GACnBkN,EAAUlN,EAAK,GAGI+K,EAAI,EAC3B,GAAGL,EAASyC,KAAK,SAASjD,GAAM,OAA+B,IAAxB2C,EAAgB3C,EAAW,GAAI,CACrE,IAAIJ,KAAYmD,EACZpD,EAAoB8B,EAAEsB,EAAanD,KACrCD,EAAoBS,EAAER,GAAYmD,EAAYnD,IAGhD,GAAGoD,EAAS,IAAIzC,EAASyC,EAAQrD,EAClC,CAEA,IADGmD,GAA4BA,EAA2BhN,GACrD+K,EAAIL,EAASjF,OAAQsF,IACzB+B,EAAUpC,EAASK,GAChBlB,EAAoB8B,EAAEkB,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOjD,EAAoBW,EAAEC,EAC9B,EAEI2C,EAAqBC,KAAK,sBAAwBA,KAAK,uBAAyB,GACpFD,EAAmBxL,QAAQmL,EAAqBO,KAAK,KAAM,IAC3DF,EAAmBG,KAAOR,EAAqBO,KAAK,KAAMF,EAAmBG,KAAKD,KAAKF,G,IC/CvF,IAAII,EAAsB3D,EAAoBW,OAAErC,EAAW,CAAC,KAAM,WAAa,OAAO0B,EAAoB,KAAO,GACjH2D,EAAsB3D,EAAoBW,EAAEgD,E", "sources": ["webpack://live2d/./src/App.vue", "webpack://live2d/src/App.vue", "webpack://live2d/./src/App.vue?c036", "webpack://live2d/./src/App.vue?0e40", "webpack://live2d/./src/main.js", "webpack://live2d/webpack/bootstrap", "webpack://live2d/webpack/runtime/chunk loaded", "webpack://live2d/webpack/runtime/compat get default export", "webpack://live2d/webpack/runtime/define property getters", "webpack://live2d/webpack/runtime/global", "webpack://live2d/webpack/runtime/hasOwnProperty shorthand", "webpack://live2d/webpack/runtime/make namespace object", "webpack://live2d/webpack/runtime/node module decorator", "webpack://live2d/webpack/runtime/jsonp chunk loading", "webpack://live2d/webpack/startup"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"live2d-container\",style:({ backgroundColor: _vm.currentBackgroundColor })},[_c('canvas',{ref:\"liveCanvas\"})])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\n<script>\nimport * as PIXI from 'pixi.js';\nimport { Live2DModel } from 'pixi-live2d-display/cubism4';\nwindow.PIXI = PIXI; // 为了pixi-live2d-display内部调用\n\nexport default {\n  data() {\n    return {\n      app: null,\n      model: null,\n      currentModel: 'greenlive2d',\n      availableMotions: {},\n      modelPaths: {},\n      config: null,\n      currentBackgroundColor: '#000000',\n      // WebSocket相关\n      socket: null,\n      isfirst: true,\n      wsStatus: 'disconnected',\n      wsUrl: 'ws://localhost:8002',\n      // 自动播放控制\n      autoPlayInterval: null,\n      // 状态显示控制\n      showStatus: false,\n      // 当前动作组索引\n      currentMotionGroupIndex: 0,\n      // 默认动作组\n      defaultMotionGroup: 'center',\n      // 循环动作组\n      loopMotionGroup: 'center',\n      // 定时器引用\n      _returnToDefaultTimer: null,\n      // 默认动作循环控制\n      defaultMotionInterval: null,\n      // 上次执行动作的时间\n      lastMotionTime: null\n    };\n  },\n  \n  async mounted() {\n    // 先加载配置文件\n    await this.loadConfig();\n    \n    this.initPixiApp();\n    this.loadModel(this.currentModel).then(() => {\n      // 模型加载完成后，添加全局动作监听\n      if (this.model && this.model.internalModel) {\n        console.log('添加全局动作监听');\n        // 尝试多种可能的事件名称\n        const possibleEventNames = ['motionFinished', 'motionEnd', 'finished', 'complete'];\n        \n        possibleEventNames.forEach(eventName => {\n          this.model.on(eventName, () => {\n            console.log(`事件触发: ${eventName}`);\n            this.handleMotionFinished();\n          });\n          \n          if (this.model.internalModel.motionManager) {\n            this.model.internalModel.motionManager.on(eventName, () => {\n              console.log(`内部事件触发: ${eventName}`);\n              this.handleMotionFinished();\n            });\n          }\n        });\n      }\n    });\n    \n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize);\n    \n    // 添加键盘事件监听\n    window.addEventListener('keydown', this.handleKeyDown);\n    \n    // 自动连接WebSocket\n    this.connectWebSocket();\n  },\n  \n  methods: {\n    initPixiApp() {\n      this.app = new PIXI.Application({\n        view: this.$refs.liveCanvas,\n        autoStart: true,\n        resizeTo: window,\n        backgroundAlpha: 0,\n      });\n    },\n    \n    async loadModel(modelName) {\n      // 如果已有模型，先销毁\n      if (this.model) {\n        this.app.stage.removeChild(this.model);\n        this.model.destroy();\n        this.model = null;\n        \n        // 停止默认动作循环\n        this.stopDefaultMotionLoop();\n      }\n      \n      try {\n        // 添加时间戳防止缓存\n        const modelPath = this.modelPaths[modelName] + (this.modelPaths[modelName].includes('?') ? '&' : '?') + `t=${Date.now()}`;\n        \n        // 加载新模型\n        this.model = await Live2DModel.from(modelPath);\n        \n        // 添加到舞台\n        this.app.stage.addChild(this.model);\n        \n        // 设置模型位置和缩放\n        this.fitModelToScreen();\n        \n        // 禁用拖拽\n        this.model.draggable = false;\n        \n        // 获取可用的动作列表\n        this.availableMotions = this.model.internalModel.settings.motions;\n        \n        // 默认播放center动作\n        if (this.availableMotions[this.defaultMotionGroup]) {\n          this.playRandomMotion(this.defaultMotionGroup);\n          \n          // 启动默认动作循环\n          this.startDefaultMotionLoop();\n        }\n        \n        console.log(`模型 ${modelName} 加载成功`);\n      } catch (error) {\n        console.error(`模型加载失败:`, error);\n      }\n    },\n    \n    // 适配屏幕的方法\n    fitModelToScreen() {\n      if (!this.model) return;\n      \n      // 获取屏幕尺寸\n      const screenWidth = this.app.renderer.width;\n      const screenHeight = this.app.renderer.height;\n      \n      // 获取模型原始尺寸\n      const modelWidth = this.model.width;\n      const modelHeight = this.model.height;\n      \n      // 计算适合屏幕的缩放比例\n      // 使用较小的缩放比例确保模型完全在屏幕内\n      const scaleX = screenWidth / modelWidth;\n      const scaleY = screenHeight / modelHeight;\n      \n      // 使用较小的缩放比例确保模型完全在屏幕内\n      const scale = Math.min(scaleX, scaleY) * 0.9; // 乘以0.9留出一些边距\n      \n      // 应用缩放\n      this.model.scale.set(scale);\n      \n      // 调整模型位置，使其居中\n      this.model.x = screenWidth / 2;\n      this.model.y = screenHeight / 2; // 居中显示\n      \n      // 设置锚点为中心，这样缩放和位置会以模型中心为基准\n      this.model.anchor.set(0.5, 0.5);\n      \n      console.log(`模型适配屏幕: 缩放=${scale}, 位置=(${this.model.x}, ${this.model.y})`);\n    },\n    \n    // 处理窗口大小变化\n    handleResize() {\n      if (this.model) {\n        this.fitModelToScreen();\n      }\n    },\n    \n    // 播放指定组的指定索引动作\n    playMotion(group, index) {\n      if (!this.model || !this.availableMotions[group]) return;\n      \n      // 获取当前正在播放的动作组和索引\n      const currentGroup = this.model.internalModel.motionManager.group;\n      const currentIndex = this.model.internalModel.motionManager.index;\n      \n      // 如果已经在播放相同的动作，则不重复播放\n      if (currentGroup === group && currentIndex === index) {\n        console.log(`已经在播放动作: ${group}[${index}]，跳过`);\n        return;\n      }\n      \n      // 如果要播放的不是默认动作，停止默认动作循环\n      if (group !== this.defaultMotionGroup) {\n        // 更新上次动作时间，重新开始20秒计时\n        this.lastMotionTime = Date.now();\n      }\n\n      // 为所有动作添加完成监听\n      this.model.once('motionFinished', () => {\n        console.log(`动作 ${group}[${index}] 完成`);\n        this.handleMotionFinished();\n      });\n\n      // 处理左右方向的特殊情况\n      if(group === 'left'){\n        this.model.motion('right', index, 3, true, 0.8);\n      } else if(group === 'right'){\n        this.model.motion('left', index, 3, true, 0.8);\n      } else {\n        // 增加淡入淡出时间，使过渡更平滑\n        this.model.motion(group, index, 3, true, 0.8);\n      }\n      \n      // 确保动作完成后回到默认动作\n      this.ensureReturnToDefaultMotion();\n    },\n    \n    // 播放指定组的随机动作\n    playRandomMotion(group) {\n      if (!this.model || !this.availableMotions[group]) return;\n      \n      const motions = this.availableMotions[group];\n      const index = Math.floor(Math.random() * motions.length);\n      \n      // 获取当前正在播放的动作组和索引\n      const currentGroup = this.model.internalModel.motionManager.group;\n      const currentIndex = this.model.internalModel.motionManager.index;\n      \n      // 如果已经在播放相同的动作，则不重复播放\n      if (currentGroup === group && currentIndex === index) {\n        console.log(`已经在播放动作: ${group}[${index}]，跳过`);\n        return;\n      }\n      \n      // 如果要播放的不是默认动作和循环动作，更新上次动作时间\n      if (group !== this.defaultMotionGroup && group !== this.loopMotionGroup) {\n        // 更新上次动作时间，重新开始20秒计时\n        this.lastMotionTime = Date.now();\n        \n        // 增加淡入淡出时间，使过渡更平滑\n        this.model.motion(group, index, 3, true, 0.8);\n        \n        // 为非默认动作添加动作完成监听\n        this.model.once('motionFinished', () => {\n          console.log(`动作 ${group}[${index}] 完成，切换到默认动作`);\n          // 切换到默认动作\n          if (this.availableMotions[this.defaultMotionGroup]) {\n            this.model.motion(this.defaultMotionGroup, 0, 1, false, 1.5);\n          }\n        });\n      } else {\n        // 如果是默认动作或循环动作，直接播放，不需要添加完成监听\n        this.model.motion(group, index, 3, true, 0.8);\n      }\n    },\n    \n    // 确保动作完成后回到默认动作\n    ensureReturnToDefaultMotion() {\n      // 清除之前的定时器，避免多个定时器同时触发\n      if (this._returnToDefaultTimer) {\n        clearTimeout(this._returnToDefaultTimer);\n      }\n      \n      // 使用setTimeout检测动作是否完成\n      this._returnToDefaultTimer = setTimeout(() => {\n        // 检查动作是否已完成\n        if (this.model && \n            this.model.internalModel && \n            this.model.internalModel.motionManager.isFinished()) {\n          console.log('动作已完成（通过定时器检测）');\n          \n          // 获取当前正在播放的动作组和索引\n          const currentGroup = this.model.internalModel.motionManager.group;\n          const currentIndex = this.model.internalModel.motionManager.index;\n          \n          // 如果当前不是自动播放模式且不是已经在播放默认动作，才切换到默认动作\n          if (!this.autoPlayInterval && \n              this.availableMotions[this.defaultMotionGroup] && \n              (currentGroup !== this.defaultMotionGroup || currentIndex !== 0)) {\n            console.log('切换到默认动作');\n            // 使用较长的淡入时间，使过渡更平滑\n            this.model.motion(this.defaultMotionGroup, 0, 1, false, 1.5);\n            \n            // 更新上次动作时间，重新开始20秒计时\n            this.lastMotionTime = Date.now();\n          }\n        } else {\n          // 如果动作还没完成，继续检测\n          this.ensureReturnToDefaultMotion();\n        }\n      }, 2000); // 设置一个合理的延迟，根据动作的平均持续时间调整\n    },\n    \n    // 启动默认动作循环\n    startDefaultMotionLoop() {\n      // 先停止可能存在的循环\n      this.stopDefaultMotionLoop();\n      \n      // 设置默认动作循环间隔（较短，例如5秒）\n      const defaultLoopInterval = 1500; // 5秒\n      \n      // 设置yun动作循环间隔（20秒）\n      const yunLoopInterval = 20000; // 20秒\n      \n      // 记录上次执行动作的时间\n      if (!this.lastMotionTime) {\n        this.lastMotionTime = Date.now();\n      }\n      \n      // 启动循环\n      this.defaultMotionInterval = setInterval(() => {\n        const currentTime = Date.now();\n        \n        // 检查当前是否可以播放动作\n        if (this.model && \n            this.model.internalModel && \n            this.model.internalModel.motionManager &&\n            this.model.internalModel.motionManager.isFinished()) {\n          \n          // 获取当前正在播放的动作组\n          const currentGroup = this.model.internalModel.motionManager.group;\n          \n          // 计算距离上次动作的时间\n          const timeSinceLastMotion = currentTime - this.lastMotionTime;\n          \n          // 如果距离上次动作已经过了20秒，播放yun动作\n          if (timeSinceLastMotion >= yunLoopInterval) {\n            // 只有当前没有播放其他指令动作时才播放yun动作\n            if (!currentGroup || currentGroup === this.defaultMotionGroup) {\n              // console.log('循环播放yun动作，距离上次动作时间：' + timeSinceLastMotion + 'ms');\n              \n              // 检查是否有yun动作组\n              // if (this.availableMotions[this.loopMotionGroup]) {\n              //   this.model.motion(this.loopMotionGroup, 0, 1, false, 0.5);\n              // } else {\n                // 如果没有yun动作组，则播放默认动作\n                this.model.motion(this.defaultMotionGroup, 0, 1, false, 0.5);\n              // }\n              \n              // 更新上次动作时间\n              this.lastMotionTime = currentTime;\n            }\n          } \n          // 否则，如果当前没有播放动作或已经播放完成，播放默认动作\n          else if (!currentGroup || currentGroup === this.defaultMotionGroup) {\n            console.log('循环播放默认动作');\n            this.model.motion(this.defaultMotionGroup, 0, 1, false, 0.5);\n          }\n        }\n      }, defaultLoopInterval); // 每5秒检查一次\n      \n      // console.log(`启动默认动作循环，默认间隔: ${defaultLoopInterval}ms，yun间隔: ${yunLoopInterval}ms`);\n    },\n    \n    // 停止默认动作循环\n    stopDefaultMotionLoop() {\n      if (this.defaultMotionInterval) {\n        clearInterval(this.defaultMotionInterval);\n        this.defaultMotionInterval = null;\n        console.log('停止默认动作循环');\n      }\n    },\n    \n    // 切换模型\n    async switchModel(modelName) {\n      if (this.currentModel === modelName) return;\n      \n      this.currentModel = modelName;\n      \n      // 更新默认动作组和背景色\n      const modelConfig = this.config.models[modelName];\n      this.defaultMotionGroup = modelConfig?.defaultMotionGroup || this.config.settings.defaultMotionGroup;\n      this.currentBackgroundColor = modelConfig?.backgroundColor || this.config.settings.defaultBackgroundColor;\n      \n      console.log(`切换到模型: ${modelName}, 默认动作组: ${this.defaultMotionGroup}, 背景色: ${this.currentBackgroundColor}`);\n      \n      await this.loadModel(modelName);\n    },\n    \n    // 播放动作序列\n    async playMotionSequence(sequence) {\n      if (!this.model) return;\n      \n      for (const motion of sequence) {\n        await new Promise(resolve => {\n          this.model.once('motionFinished', resolve);\n          if (typeof motion === 'string') {\n            this.playRandomMotion(motion);\n          } else {\n            this.playMotion(motion.group, motion.index);\n          }\n        });\n      }\n      \n      // 序列播放完成后，确保回到默认动作\n      if (!this.autoPlayInterval && this.availableMotions[this.defaultMotionGroup]) {\n        this.model.motion(this.defaultMotionGroup, 0, 1, false);\n      }\n    },\n    \n    // 自动播放控制\n    startAutoPlay() {\n      // 清除可能存在的定时器\n      this.stopAutoPlay();\n\n      // 随机选择一个动作组\n      const groups = Object.keys(this.availableMotions);\n          if (groups.length > 0) {\n            const randomGroup = groups[Math.floor(Math.random() * groups.length)];\n            this.playRandomMotion(randomGroup);\n          }\n      \n      // 只有在WebSocket未连接时才启动自动播放\n      if (this.wsStatus !== 'connected') {\n        this.autoPlayInterval = setInterval(() => {\n          // 随机选择一个动作组\n          const groups = Object.keys(this.availableMotions);\n          if (groups.length > 0) {\n            const randomGroup = groups[Math.floor(Math.random() * groups.length)];\n            this.playRandomMotion(randomGroup);\n          }\n        }, 4000); // 每2秒随机播放一个动作\n      }\n    },\n    \n    stopAutoPlay() {\n      if (this.autoPlayInterval) {\n        clearInterval(this.autoPlayInterval);\n        this.autoPlayInterval = null;\n      }\n    },\n    \n    // WebSocket相关方法\n    connectWebSocket() {\n      if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {\n        console.log('WebSocket已连接或正在连接中');\n        return;\n      }\n      \n      this.wsStatus = 'connecting';\n      \n      try {\n        this.socket = new WebSocket(this.wsUrl);\n        \n        this.socket.onopen = () => {\n          console.log('WebSocket连接成功');\n          this.wsStatus = 'connected';\n          \n          // 不发送初始化消息，只接收\n          console.log('WebSocket已连接，等待接收消息...');\n          \n          // 停止自动播放\n          this.stopAutoPlay();\n        };\n        \n        this.socket.onmessage = (event) => {\n          this.handleWebSocketMessage(event.data);\n        };\n        \n        this.socket.onerror = (error) => {\n          console.error('WebSocket错误:', error);\n          this.wsStatus = 'error';\n          \n          // 启动自动播放\n          // this.startAutoPlay();\n        };\n        \n        this.socket.onclose = () => {\n          console.log('WebSocket连接关闭');\n          this.wsStatus = 'disconnected';\n          \n          // 启动自动播放\n          // this.startAutoPlay();\n          \n          // 尝试重新连接\n          setTimeout(() => {\n            this.connectWebSocket();\n          }, 5000); // 5秒后尝试重新连接\n        };\n      } catch (error) {\n        console.error('WebSocket连接失败:', error);\n        this.wsStatus = 'error';\n        \n        // 启动自动播放\n        // this.startAutoPlay();\n      }\n    },\n    \n    sendWebSocketMessage(message) {\n      if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n        this.socket.send(JSON.stringify(message));\n      } else {\n        console.warn('WebSocket未连接，无法发送消息');\n      }\n    },\n    \n    handleWebSocketMessage(data) {\n      try {\n        const message = JSON.parse(data);\n        console.log('收到WebSocket消息:', message);\n        \n        // 根据消息类型执行不同操作\n        switch (message.type) {\n          case 'playMotion':\n            // 播放指定动作\n            if (message.group && message.index !== undefined) {\n              //这里两个参数需要传\n              // {\n                  //  \"type\": \"playMotion\",\n                  //  \"group\": \"Tap\",    // 动作组名称，如\"Idle\"、\"Tap\"、\"FlickUp\"等\n                  //  \"index\": 1         // 该组中的动作索引，从0开始\n              // }\n              // if(message.group === 'center'){\n              //   this.playRandomMotion('sleep', message.index);\n              // } else {\n                this.playMotion(message.group, message.index);\n              // }\n            }\n            break;\n            \n          case 'playRandomMotion':\n            // 播放随机动作\n            if (message.group) {\n              this.playRandomMotion(message.group);\n            }\n            break;\n            \n          case 'playSequence':\n            // 播放动作序列\n            if (message.sequence && Array.isArray(message.sequence)) {\n              this.playMotionSequence(message.sequence);\n            }\n            break;\n            \n          case 'switchModel':\n            // 切换模型\n            if (message.model && this.modelPaths[message.model]) {\n              this.switchModel(message.model);\n            }\n            break;\n            \n          default:\n            console.warn('未知的WebSocket消息类型:', message.type);\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    },\n    \n    // 切换状态显示\n    toggleStatusVisibility() {\n      this.showStatus = !this.showStatus;\n    },\n    \n    // 处理键盘事件\n    handleKeyDown(event) {\n      // 空格键切换动作\n      if (event.code === 'Space') {\n        this.playNextMotionGroup();\n      }\n    },\n    \n    // 播放下一个动作组的随机动作\n    playNextMotionGroup() {\n      const groups = Object.keys(this.availableMotions);\n      if (groups.length === 0) return;\n      \n      // 循环切换到下一个动作组\n      this.currentMotionGroupIndex = (this.currentMotionGroupIndex + 1) % groups.length;\n      const nextGroup = groups[this.currentMotionGroupIndex];\n      console.log(`当前动作组: ${nextGroup}`,this.isfirst);\n    \n      this.playRandomMotion(nextGroup);\n      \n      \n      console.log(`切换到动作组: ${nextGroup}`);\n    },\n    \n    // 处理动作完成事件\n    handleMotionFinished() {\n      console.log('动作完成处理函数被调用');\n      \n      // 确保模型和动作管理器存在\n      if (!this.model || !this.model.internalModel || !this.model.internalModel.motionManager) {\n        return;\n      }\n      \n      // 获取当前正在播放的动作组和索引\n      const currentGroup = this.model.internalModel.motionManager.group;\n      const currentIndex = this.model.internalModel.motionManager.index;\n      \n      // 如果当前不是自动播放模式且不是已经在播放默认动作，才切换到默认动作\n      if (!this.autoPlayInterval && \n          this.availableMotions[this.defaultMotionGroup] && \n          (currentGroup !== this.defaultMotionGroup || currentIndex !== 0)) {\n        console.log(`动作完成，重置到默认动作: ${this.defaultMotionGroup}`);\n        \n        // 使用较长的淡入时间，使过渡更平滑\n        this.model.motion(this.defaultMotionGroup, 0, 1, false, 1.5);\n        \n        // 重置模型位置和姿态\n        this.resetModelPosition();\n      }\n    },\n\n    // 重置模型到默认位置和姿态\n    resetModelPosition() {\n      if (!this.model || !this.model.internalModel) return;\n      \n      // 重置模型的变换\n      this.fitModelToScreen();\n      \n      console.log('模型已重置到默认位置');\n    },\n    // 加载配置文件\n    async loadConfig() {\n      try {\n        const response = await fetch(`./config.json?t=${Date.now()}`);\n        this.config = await response.json();\n        \n        // 设置当前模型\n        this.currentModel = this.config.currentModel;\n        \n        // 构建模型路径对象\n        this.modelPaths = {};\n        Object.keys(this.config.models).forEach(key => {\n          const model = this.config.models[key];\n          this.modelPaths[key] = this.config.settings.autoCache \n            ? model.path \n            : `${model.path}?t=${Date.now()}`;\n        });\n        \n        // 设置当前模型的默认动作组和背景色\n        const currentModelConfig = this.config.models[this.currentModel];\n        this.defaultMotionGroup = currentModelConfig?.defaultMotionGroup || this.config.settings.defaultMotionGroup;\n        this.currentBackgroundColor = currentModelConfig?.backgroundColor || this.config.settings.defaultBackgroundColor;\n        \n        this.wsUrl = this.config.settings.wsUrl;\n        \n        console.log('配置加载成功:', this.config);\n        console.log('当前模型:', this.currentModel);\n        console.log('默认动作组:', this.defaultMotionGroup);\n        console.log('背景色:', this.currentBackgroundColor);\n      } catch (error) {\n        console.error('配置加载失败，使用默认配置:', error);\n        this.currentModel = 'greenlive2d';\n        this.defaultMotionGroup = 'center';\n        this.currentBackgroundColor = '#000000';\n        this.modelPaths = {\n          greenlive2d: `./lvse/green.model3.json?t=${Date.now()}`\n        };\n      }\n    }\n  },\n  \n  beforeUnmount() {\n    // 清理资源\n    if (this.model) {\n      this.model.destroy();\n    }\n    if (this.app) {\n      this.app.destroy(true);\n    }\n    \n    // 停止自动播放\n    this.stopAutoPlay();\n    \n    // 停止默认动作循环\n    this.stopDefaultMotionLoop();\n    \n    // 清除定时器\n    if (this._returnToDefaultTimer) {\n      clearTimeout(this._returnToDefaultTimer);\n    }\n    \n    // 断开WebSocket连接\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    \n    // 移除事件监听\n    window.removeEventListener('resize', this.handleResize);\n    window.removeEventListener('keydown', this.handleKeyDown);\n  }\n}\n</script>\n<template>\n  <div class=\"live2d-container\" :style=\"{ backgroundColor: currentBackgroundColor }\">\n    <canvas ref=\"liveCanvas\"></canvas>\n    \n    <!-- <div class=\"websocket-status\" v-if=\"showStatus\">\n      WebSocket: {{ wsStatus }}\n      <button @click=\"toggleStatusVisibility\">隐藏</button>\n    </div>\n    <div class=\"status-toggle\" v-else>\n      <button @click=\"toggleStatusVisibility\">显示状态</button>\n    </div> -->\n  </div>\n</template>\n\n<style scoped>\n.live2d-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  overflow: hidden;\n}\n\ncanvas {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  width: 100%;\n  height: 100%;\n  display: block;\n}\n\n.websocket-status {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 10px;\n  border-radius: 5px;\n  color: white;\n  z-index: 10;\n}\n\n.status-toggle {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  z-index: 10;\n}\n\nbutton {\n  padding: 5px 10px;\n  background: #4CAF50;\n  border: none;\n  border-radius: 3px;\n  color: white;\n  cursor: pointer;\n  margin-left: 5px;\n}\n\nbutton:hover {\n  background: #45a049;\n}\n\nbutton:disabled {\n  background: #cccccc;\n  cursor: not-allowed;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "import mod from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=8d46e566&scoped=true\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=8d46e566&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8d46e566\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport App from './App.vue'\n\nVue.config.productionTip = false\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = function(module) {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunklive2d\"] = self[\"webpackChunklive2d\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(9596); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "style", "backgroundColor", "currentBackgroundColor", "ref", "staticRenderFns", "window", "PIXI", "data", "app", "model", "currentModel", "availableMotions", "modelPaths", "config", "socket", "isfirst", "wsStatus", "wsUrl", "autoPlayInterval", "showStatus", "currentMotionGroupIndex", "defaultMotionGroup", "loopMotionGroup", "_returnToDefaultTimer", "defaultMotionInterval", "lastMotionTime", "mounted", "loadConfig", "initPixiApp", "loadModel", "then", "internalModel", "console", "log", "possibleEventNames", "for<PERSON>ach", "eventName", "on", "handleMotionFinished", "motionManager", "addEventListener", "handleResize", "handleKeyDown", "connectWebSocket", "methods", "view", "$refs", "liveCanvas", "autoStart", "resizeTo", "backgroundAlpha", "modelName", "stage", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "stopDefaultMotionLoop", "modelPath", "includes", "Date", "now", "Live2DModel", "from", "<PERSON><PERSON><PERSON><PERSON>", "fitModelToScreen", "draggable", "settings", "motions", "playRandomMotion", "startDefaultMotionLoop", "error", "screenWidth", "renderer", "width", "screenHeight", "height", "modelWidth", "modelHeight", "scaleX", "scaleY", "scale", "Math", "min", "set", "x", "y", "anchor", "playMotion", "group", "index", "currentGroup", "currentIndex", "once", "motion", "ensureReturnToDefaultMotion", "floor", "random", "length", "clearTimeout", "setTimeout", "isFinished", "defaultLoopInterval", "yunLoopInterval", "setInterval", "currentTime", "timeSinceLastMotion", "clearInterval", "switchModel", "modelConfig", "models", "defaultBackgroundColor", "playMotionSequence", "sequence", "Promise", "resolve", "startAutoPlay", "stopAutoPlay", "groups", "Object", "keys", "randomGroup", "readyState", "WebSocket", "OPEN", "CONNECTING", "onopen", "onmessage", "event", "handleWebSocketMessage", "onerror", "onclose", "sendWebSocketMessage", "message", "send", "JSON", "stringify", "warn", "parse", "type", "undefined", "Array", "isArray", "toggleStatusVisibility", "code", "playNextMotionGroup", "nextGroup", "resetModelPosition", "response", "fetch", "json", "key", "autoCache", "path", "currentModelConfig", "greenlive2d", "beforeUnmount", "close", "removeEventListener", "component", "<PERSON><PERSON>", "productionTip", "h", "App", "$mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "id", "loaded", "__webpack_modules__", "call", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "every", "splice", "r", "n", "getter", "__esModule", "d", "a", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "e", "obj", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "value", "nmd", "paths", "children", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "bind", "push", "__webpack_exports__"], "sourceRoot": ""}