{"Version": 3, "Meta": {"Duration": 6.967, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 21, "TotalSegmentCount": 166, "TotalPointCount": 477, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param5", "Segments": [0, 30, 0, 6.967, 30]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 30, 0, 6.967, 30]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 30, 1, 1.722, 30, 3.444, 30, 5.167, 30, 1, 5.211, 30, 5.256, -30, 5.3, -30, 1, 5.422, -30, 5.544, -30, 5.667, -30, 0, 6.967, -30]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 30, 1, 1.722, 30, 3.444, 30, 5.167, 30, 1, 5.211, 30, 5.256, -30, 5.3, -30, 1, 5.422, -30, 5.544, -30, 5.667, -30, 0, 6.967, -30]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0, 0.333, 0, 1, 0.533, 0, 0.733, -30, 0.933, -30, 1, 1.033, -30, 1.133, -30, 1.233, -30, 1, 1.444, -30, 1.656, 30, 1.867, 30, 1, 1.944, 30, 2.022, 30, 2.1, 30, 1, 2.444, 30, 2.789, -30, 3.133, -30, 1, 3.522, -30, 3.911, 30, 4.3, 30, 1, 4.356, 30, 4.411, 30, 4.467, 30, 1, 4.7, 30, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 24, 0.033, 24, 1, 0.133, 24, 0.233, 24, 0.333, 24, 1, 0.533, 24, 0.733, 24, 0.933, 24, 1, 1.033, 24, 1.133, 24, 1.233, 24, 1, 1.444, 24, 1.656, 24, 1.867, 24, 1, 1.944, 24, 2.022, 24, 2.1, 24, 1, 2.444, 24, 2.789, 24, 3.133, 24, 1, 3.522, 24, 3.911, 24, 4.3, 24, 1, 4.356, 24, 4.411, 24, 4.467, 24, 1, 4.7, 24, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -30, 1, 1.489, -30, 2.978, -30, 4.467, -30, 1, 4.7, -30, 4.933, -30, 5.167, -30, 1, 5.211, -30, 5.256, -30, 5.3, -30, 0, 6.967, -30]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 30, 1, 0.011, 30, 0.022, -30, 0.033, -30, 1, 0.133, -30, 0.233, -30, 0.333, -30, 1, 1.711, -30, 3.089, -30, 4.467, -30, 1, 4.7, -30, 4.933, 30, 5.167, 30, 1, 5.211, 30, 5.256, 30, 5.3, 30, 0, 6.967, 30]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 1, 1.489, -30, 2.978, -30, 4.467, -30, 1, 4.7, -30, 4.933, -30, 5.167, -30, 1, 5.211, -30, 5.256, -30, 5.3, -30, 1, 5.422, -30, 5.544, 30, 5.667, 30, 1, 5.767, 30, 5.867, -30, 5.967, -30, 0, 6.967, -30]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 30, 1, 1.489, 30, 2.978, 30, 4.467, 30, 1, 4.744, 30, 5.022, 30, 5.3, 30, 1, 5.422, 30, 5.544, -30, 5.667, -30, 1, 5.767, -30, 5.867, 30, 5.967, 30, 0, 6.967, 30]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0, 0.333, 0, 1, 0.533, 0, 0.733, -30, 0.933, -30, 1, 1.033, -30, 1.133, -30, 1.233, -30, 1, 1.444, -30, 1.656, 30, 1.867, 30, 1, 1.944, 30, 2.022, 30, 2.1, 30, 1, 2.444, 30, 2.789, -30, 3.133, -30, 1, 3.522, -30, 3.911, 30, 4.3, 30, 1, 4.356, 30, 4.411, 30, 4.467, 30, 1, 4.7, 30, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.011, 0, 0.022, -30, 0.033, -30, 1, 0.133, -30, 0.233, -30, 0.333, -30, 1, 0.533, -30, 0.733, -30, 0.933, -30, 1, 1.033, -30, 1.133, -30, 1.233, -30, 1, 1.444, -30, 1.656, -30, 1.867, -30, 1, 2.289, -30, 2.711, -30, 3.133, -30, 1, 3.578, -30, 4.022, -30, 4.467, -30, 1, 4.7, -30, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 30, 0.033, 30, 1, 0.133, 30, 0.233, 30, 0.333, 30, 1, 0.533, 30, 0.733, 30, 0.933, 30, 1, 1.033, 30, 1.133, 30, 1.233, 30, 1, 1.444, 30, 1.656, 30, 1.867, 30, 1, 1.944, 30, 2.022, 30, 2.1, 30, 1, 2.444, 30, 2.789, 30, 3.133, 30, 1, 3.522, 30, 3.911, 30, 4.3, 30, 1, 4.356, 30, 4.411, 30, 4.467, 30, 1, 4.7, 30, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0, 0.333, 0, 1, 0.533, 0, 0.733, -30, 0.933, -30, 1, 1.033, -30, 1.133, -30, 1.233, -30, 1, 1.444, -30, 1.656, 30, 1.867, 30, 1, 1.944, 30, 2.022, 30, 2.1, 30, 1, 2.444, 30, 2.789, -30, 3.133, -30, 1, 3.522, -30, 3.911, 30, 4.3, 30, 1, 4.356, 30, 4.411, 30, 4.467, 30, 1, 4.7, 30, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0, 0.333, 0, 1, 0.533, 0, 0.733, 0, 0.933, 0, 1, 1.033, 0, 1.133, 0, 1.233, 0, 1, 1.867, 0, 2.5, 0, 3.133, 0, 1, 3.578, 0, 4.022, 0, 4.467, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 1, 1, 0.011, 1, 0.022, 1, 0.033, 1, 1, 0.133, 1, 0.233, 1, 0.333, 1, 1, 0.533, 1, 0.733, -0.5, 0.933, -0.5, 1, 1.033, -0.5, 1.133, -0.5, 1.233, -0.5, 1, 1.444, -0.5, 1.656, -1, 1.867, -1, 1, 1.944, -1, 2.022, -1, 2.1, -1, 1, 2.444, -1, 2.789, -0.5, 3.133, -0.5, 1, 3.522, -0.5, 3.911, -1, 4.3, -1, 1, 4.356, -1, 4.411, -1, 4.467, -1, 1, 4.7, -1, 4.933, 1, 5.167, 1, 1, 5.211, 1, 5.256, 1, 5.3, 1, 0, 6.967, 1]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 1, 1, 0.011, 1, 0.022, 1, 0.033, 1, 1, 0.133, 1, 0.233, 1, 0.333, 1, 1, 0.533, 1, 0.733, -0.5, 0.933, -0.5, 1, 1.033, -0.5, 1.133, -0.5, 1.233, -0.5, 1, 1.444, -0.5, 1.656, -1, 1.867, -1, 1, 1.944, -1, 2.022, -1, 2.1, -1, 1, 2.444, -1, 2.789, -0.5, 3.133, -0.5, 1, 3.522, -0.5, 3.911, -1, 4.3, -1, 1, 4.356, -1, 4.411, -1, 4.467, -1, 1, 4.7, -1, 4.933, 1, 5.167, 1, 1, 5.211, 1, 5.256, 1, 5.3, 1, 0, 6.967, 1]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.011, 0, 0.022, 1, 0.033, 1, 1, 0.133, 1, 0.233, 1, 0.333, 1, 1, 1.944, 1, 3.556, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0, 0.333, 0, 1, 0.533, 0, 0.733, -1, 0.933, -1, 1, 1.033, -1, 1.133, -1, 1.233, -1, 1, 1.444, -1, 1.656, 0.5, 1.867, 0.5, 1, 1.944, 0.5, 2.022, 0.5, 2.1, 0.5, 1, 2.444, 0.5, 2.789, -1, 3.133, -1, 1, 3.522, -1, 3.911, 0.5, 4.3, 0.5, 1, 4.356, 0.5, 4.411, 0.5, 4.467, 0.5, 1, 4.7, 0.5, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 1, 0.033, 1, 1, 0.133, 1, 0.233, 1, 0.333, 1, 1, 1.944, 1, 3.556, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0, 0.333, 0, 1, 0.533, 0, 0.733, -1, 0.933, -1, 1, 1.033, -1, 1.133, -1, 1.233, -1, 1, 1.444, -1, 1.656, 0.5, 1.867, 0.5, 1, 1.944, 0.5, 2.022, 0.5, 2.1, 0.5, 1, 2.444, 0.5, 2.789, -1, 3.133, -1, 1, 3.522, -1, 3.911, 0.5, 4.3, 0.5, 1, 4.356, 0.5, 4.411, 0.5, 4.467, 0.5, 1, 4.7, 0.5, 4.933, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 0, 6.967, 0]}]}