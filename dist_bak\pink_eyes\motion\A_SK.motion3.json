{"Version": 3, "Meta": {"Duration": 3.967, "Fps": 30.0, "FadeInTime": 0.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 32, "TotalSegmentCount": 171, "TotalPointCount": 453, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 1, 0.911, -30, 1.822, -30, 2.733, -30, 0, 3.967, -30]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, -30, 1, 0.911, -30, 1.822, -30, 2.733, -30, 0, 3.967, -30]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0, 0.533, 0, 1, 1.1, 0, 1.667, 0, 2.233, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, -30, 1, 0.156, -30, 0.311, -30, 0.467, -30, 1, 0.478, -30, 0.489, 30, 0.5, 30, 1, 0.511, 30, 0.522, 30, 0.533, 30, 1, 1.089, 30, 1.644, 30, 2.2, 30, 1, 2.211, 30, 2.222, 30, 2.233, 30, 1, 2.4, 30, 2.567, -30, 2.733, -30, 0, 3.967, -30]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 0, 0.533, 0, 1, 0.556, 0, 0.578, 0, 0.6, 0, 1, 0.644, 0, 0.689, 0.3, 0.733, 0.3, 1, 0.8, 0.3, 0.867, -0.07, 0.933, -0.07, 1, 1.144, -0.07, 1.356, -0.07, 1.567, -0.07, 1, 1.789, -0.07, 2.011, 0, 2.233, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0.6, 0, 0.6, 0.6, 1, 0.711, 0.6, 0.822, -0.07, 0.933, -0.07, 1, 1.144, -0.07, 1.356, -0.07, 1.567, -0.07, 0, 3.967, -0.07]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, -0.05, 0, 0.6, -0.05, 1, 0.644, -0.05, 0.689, -0.2, 0.733, -0.2, 1, 0.8, -0.2, 0.867, -0.05, 0.933, -0.05, 1, 1.144, -0.05, 1.356, -0.05, 1.567, -0.05, 0, 3.967, -0.05]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0.75, 0, 0.6, 0.75, 1, 0.711, 0.75, 0.822, -0.07, 0.933, -0.07, 1, 1.144, -0.07, 1.356, -0.07, 1.567, -0.07, 0, 3.967, -0.07]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, -0.2, 0, 0.5, -0.2, 1, 0.511, -0.2, 0.522, -0.19, 0.533, -0.19, 1, 1.089, -0.19, 1.644, -0.2, 2.2, -0.2, 1, 2.211, -0.2, 2.222, -0.19, 2.233, -0.19, 0, 3.967, -0.19]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, -0.2, 0, 0.5, -0.2, 1, 0.511, -0.2, 0.522, -0.19, 0.533, -0.19, 1, 1.089, -0.19, 1.644, -0.2, 2.2, -0.2, 1, 2.211, -0.2, 2.222, -0.19, 2.233, -0.19, 0, 3.967, -0.19]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -3, 0, 0.5, -3, 1, 0.511, -3, 0.522, -3, 0.533, -3, 1, 1.089, -3, 1.644, -3, 2.2, -3, 1, 2.211, -3, 2.222, -3, 2.233, -3, 0, 3.967, -3]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -2, 0, 0.5, -2, 1, 0.511, -2, 0.522, -2, 0.533, -2, 1, 1.089, -2, 1.644, -2, 2.2, -2, 1, 2.211, -2, 2.222, -2, 2.233, -2, 0, 3.967, -2]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 28.5, 0, 0.533, 28.5, 1, 0.667, 28.5, 0.8, 5, 0.933, 5, 1, 1.144, 5, 1.356, 5, 1.567, 5, 1, 1.789, 5, 2.011, 28.5, 2.233, 28.5, 0, 3.967, 28.5]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 1, 0, 0.533, 1, 1, 0.667, 1, 0.8, 0, 0.933, 0, 1, 1.144, 0, 1.356, 0, 1.567, 0, 1, 1.789, 0, 2.011, 1, 2.233, 1, 0, 3.967, 1]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 1.3, 0, 0.533, 1.3, 1, 0.667, 1.3, 0.8, 18, 0.933, 18, 1, 1.144, 18, 1.356, 18, 1.567, 18, 1, 1.789, 18, 2.011, 1.3, 2.233, 1.3, 0, 3.967, 1.3]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 0.933, 0, 1, 1.144, 0, 1.356, 0, 1.567, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -30, 0, 0.533, -30, 1, 0.667, -30, 0.8, -30, 0.933, -30, 1, 1.144, -30, 1.356, -30, 1.567, -30, 1, 1.789, -30, 2.011, -30, 2.233, -30, 0, 3.967, -30]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 1, 1, 0.122, 1, 0.244, 1, 0.367, 1, 1, 0.411, 1, 0.456, 1, 0.5, 1, 1, 0.511, 1, 0.522, -1, 0.533, -1, 1, 0.667, -1, 0.8, -1, 0.933, -1, 1, 1.144, -1, 1.356, -1, 1.567, -1, 1, 1.767, -1, 1.967, -1, 2.167, -1, 1, 2.178, -1, 2.189, 1, 2.2, 1, 1, 2.211, 1, 2.222, 1, 2.233, 1, 1, 2.4, 1, 2.567, 1, 2.733, 1, 0, 3.967, 1]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.411, 0, 0.456, 0, 0.5, 0, 1, 0.511, 0, 0.522, 0, 0.533, 0, 1, 1.089, 0, 1.644, 0, 2.2, 0, 1, 2.211, 0, 2.222, 0, 2.233, 0, 1, 2.4, 0, 2.567, 0, 2.733, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 1, 1, 0.122, 1, 0.244, 1, 0.367, 1, 1, 0.411, 1, 0.456, -0.7, 0.5, -0.7, 1, 0.511, -0.7, 0.522, -0.7, 0.533, -0.7, 1, 1.089, -0.7, 1.644, -0.7, 2.2, -0.7, 1, 2.211, -0.7, 2.222, -0.7, 2.233, -0.7, 1, 2.4, -0.7, 2.567, 1, 2.733, 1, 0, 3.967, 1]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 1, 1, 0.122, 1, 0.244, 1, 0.367, 1, 1, 0.411, 1, 0.456, -0.7, 0.5, -0.7, 1, 0.511, -0.7, 0.522, -0.7, 0.533, -0.7, 1, 1.089, -0.7, 1.644, -0.7, 2.2, -0.7, 1, 2.211, -0.7, 2.222, -0.7, 2.233, -0.7, 1, 2.4, -0.7, 2.567, 1, 2.733, 1, 0, 3.967, 1]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.411, 0, 0.456, 0, 0.5, 0, 1, 0.511, 0, 0.522, 0, 0.533, 0, 1, 1.089, 0, 1.644, 0, 2.2, 0, 1, 2.211, 0, 2.222, 0, 2.233, 0, 1, 2.4, 0, 2.567, 0, 2.733, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.411, 0, 0.456, 0, 0.5, 0, 1, 0.511, 0, 0.522, 0, 0.533, 0, 1, 1.089, 0, 1.644, 0, 2.2, 0, 1, 2.211, 0, 2.222, 0, 2.233, 0, 1, 2.4, 0, 2.567, 0, 2.733, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.411, 0, 0.456, 0, 0.5, 0, 1, 0.511, 0, 0.522, 0, 0.533, 0, 1, 1.089, 0, 1.644, 0, 2.2, 0, 1, 2.211, 0, 2.222, 0, 2.233, 0, 1, 2.4, 0, 2.567, 0, 2.733, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.411, 0, 0.456, 0, 0.5, 0, 1, 0.511, 0, 0.522, 0, 0.533, 0, 1, 1.089, 0, 1.644, 0, 2.2, 0, 1, 2.211, 0, 2.222, 0, 2.233, 0, 1, 2.4, 0, 2.567, 0, 2.733, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.411, 0, 0.456, 0, 0.5, 0, 1, 0.511, 0, 0.522, 0, 0.533, 0, 1, 1.089, 0, 1.644, 0, 2.2, 0, 1, 2.211, 0, 2.222, 0, 2.233, 0, 1, 2.4, 0, 2.567, 0, 2.733, 0, 0, 3.967, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, -30, 1, 0.122, -30, 0.244, -30, 0.367, -30, 1, 0.411, -30, 0.456, -30, 0.5, -30, 1, 0.511, -30, 0.522, -30, 0.533, -30, 1, 0.578, -30, 0.622, -30, 0.667, -30, 1, 1.178, -30, 1.689, -30, 2.2, -30, 1, 2.211, -30, 2.222, -30, 2.233, -30, 1, 2.4, -30, 2.567, -30, 2.733, -30, 0, 3.967, -30]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, -1, 1, 0.911, -1, 1.822, -1, 2.733, -1, 0, 3.967, -1]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, -1, 1, 0.911, -1, 1.822, -1, 2.733, -1, 0, 3.967, -1]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 1, 1, 0.911, 1, 1.822, 1, 2.733, 1, 0, 3.967, 1]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, -1, 1, 0.156, -1, 0.311, -1, 0.467, -1, 1, 0.489, -1, 0.511, -0.349, 0.533, -0.2, 1, 0.667, 0.693, 0.8, 1, 0.933, 1, 1, 1.144, 1, 1.356, 1, 1.567, 1, 1, 1.778, 1, 1.989, -1, 2.2, -1, 1, 2.211, -1, 2.222, -1, 2.233, -1, 1, 2.4, -1, 2.567, -1, 2.733, -1, 0, 3.967, -1]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, -0.9, 0, 0.533, -0.9, 1, 0.667, -0.9, 0.8, 0.6, 0.933, 0.6, 1, 0.989, 0.6, 1.044, 0, 1.1, 0, 1, 1.256, 0, 1.411, 0, 1.567, 0, 1, 1.789, 0, 2.011, -0.9, 2.233, -0.9, 0, 3.967, -0.9]}]}