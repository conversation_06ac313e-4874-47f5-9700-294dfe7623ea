{"Version": 3, "Meta": {"Duration": 5.9, "Fps": 30.0, "FadeInTime": 0.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 24, "TotalSegmentCount": 165, "TotalPointCount": 455, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param", "Segments": [0, 30, 0, 0.3, 30, 1, 1.789, 30, 3.278, -30, 4.767, -30, 1, 4.778, -30, 4.791, -30, 4.8, -24, 1, 4.811, -16.927, 4.822, 30, 4.833, 30, 0, 5.9, 30]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, -30, 0, 5.9, -30]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, -30, 1, 0.078, -30, 0.156, -2.23, 0.233, 27.42, 1, 0.24, 30, 0.256, 30, 0.267, 30, 1, 0.378, 30, 0.489, 30, 0.6, 30, 1, 0.622, 30, 0.644, 30, 0.667, 30, 1, 1.956, 30, 3.244, 30, 4.533, 30, 1, 4.678, 30, 4.822, 30, 4.967, 30, 1, 4.978, 30, 4.989, 27.42, 5, 27.42, 0, 5.9, 27.42]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 10, 0, 0.233, 10, 1, 0.244, 10, 0.256, 1, 0.267, 1, 1, 0.378, 1, 0.489, 4, 0.6, 4, 1, 1.911, 4, 3.222, 4, 4.533, 4, 1, 4.678, 4, 4.822, 1, 4.967, 1, 1, 4.978, 1, 4.989, 10, 5, 10, 0, 5.9, 10]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 10, 0, 0.233, 10, 1, 0.244, 10, 0.256, 1, 0.267, 1, 1, 0.378, 1, 0.489, 4, 0.6, 4, 1, 1.911, 4, 3.222, 4, 4.533, 4, 1, 4.678, 4, 4.822, 1, 4.967, 1, 1, 4.978, 1, 4.989, 10, 5, 10, 0, 5.9, 10]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 28.5, 0, 0.233, 28.5, 1, 0.244, 28.5, 0.256, 28.5, 0.267, 28.5, 1, 0.378, 28.5, 0.489, 4, 0.6, 4, 1, 0.644, 4, 0.689, 4, 0.733, 4, 1, 0.878, 4, 1.022, 4, 1.167, 4, 1, 1.189, 4, 1.211, 4, 1.233, 4, 1, 2.333, 4, 3.433, 4, 4.533, 4, 1, 4.678, 4, 4.822, 28.5, 4.967, 28.5, 1, 4.978, 28.5, 4.989, 28.5, 5, 28.5, 0, 5.9, 28.5]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 1, 0, 0.233, 1, 1, 0.244, 1, 0.256, 1, 0.267, 1, 1, 0.378, 1, 0.489, 0, 0.6, 0, 1, 0.644, 0, 0.689, 0, 0.733, 0, 1, 0.878, 0, 1.022, 0, 1.167, 0, 1, 1.189, 0, 1.211, 0, 1.233, 0, 1, 1.356, 0, 1.478, -30, 1.6, -30, 1, 1.744, -30, 1.889, -30, 2.033, -30, 1, 2.067, -30, 2.1, -30, 2.133, -30, 1, 2.278, -30, 2.422, 30, 2.567, 30, 1, 2.689, 30, 2.811, 30, 2.933, 30, 1, 2.956, 30, 2.978, 30, 3, 30, 1, 3.144, 30, 3.289, -30, 3.433, -30, 1, 3.578, -30, 3.722, -30, 3.867, -30, 1, 3.9, -30, 3.933, -30, 3.967, -30, 1, 4.111, -30, 4.256, 30, 4.4, 30, 1, 4.444, 30, 4.489, 30, 4.533, 30, 1, 4.678, 30, 4.822, 1, 4.967, 1, 1, 4.978, 1, 4.989, 1, 5, 1, 0, 5.9, 1]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0.6, 0, 0.233, 0.6, 1, 0.244, 0.6, 0.256, 1.355, 0.267, -0.13, 1, 0.378, -14.978, 0.489, -30, 0.6, -30, 1, 0.644, -30, 0.689, -30, 0.733, -30, 1, 0.878, -30, 1.022, -30, 1.167, -30, 1, 1.189, -30, 1.211, -30, 1.233, -30, 1, 1.356, -30, 1.478, -30, 1.6, -30, 1, 1.744, -30, 1.889, -30, 2.033, -30, 1, 2.067, -30, 2.1, -30, 2.133, -30, 1, 2.278, -30, 2.422, -30, 2.567, -30, 1, 2.689, -30, 2.811, -30, 2.933, -30, 1, 2.956, -30, 2.978, -30, 3, -30, 1, 3.144, -30, 3.289, -30, 3.433, -30, 1, 3.578, -30, 3.722, -30, 3.867, -30, 1, 3.9, -30, 3.933, -30, 3.967, -30, 1, 4.111, -30, 4.256, -30, 4.4, -30, 1, 4.444, -30, 4.489, -30, 4.533, -30, 1, 4.678, -30, 4.822, -16.976, 4.967, -0.13, 1, 4.978, 1.166, 4.989, 0.6, 5, 0.6, 0, 5.9, 0.6]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 0.233, 0, 1, 0.244, 0, 0.256, 0, 0.267, 0, 1, 0.378, 0, 0.489, 0, 0.6, 0, 1, 0.644, 0, 0.689, 0, 0.733, 0, 1, 0.878, 0, 1.022, 0, 1.167, 0, 1, 1.189, 0, 1.211, 0, 1.233, 0, 1, 1.356, 0, 1.478, -30, 1.6, -30, 1, 1.744, -30, 1.889, -30, 2.033, -30, 1, 2.067, -30, 2.1, -30, 2.133, -30, 1, 2.278, -30, 2.422, 30, 2.567, 30, 1, 2.689, 30, 2.811, 30, 2.933, 30, 1, 2.956, 30, 2.978, 30, 3, 30, 1, 3.144, 30, 3.289, -30, 3.433, -30, 1, 3.578, -30, 3.722, -30, 3.867, -30, 1, 3.9, -30, 3.933, -30, 3.967, -30, 1, 4.111, -30, 4.256, 30, 4.4, 30, 1, 4.444, 30, 4.489, 30, 4.533, 30, 1, 4.678, 30, 4.822, 0, 4.967, 0, 1, 4.978, 0, 4.989, 0, 5, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 30, 0, 0.233, 30, 1, 0.244, 30, 0.256, 30, 0.267, 30, 1, 0.378, 30, 0.489, 30, 0.6, 30, 1, 0.644, 30, 0.689, 30, 0.733, 30, 1, 0.878, 30, 1.022, 30, 1.167, 30, 1, 1.189, 30, 1.211, 30, 1.233, 30, 1, 1.356, 30, 1.478, 30, 1.6, 30, 1, 1.744, 30, 1.889, 30, 2.033, 30, 1, 2.067, 30, 2.1, 30, 2.133, 30, 1, 2.278, 30, 2.422, 30, 2.567, 30, 1, 2.689, 30, 2.811, 30, 2.933, 30, 1, 2.956, 30, 2.978, 30, 3, 30, 1, 3.144, 30, 3.289, 30, 3.433, 30, 1, 3.578, 30, 3.722, 30, 3.867, 30, 1, 3.9, 30, 3.933, 30, 3.967, 30, 1, 4.111, 30, 4.256, 30, 4.4, 30, 1, 4.444, 30, 4.489, 30, 4.533, 30, 1, 4.678, 30, 4.822, 30, 4.967, 30, 1, 4.978, 30, 4.989, 30, 5, 30, 0, 5.9, 30]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 1, 1, 0.078, 1, 0.156, 1, 0.233, 1, 1, 0.244, 1, 0.256, -1, 0.267, -1, 1, 0.278, -1, 0.289, -1, 0.3, -1, 1, 1.856, -1, 3.411, -1, 4.967, -1, 1, 4.978, -1, 4.989, 1, 5, 1, 1, 5.078, 1, 5.156, 1, 5.233, 1, 0, 5.9, 1]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0.086, 0.233, 0.19, 1, 0.244, 0.205, 0.256, 0.2, 0.267, 0.2, 1, 1.844, 0.2, 3.422, 0.197, 5, 0.19, 1, 5.078, 0.19, 5.156, 0, 5.233, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 1, 1, 0.078, 1, 0.156, 0.272, 0.233, -0.53, 1, 0.244, -0.645, 0.256, -0.6, 0.267, -0.6, 1, 1.844, -0.6, 3.422, -0.579, 5, -0.53, 1, 5.078, -0.528, 5.156, 1, 5.233, 1, 0, 5.9, 1]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 1, 1, 0.078, 1, 0.156, 0.272, 0.233, -0.53, 1, 0.244, -0.645, 0.256, -0.6, 0.267, -0.6, 1, 1.844, -0.6, 3.422, -0.579, 5, -0.53, 1, 5.078, -0.528, 5.156, 1, 5.233, 1, 0, 5.9, 1]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0.086, 0.233, 0.19, 1, 0.244, 0.205, 0.256, 0.2, 0.267, 0.2, 1, 1.844, 0.2, 3.422, 0.197, 5, 0.19, 1, 5.078, 0.19, 5.156, 0, 5.233, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 1, 1.744, 0, 3.489, 0, 5.233, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 1.744, 0, 3.489, 0, 5.233, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.078, 0, 0.156, -0.151, 0.233, -0.29, 1, 0.244, -0.31, 0.256, -0.3, 0.267, -0.3, 1, 1.844, -0.3, 3.422, -0.297, 5, -0.29, 1, 5.078, -0.29, 5.156, 0, 5.233, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 1, 0.078, 0, 0.156, -0.151, 0.233, -0.29, 1, 0.244, -0.31, 0.256, -0.3, 0.267, -0.3, 1, 1.844, -0.3, 3.422, -0.297, 5, -0.29, 1, 5.078, -0.29, 5.156, 0, 5.233, 0, 0, 5.9, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, -30, 1, 0.222, -30, 0.444, -30, 0.667, -30, 0, 5.9, -30]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, -1, 0, 5.9, -1]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, -1, 0, 5.9, -1]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 1, 0, 5.9, 1]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, -1, 0, 5.9, -1]}]}