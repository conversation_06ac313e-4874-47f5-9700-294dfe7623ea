{"Version": 3, "Meta": {"Duration": 4.5, "Fps": 30.0, "FadeInTime": 0.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 25, "TotalSegmentCount": 140, "TotalPointCount": 379, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 4.5, -30]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, -30, 0, 4.5, -30]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 30, 0, 4.5, 30]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.522, 1, 0.544, 1, 0.567, 1, 1, 0.578, 1, 0.589, -1, 0.6, -1, 1, 0.867, -1, 1.133, -1, 1.4, -1, 1, 2.078, -1, 2.756, 1, 3.433, 1, 1, 3.556, 1, 3.678, 1, 3.8, 1, 0, 4.5, 1]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.522, 0, 0.544, 0.3, 0.567, 0.3, 1, 1.522, 0.3, 2.478, 0.3, 3.433, 0.3, 1, 3.556, 0.3, 3.678, 0, 3.8, 0, 0, 4.5, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.522, 1, 0.544, -0.2, 0.567, -0.2, 1, 1.522, -0.2, 2.478, -0.2, 3.433, -0.2, 1, 3.556, -0.2, 3.678, 1, 3.8, 1, 0, 4.5, 1]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.522, 1, 0.544, -0.2, 0.567, -0.2, 1, 1.522, -0.2, 2.478, -0.2, 3.433, -0.2, 1, 3.556, -0.2, 3.678, 1, 3.8, 1, 0, 4.5, 1]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.522, 0, 0.544, 0.3, 0.567, 0.3, 1, 1.522, 0.3, 2.478, 0.3, 3.433, 0.3, 1, 3.556, 0.3, 3.678, 0, 3.8, 0, 0, 4.5, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.522, 0, 0.544, 0, 0.567, 0, 1, 1.522, 0, 2.478, 0, 3.433, 0, 1, 3.556, 0, 3.678, 0, 3.8, 0, 0, 4.5, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 1.478, 0, 2.456, 0, 3.433, 0, 1, 3.556, 0, 3.678, 0, 3.8, 0, 0, 4.5, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.522, 0, 0.544, 0.2, 0.567, 0.2, 1, 1.522, 0.2, 2.478, 0.2, 3.433, 0.2, 1, 3.556, 0.2, 3.678, 0, 3.8, 0, 0, 4.5, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.522, 0, 0.544, 0.2, 0.567, 0.2, 1, 1.522, 0.2, 2.478, 0.2, 3.433, 0.2, 1, 3.556, 0.2, 3.678, 0, 3.8, 0, 0, 4.5, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, -30, 0, 4.5, -30]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, -1, 1, 0.156, -1, 0.311, -0.3, 0.467, -0.3, 1, 0.478, -0.3, 0.489, -1, 0.5, -1, 1, 0.522, -1, 0.544, -0.897, 0.567, -0.3, 1, 0.578, -0.001, 0.589, 1, 0.6, 1, 1, 1, 1, 1.4, 1, 1.8, 1, 1, 2.2, 1, 2.6, 1, 3, 1, 1, 3.111, 1, 3.222, 1, 3.333, 1, 1, 3.356, 1, 3.378, 1, 3.4, 1, 1, 3.411, 1, 3.422, -1, 3.433, -1, 0, 4.5, -1]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, -0.4, 0, 0.633, -0.4, 1, 0.689, -0.4, 0.744, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.5, 0, 1.6, -0.6, 1.7, -0.6, 1, 1.733, -0.6, 1.767, -0.6, 1.8, -0.6, 1, 2.2, -0.6, 2.6, -0.6, 3, -0.6, 1, 3.056, -0.6, 3.111, -0.6, 3.167, -0.6, 1, 3.222, -0.6, 3.278, -0.45, 3.333, -0.45, 0, 4.5, -0.45]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -0.4, 0, 0.633, -0.4, 1, 0.689, -0.4, 0.744, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.5, 0, 1.6, -0.6, 1.7, -0.6, 1, 1.733, -0.6, 1.767, -0.6, 1.8, -0.6, 1, 2.2, -0.6, 2.6, -0.6, 3, -0.6, 1, 3.056, -0.6, 3.111, -0.6, 3.167, -0.6, 1, 3.222, -0.6, 3.278, -0.45, 3.333, -0.45, 0, 4.5, -0.45]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 1, 0, 0.567, 1, 1, 0.644, 1, 0.722, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.5, 0, 1.6, -0.2, 1.7, -0.2, 1, 1.733, -0.2, 1.767, -0.2, 1.8, -0.2, 1, 2.2, -0.2, 2.6, -0.2, 3, -0.2, 1, 3.144, -0.2, 3.289, 1, 3.433, 1, 0, 4.5, 1]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 1, 0, 0.567, 1, 1, 0.644, 1, 0.722, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.5, 0, 1.6, -0.2, 1.7, -0.2, 1, 1.733, -0.2, 1.767, -0.2, 1.8, -0.2, 1, 2.2, -0.2, 2.6, -0.2, 3, -0.2, 1, 3.144, -0.2, 3.289, 1, 3.433, 1, 0, 4.5, 1]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0.9, 0, 0.567, 0.9, 1, 0.644, 0.9, 0.722, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0, 1, 0, 1, 1.133, 0, 1.267, 0.95, 1.4, 0.95, 1, 1.5, 0.95, 1.6, 0.2, 1.7, 0.2, 1, 1.733, 0.2, 1.767, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.144, 0.2, 3.289, 0.9, 3.433, 0.9, 0, 4.5, 0.9]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0.9, 0, 0.567, 0.9, 1, 0.644, 0.9, 0.722, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0, 1, 0, 1, 1.133, 0, 1.267, 0.95, 1.4, 0.95, 1, 1.5, 0.95, 1.6, 0.2, 1.7, 0.2, 1, 1.733, 0.2, 1.767, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.144, 0.2, 3.289, 0.9, 3.433, 0.9, 0, 4.5, 0.9]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 1.4, 0, 1, 1.5, 0, 1.6, -0.5, 1.7, -0.5, 1, 1.733, -0.5, 1.767, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 0, 4.5, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, -0.9, 0, 1.4, -0.9, 1, 1.511, -0.9, 1.622, 0.02, 1.733, 0.02, 1, 1.756, 0.02, 1.778, 0.02, 1.8, 0.02, 1, 2.2, 0.02, 2.6, 0.02, 3, 0.02, 1, 3.122, 0.02, 3.244, 1, 3.367, 1, 0, 4.5, 1]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, -1, 1, 0.167, -1, 0.333, -1, 0.5, -1, 1, 0.522, -1, 0.544, -1, 0.567, -1, 1, 0.578, -1, 0.589, 1, 0.6, 1, 1, 0.967, 1, 1.333, 1, 1.7, 1, 1, 2.256, 1, 2.811, 1, 3.367, 1, 1, 3.378, 1, 3.389, -1, 3.4, -1, 1, 3.411, -1, 3.422, -1, 3.433, -1, 0, 4.5, -1]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 1, 1, 0.578, 1, 1.156, -1, 1.733, -1, 1, 1.756, -1, 1.778, -1, 1.8, -1, 1, 2.2, -1, 2.6, -1, 3, -1, 0, 4.5, -1]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, -1, 0, 4.5, -1]}]}