#!/bin/bash

# TPRobotAPI 自启动安装脚本

echo "正在安装 TPRobotAPI 自启动服务..."

# 获取当前目录
CURRENT_DIR=$(pwd)
SCRIPT_PATH="$CURRENT_DIR/autostart_tprobotapi.sh"
DESKTOP_FILE="$CURRENT_DIR/tprobotapi.desktop"

# 检查文件是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "错误: 找不到 autostart_tprobotapi.sh 文件"
    exit 1
fi

if [ ! -f "$DESKTOP_FILE" ]; then
    echo "错误: 找不到 tprobotapi.desktop 文件"
    exit 1
fi

# 设置脚本可执行权限
chmod +x "$SCRIPT_PATH"
echo "已设置脚本可执行权限"

# 创建自启动目录（如果不存在）
AUTOSTART_DIR="$HOME/.config/autostart"
mkdir -p "$AUTOSTART_DIR"

# 复制desktop文件到自启动目录
cp "$DESKTOP_FILE" "$AUTOSTART_DIR/"
echo "已复制desktop文件到自启动目录: $AUTOSTART_DIR"

# 验证安装
if [ -f "$AUTOSTART_DIR/tprobotapi.desktop" ]; then
    echo "✅ 自启动安装成功！"
    echo ""
    echo "服务将在下次重启后自动启动"
    echo "日志文件位置: /home/<USER>/Desktop/tprobotapi_v2/logs/autostart.log"
    echo ""
    echo "手动管理命令："
    echo "  启动: $SCRIPT_PATH"
    echo "  停止: kill \$(cat /tmp/tprobotapi.pid)"
    echo "  查看日志: tail -f /home/<USER>/Desktop/tprobotapi_v2/logs/autostart.log"
    echo ""
    echo "如需卸载自启动，删除文件: $AUTOSTART_DIR/tprobotapi.desktop"
else
    echo "❌ 自启动安装失败"
    exit 1
fi
