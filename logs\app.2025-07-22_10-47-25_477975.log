2025-07-22 10:47:25.474 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-22 10:47:25.478 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-22 10:47:25.479 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-22 10:47:25.480 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-22 10:47:25.480 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-22 10:47:25.481 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-22 10:47:25.482 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-22 10:47:25.483 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-22 10:47:25.484 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-22 10:47:25.484 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-22 10:47:25.485 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-22 10:47:25.485 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-22 10:47:25.486 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-22 10:47:25.488 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-22 10:47:25.489 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-22 10:47:25.489 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-22 10:47:25.568 | INFO     | __main__:<module>:765 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-22 10:47:25.569 | INFO     | __main__:<module>:777 - 启动集成服务器...
2025-07-22 10:47:25.570 | INFO     | __main__:<module>:778 - - HTTP/API 服务: http://localhost:8000
2025-07-22 10:47:25.570 | INFO     | __main__:<module>:779 - - Live2D 界面: http://localhost:8000/live2d
2025-07-22 10:47:25.570 | INFO     | __main__:<module>:780 - - Live2D WebSocket: ws://localhost:8002
2025-07-22 10:47:25.571 | INFO     | __main__:<module>:781 - - 控制面板: http://localhost:8000
2025-07-22 10:47:25.628 | INFO     | __main__:start_live2d_websocket_server:624 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-22 10:47:25.632 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-22 10:47:25.633 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-22 10:47:25.633 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-22 10:47:25.633 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-22 10:47:25.633 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-22 10:47:25.634 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-22 10:47:25.634 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-22 10:47:25.634 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-22 10:47:25.634 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-22 10:47:25.634 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-22 10:47:25.635 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-22 10:47:25.635 | INFO     | __main__:startup_event:566 - 应用启动完成
2025-07-22 10:47:25.635 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-22 10:47:25.636 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-22 10:47:25.636 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-22 10:47:28.573 | INFO     | __main__:browser_automation:639 - SSH环境检测: 否
2025-07-22 10:47:28.573 | INFO     | __main__:browser_automation:640 - 当前环境变量 DISPLAY: 未设置
2025-07-22 10:47:28.574 | INFO     | __main__:browser_automation:649 - 已设置 DISPLAY=:0
2025-07-22 10:47:28.574 | INFO     | __main__:browser_automation:651 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-22 10:47:28.613 | WARNING  | __main__:browser_automation:681 - 未找到 epiphany-browser
2025-07-22 10:47:28.613 | INFO     | __main__:browser_automation:686 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-22 10:47:28.989 | INFO     | __main__:browser_automation:690 - 系统默认浏览器启动成功
2025-07-22 10:47:28.990 | INFO     | __main__:browser_automation:702 - 等待浏览器打开...
2025-07-22 10:47:30.417 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:8945] 在8002端口，当前连接数: 1
2025-07-22 10:47:31.006 | INFO     | __main__:browser_automation:711 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-22 10:47:31.623 | INFO     | __main__:browser_automation:718 - 按下F11键进入全屏模式
2025-07-22 10:47:32.738 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-22 10:47:32.846 | INFO     | __main__:browser_automation:734 - 已将鼠标移至屏幕边缘
2025-07-22 10:48:25.660 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 10:48:46.710 | INFO     | __main__:handle_live2d_client:619 - Live2D客户端 [127.0.0.1:8945] 断开连接，剩余连接数: 0
2025-07-22 10:48:47.177 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:9411] 在8002端口，当前连接数: 1
2025-07-22 10:49:25.653 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 10:50:25.662 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:01:14.056 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-22 11:01:14.059 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-22 11:01:14.059 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-22 11:01:14.060 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-22 11:01:14.061 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-22 11:01:14.062 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-22 11:01:14.062 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-22 11:01:14.063 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-22 11:01:14.063 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-22 11:01:14.064 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-22 11:01:14.064 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-22 11:01:14.064 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-22 11:01:14.066 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-22 11:01:14.067 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-22 11:01:14.067 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-22 11:01:14.068 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-22 11:01:14.126 | INFO     | __main__:<module>:765 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-22 11:01:14.126 | INFO     | __main__:<module>:777 - 启动集成服务器...
2025-07-22 11:01:14.127 | INFO     | __main__:<module>:778 - - HTTP/API 服务: http://localhost:8000
2025-07-22 11:01:14.127 | INFO     | __main__:<module>:779 - - Live2D 界面: http://localhost:8000/live2d
2025-07-22 11:01:14.127 | INFO     | __main__:<module>:780 - - Live2D WebSocket: ws://localhost:8002
2025-07-22 11:01:14.127 | INFO     | __main__:<module>:781 - - 控制面板: http://localhost:8000
2025-07-22 11:01:14.164 | INFO     | __main__:start_live2d_websocket_server:624 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-22 11:01:14.167 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-22 11:01:14.167 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-22 11:01:14.167 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-22 11:01:14.168 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-22 11:01:14.168 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-22 11:01:14.168 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-22 11:01:14.168 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-22 11:01:14.168 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-22 11:01:14.168 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-22 11:01:14.169 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-22 11:01:14.169 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-22 11:01:14.169 | INFO     | __main__:startup_event:566 - 应用启动完成
2025-07-22 11:01:14.169 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-22 11:01:14.170 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-22 11:01:14.170 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-22 11:01:17.141 | INFO     | __main__:browser_automation:639 - SSH环境检测: 否
2025-07-22 11:01:17.142 | INFO     | __main__:browser_automation:640 - 当前环境变量 DISPLAY: 未设置
2025-07-22 11:01:17.142 | INFO     | __main__:browser_automation:649 - 已设置 DISPLAY=:0
2025-07-22 11:01:17.143 | INFO     | __main__:browser_automation:651 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-22 11:01:17.167 | WARNING  | __main__:browser_automation:681 - 未找到 epiphany-browser
2025-07-22 11:01:17.168 | INFO     | __main__:browser_automation:686 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-22 11:01:17.426 | INFO     | __main__:browser_automation:690 - 系统默认浏览器启动成功
2025-07-22 11:01:17.427 | INFO     | __main__:browser_automation:702 - 等待浏览器打开...
2025-07-22 11:01:18.770 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:13427] 在8002端口，当前连接数: 1
2025-07-22 11:01:19.436 | INFO     | __main__:browser_automation:711 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-22 11:01:20.061 | INFO     | __main__:browser_automation:718 - 按下F11键进入全屏模式
2025-07-22 11:01:21.177 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-22 11:01:21.286 | INFO     | __main__:browser_automation:734 - 已将鼠标移至屏幕边缘
2025-07-22 11:01:50.857 | INFO     | __main__:handle_live2d_client:619 - Live2D客户端 [127.0.0.1:13427] 断开连接，剩余连接数: 0
2025-07-22 11:01:51.306 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:13647] 在8002端口，当前连接数: 1
2025-07-22 11:02:06.708 | INFO     | __main__:handle_live2d_client:619 - Live2D客户端 [127.0.0.1:13647] 断开连接，剩余连接数: 0
2025-07-22 11:02:07.150 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:13684] 在8002端口，当前连接数: 1
2025-07-22 11:02:14.179 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:03:14.194 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:03:18.474 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-22 11:03:18.475 | INFO     | __main__:websocket_endpoint:311 - 新的WebSocket连接: Address(host='127.0.0.1', port=13920)
2025-07-22 11:03:20.877 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-22 11:03:20.878 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:03:20.879 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:03:20.879 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:03:20.880 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-22 11:03:20.880 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:03:24.005 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-22 11:03:24.006 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:03:24.006 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:03:24.006 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:03:24.007 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-22 11:03:24.008 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:03:27.221 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-22 11:03:27.222 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:03:27.222 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:03:27.223 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:03:27.224 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:03:27.224 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:03:28.898 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-22 11:03:28.899 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:03:28.899 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:03:28.899 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:03:28.901 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-22 11:03:28.901 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:03:30.809 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:03:30.809 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:03:30.810 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:03:30.810 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:03:30.811 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:03:30.812 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:03:31.731 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-22 11:03:31.732 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:03:31.732 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:03:31.732 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:03:31.733 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-22 11:03:31.733 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:03:33.231 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:03:33.231 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:03:33.232 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:03:33.233 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:03:33.234 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:03:33.234 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:04:14.206 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:05:14.224 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:06:14.237 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:06:36.724 | INFO     | __main__:handle_live2d_client:619 - Live2D客户端 [127.0.0.1:13684] 断开连接，剩余连接数: 0
2025-07-22 11:06:37.271 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:14503] 在8002端口，当前连接数: 1
2025-07-22 11:06:42.004 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-22 11:06:42.005 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:06:42.005 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:06:42.005 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:06:42.006 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-22 11:06:42.007 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:06:43.708 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-22 11:06:43.709 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:06:43.709 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:06:43.709 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:06:43.709 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-22 11:06:43.711 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:06:46.180 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-22 11:06:46.181 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:06:46.181 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:06:46.182 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:06:46.182 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:06:46.182 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:06:46.876 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-22 11:06:46.876 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:06:46.876 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:06:46.876 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:06:46.878 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-22 11:06:46.878 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:06:49.575 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:06:49.576 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:06:49.576 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:06:49.576 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:06:49.577 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:06:49.577 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:06:55.685 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-22 11:06:55.685 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:06:55.685 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:06:55.685 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:06:55.687 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-22 11:06:55.687 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:06:58.641 | INFO     | __main__:handle_live2d_client:619 - Live2D客户端 [127.0.0.1:14503] 断开连接，剩余连接数: 0
2025-07-22 11:06:59.170 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:14567] 在8002端口，当前连接数: 1
2025-07-22 11:07:01.286 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-22 11:07:01.286 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:01.287 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:01.287 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:01.288 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-22 11:07:01.288 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:09.344 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-22 11:07:09.344 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:09.344 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:09.345 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:09.345 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-22 11:07:09.346 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:14.237 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:07:16.409 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:16.411 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:16.411 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:16.411 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:16.412 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:16.412 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:18.819 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-22 11:07:18.819 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:18.819 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:18.821 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:18.821 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-22 11:07:18.821 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:20.260 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:20.260 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:20.260 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:20.262 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:20.262 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:20.263 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:22.370 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:22.371 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:22.371 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:22.371 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:22.372 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:22.372 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:24.946 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:24.946 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:24.946 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:24.947 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:24.947 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:24.947 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:25.871 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-22 11:07:25.872 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:25.872 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:25.872 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:25.873 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-22 11:07:25.873 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:27.796 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-22 11:07:27.796 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:27.796 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:27.798 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:27.798 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-22 11:07:27.799 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:29.643 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:29.643 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:29.645 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:29.645 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:29.646 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:29.646 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:30.939 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:30.940 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:30.940 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:30.940 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:30.941 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:30.942 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:31.229 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:31.230 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:31.230 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:31.230 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:31.231 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:31.231 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:31.484 | INFO     | __main__:websocket_endpoint:317 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-22 11:07:31.485 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-22 11:07:31.485 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-22 11:07:31.486 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-22 11:07:31.486 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-22 11:07:31.487 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-22 11:07:33.407 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-22 11:07:33.407 | INFO     | __main__:websocket_endpoint:353 - WebSocket连接断开: Address(host='127.0.0.1', port=13920)
2025-07-22 11:07:35.043 | INFO     | __main__:handle_live2d_client:619 - Live2D客户端 [127.0.0.1:14567] 断开连接，剩余连接数: 0
2025-07-22 11:07:35.535 | INFO     | __main__:handle_live2d_client:590 - 新的Live2D客户端连接 [127.0.0.1:14600] 在8002端口，当前连接数: 1
2025-07-22 11:08:14.228 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:09:14.225 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:10:14.231 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:11:14.211 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:12:14.226 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:13:14.250 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:14:14.265 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:15:14.263 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:16:14.263 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:17:14.276 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:18:14.282 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:19:14.294 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:20:14.309 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:21:14.319 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:22:14.327 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:23:14.334 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:24:14.344 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:25:14.363 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:26:14.368 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:27:14.396 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:28:14.418 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:29:14.438 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:30:14.430 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:31:14.437 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:32:14.455 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:33:14.458 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:34:14.443 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:35:14.472 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:36:14.478 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:37:14.484 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:38:14.503 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:39:14.494 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:40:14.517 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:41:14.520 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:42:14.533 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:43:14.533 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:44:14.534 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:45:14.532 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:46:14.552 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:47:14.557 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:48:14.581 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:49:14.589 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:50:14.600 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:51:14.622 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:52:14.637 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:53:14.631 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:54:14.642 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:55:14.638 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:56:14.631 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:57:14.660 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:58:14.667 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 11:59:14.682 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:00:14.699 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:01:14.716 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:02:14.735 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:03:14.740 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:04:14.769 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:05:14.759 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:06:14.766 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:07:14.761 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:08:14.743 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:09:14.755 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:10:14.749 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:11:14.762 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:12:14.762 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:13:14.744 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:14:14.761 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:15:14.756 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:16:14.783 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:17:14.788 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:18:14.805 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:19:14.803 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:20:14.812 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:21:14.817 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:22:14.817 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:23:14.804 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:24:14.816 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:25:14.798 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:26:14.799 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:27:14.811 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-22 12:27:21.150 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-22 12:27:21.151 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-22 12:27:21.151 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-22 12:27:21.151 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-22 12:27:21.151 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-22 12:27:21.151 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-22 12:27:21.153 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-22 12:27:21.153 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-22 12:27:21.153 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-22 12:27:21.153 | INFO     | __main__:shutdown_event:576 - 应用关闭完成
2025-07-22 12:27:21.153 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-22 12:27:21.154 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-22 12:27:21.154 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
2025-07-23 09:35:00.129 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-23 09:35:00.131 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-23 09:35:00.132 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-23 09:35:00.132 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-23 09:35:00.133 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-23 09:35:00.134 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-23 09:35:00.134 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-23 09:35:00.135 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-23 09:35:00.135 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-23 09:35:00.135 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-23 09:35:00.136 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-23 09:35:00.136 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-23 09:35:00.136 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:35:00.138 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:35:00.138 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:35:00.138 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-23 09:35:00.209 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-23 09:35:00.210 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-23 09:35:00.210 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-23 09:35:00.211 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-23 09:35:00.211 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-23 09:35:00.211 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-23 09:35:00.249 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-23 09:35:00.252 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-23 09:35:00.252 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-23 09:35:00.253 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-23 09:35:00.253 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-23 09:35:00.253 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-23 09:35:00.253 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-23 09:35:00.253 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-23 09:35:00.254 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-23 09:35:00.254 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-23 09:35:00.254 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-23 09:35:00.254 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-23 09:35:00.255 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-23 09:35:00.255 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-23 09:35:00.255 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-23 09:35:00.255 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-23 09:35:03.212 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-23 09:35:03.212 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-23 09:35:03.212 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-23 09:35:03.213 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-23 09:35:03.239 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-23 09:35:03.240 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-23 09:35:03.496 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-23 09:35:03.496 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-23 09:35:04.670 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:1493] 在8002端口，当前连接数: 1
2025-07-23 09:35:05.510 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-23 09:35:06.134 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-23 09:35:07.252 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-23 09:35:07.359 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-23 09:35:40.696 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-23 09:35:40.698 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=1536)
2025-07-23 09:35:42.854 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-23 09:35:42.856 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:35:42.857 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:35:42.857 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:35:42.858 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-23 09:35:42.858 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:35:45.929 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-23 09:35:45.929 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:35:45.931 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:35:45.931 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:35:45.932 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-23 09:35:45.932 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:35:51.320 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-23 09:35:51.320 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:35:51.321 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:35:51.321 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:35:51.322 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-23 09:35:51.323 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:35:52.714 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-23 09:35:52.714 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:35:52.715 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:35:52.716 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:35:52.716 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-23 09:35:52.716 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:35:53.986 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-23 09:35:53.987 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:35:53.987 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:35:53.988 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:35:53.988 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-23 09:35:53.989 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:35:54.565 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-23 09:35:54.566 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:35:54.566 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:35:54.567 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:35:54.567 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-23 09:35:54.567 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:35:55.286 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-23 09:35:55.287 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=1536)
2025-07-23 09:35:56.444 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-23 09:35:56.444 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=1538)
2025-07-23 09:35:57.898 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 2
2025-07-23 09:35:57.898 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=1540)
2025-07-23 09:35:58.990 | ERROR    | __main__:handle_live2d_client:596 - 与Live2D客户端 [127.0.0.1:1493] 通信时出错: no close frame received or sent
2025-07-23 09:35:58.991 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:1493] 断开连接，剩余连接数: 0
2025-07-23 09:35:59.435 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:1545] 在8002端口，当前连接数: 1
2025-07-23 09:36:00.261 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:36:01.317 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-23 09:36:01.318 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:36:01.318 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:36:01.318 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:36:01.319 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-23 09:36:01.319 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:36:03.256 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-23 09:36:03.257 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:36:03.257 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:36:03.257 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:36:03.259 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-23 09:36:03.259 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:36:05.108 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-23 09:36:05.108 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:36:05.110 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:36:05.110 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:36:05.111 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-23 09:36:05.112 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:36:07.510 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-23 09:36:07.511 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-23 09:36:07.511 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-23 09:36:07.511 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-23 09:36:07.512 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-23 09:36:07.512 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-23 09:36:12.042 | ERROR    | __main__:handle_live2d_client:596 - 与Live2D客户端 [127.0.0.1:1545] 通信时出错: no close frame received or sent
2025-07-23 09:36:12.043 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:1545] 断开连接，剩余连接数: 0
2025-07-23 09:36:12.605 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:1559] 在8002端口，当前连接数: 1
2025-07-23 09:36:22.355 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 1
2025-07-23 09:36:22.356 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=1540)
2025-07-23 09:36:22.356 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-23 09:36:22.357 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=1538)
2025-07-23 09:37:00.284 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:38:00.274 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:39:00.297 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:40:00.296 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:41:00.316 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:42:00.317 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:43:00.287 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:44:00.307 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:45:00.309 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:46:00.307 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:47:00.316 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:48:00.312 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:49:00.304 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:49:00.536 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-23 09:49:00.537 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-23 09:49:00.537 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-23 09:49:00.538 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-23 09:49:00.538 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-23 09:49:00.538 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-23 09:49:00.539 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-23 09:49:00.539 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-23 09:49:00.539 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-23 09:49:00.540 | INFO     | __main__:shutdown_event:557 - 应用关闭完成
2025-07-23 09:49:00.540 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-23 09:49:00.540 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-23 09:49:00.541 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
2025-07-23 09:49:03.355 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-23 09:49:03.356 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-23 09:49:03.358 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-23 09:49:03.358 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-23 09:49:03.359 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-23 09:49:03.359 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-23 09:49:03.360 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-23 09:49:03.360 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-23 09:49:03.361 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-23 09:49:03.362 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-23 09:49:03.362 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-23 09:49:03.362 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-23 09:49:03.363 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:49:03.364 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:49:03.364 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:49:03.364 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-23 09:49:03.408 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-23 09:49:03.408 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-23 09:49:03.409 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-23 09:49:03.409 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-23 09:49:03.409 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-23 09:49:03.410 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-23 09:49:03.444 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-23 09:49:03.447 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-23 09:49:03.447 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-23 09:49:03.448 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-23 09:49:03.448 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-23 09:49:03.448 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-23 09:49:03.448 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-23 09:49:03.449 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-23 09:49:03.449 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-23 09:49:03.449 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-23 09:49:03.449 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-23 09:49:03.450 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-23 09:49:03.450 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-23 09:49:03.450 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-23 09:49:03.450 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-23 09:49:03.451 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-23 09:49:06.421 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-23 09:49:06.422 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-23 09:49:06.422 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-23 09:49:06.423 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-23 09:49:06.448 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-23 09:49:06.449 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-23 09:49:06.718 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6027] 在8002端口，当前连接数: 1
2025-07-23 09:49:06.728 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-23 09:49:06.728 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-23 09:49:07.889 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6038] 在8002端口，当前连接数: 2
2025-07-23 09:49:08.733 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-23 09:49:09.356 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-23 09:49:10.475 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-23 09:49:10.582 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-23 09:49:28.714 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:6038] 断开连接，剩余连接数: 1
2025-07-23 09:50:03.465 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:50:11.365 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-23 09:50:11.366 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-23 09:50:11.366 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-23 09:50:11.366 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-23 09:50:11.367 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-23 09:50:11.367 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-23 09:50:11.367 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-23 09:50:11.367 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-23 09:50:11.368 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-23 09:50:11.368 | INFO     | __main__:shutdown_event:557 - 应用关闭完成
2025-07-23 09:50:11.368 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-23 09:50:11.368 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-23 09:50:11.368 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
2025-07-23 09:50:17.772 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-23 09:50:17.773 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-23 09:50:17.773 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-23 09:50:17.774 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-23 09:50:17.774 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-23 09:50:17.775 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-23 09:50:17.775 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-23 09:50:17.775 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-23 09:50:17.775 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-23 09:50:17.776 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-23 09:50:17.776 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-23 09:50:17.777 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-23 09:50:17.778 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:50:17.779 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:50:17.780 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-23 09:50:17.780 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-23 09:50:17.823 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-23 09:50:17.824 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-23 09:50:17.824 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-23 09:50:17.824 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-23 09:50:17.825 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-23 09:50:17.825 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-23 09:50:17.858 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-23 09:50:17.861 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-23 09:50:17.861 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-23 09:50:17.861 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-23 09:50:17.861 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-23 09:50:17.862 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-23 09:50:17.862 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-23 09:50:17.862 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-23 09:50:17.862 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-23 09:50:17.863 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-23 09:50:17.863 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-23 09:50:17.863 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-23 09:50:17.863 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-23 09:50:17.863 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-23 09:50:17.864 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-23 09:50:17.864 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-23 09:50:18.231 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6112] 在8002端口，当前连接数: 1
2025-07-23 09:50:20.829 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-23 09:50:20.829 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-23 09:50:20.831 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-23 09:50:20.831 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-23 09:50:20.857 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-23 09:50:20.858 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-23 09:50:21.116 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-23 09:50:21.116 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-23 09:50:22.052 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6124] 在8002端口，当前连接数: 2
2025-07-23 09:50:23.123 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-23 09:50:23.745 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-23 09:50:24.856 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-23 09:50:24.967 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-23 09:51:17.870 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:51:41.581 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:6112] 断开连接，剩余连接数: 1
2025-07-23 09:52:17.870 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:53:17.880 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:54:17.891 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:55:17.876 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:56:17.890 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:57:17.897 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:58:17.927 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 09:59:17.918 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:00:17.926 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:01:17.931 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:02:17.951 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:03:17.960 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:04:17.972 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:05:17.979 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:06:17.980 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:07:18.003 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:08:18.017 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:09:18.006 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:10:18.017 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:11:18.015 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:12:18.033 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:13:18.039 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:14:18.061 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:15:18.078 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-23 10:16:13.844 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-23 10:16:13.845 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-23 10:16:13.845 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-23 10:16:13.846 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-23 10:16:13.846 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-23 10:16:13.846 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-23 10:16:13.846 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-23 10:16:13.847 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-23 10:16:13.847 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-23 10:16:13.847 | INFO     | __main__:shutdown_event:557 - 应用关闭完成
2025-07-23 10:16:13.847 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-23 10:16:13.848 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-23 10:16:13.848 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
