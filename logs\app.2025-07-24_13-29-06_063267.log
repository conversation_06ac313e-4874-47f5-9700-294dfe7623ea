2025-07-24 13:29:06.056 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:30:06.041 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:31:06.057 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:32:06.079 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:33:06.095 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:34:06.092 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:35:06.107 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:36:06.126 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:37:06.121 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:38:06.132 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:39:06.159 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:40:06.171 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:41:06.171 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:42:06.189 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:43:06.171 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:44:06.181 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:45:06.179 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:46:06.193 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:47:06.211 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:48:06.227 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:49:06.249 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:50:06.268 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:51:06.276 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:52:06.296 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:53:06.304 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:54:06.298 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:54:44.876 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-24 13:54:44.876 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-24 13:54:44.877 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-24 13:54:44.877 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-24 13:54:44.877 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-24 13:54:44.877 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-24 13:54:44.877 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-24 13:54:44.878 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-24 13:54:44.878 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-24 13:54:44.878 | INFO     | __main__:shutdown_event:557 - 应用关闭完成
2025-07-24 13:54:44.879 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-24 13:54:44.879 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-24 13:54:44.880 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
2025-07-24 13:54:49.539 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-24 13:54:49.542 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /723 -> dist\723
2025-07-24 13:54:49.542 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-24 13:54:49.542 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-24 13:54:49.544 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-24 13:54:49.544 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /huiyan -> dist\huiyan
2025-07-24 13:54:49.544 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-24 13:54:49.545 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-24 13:54:49.545 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lanse -> dist\lanse
2025-07-24 13:54:49.546 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-24 13:54:49.546 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-24 13:54:49.547 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-24 13:54:49.547 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-24 13:54:49.548 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-24 13:54:49.548 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-24 13:54:49.549 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-24 13:54:49.549 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-24 13:54:49.550 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-24 13:54:49.550 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-24 13:54:49.599 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-24 13:54:49.599 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-24 13:54:49.599 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-24 13:54:49.600 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-24 13:54:49.600 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-24 13:54:49.600 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-24 13:54:49.644 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-24 13:54:49.647 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-24 13:54:49.647 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-24 13:54:49.648 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-24 13:54:49.648 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-24 13:54:49.648 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-24 13:54:49.649 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-24 13:54:49.649 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-24 13:54:49.649 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-24 13:54:49.650 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-24 13:54:49.650 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-24 13:54:49.650 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-24 13:54:49.650 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-24 13:54:49.650 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-24 13:54:49.651 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-24 13:54:49.651 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-24 13:54:52.607 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-24 13:54:52.608 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-24 13:54:52.609 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-24 13:54:52.610 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-24 13:54:52.648 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-24 13:54:52.648 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-24 13:54:52.987 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-24 13:54:52.988 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-24 13:54:54.378 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:5579] 在8002端口，当前连接数: 1
2025-07-24 13:54:55.000 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-24 13:54:55.612 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-24 13:54:56.726 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-24 13:54:56.831 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-24 13:55:49.673 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-24 13:56:02.177 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:5579] 断开连接，剩余连接数: 0
2025-07-24 13:56:02.603 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:9264] 在8002端口，当前连接数: 1
2025-07-24 13:56:14.438 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:9264] 断开连接，剩余连接数: 0
2025-07-24 13:56:18.607 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-24 13:56:18.608 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-24 13:56:18.608 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-24 13:56:18.608 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-24 13:56:18.609 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-24 13:56:18.609 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-24 13:56:18.609 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-24 13:56:18.609 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-24 13:56:18.610 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-24 13:56:18.610 | INFO     | __main__:shutdown_event:557 - 应用关闭完成
2025-07-24 13:56:18.610 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-24 13:56:18.610 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-24 13:56:18.610 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
2025-07-25 09:26:36.959 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-25 09:26:36.960 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /723 -> dist\723
2025-07-25 09:26:36.961 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /725_test -> dist\725_test
2025-07-25 09:26:36.962 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-25 09:26:36.962 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-25 09:26:36.962 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-25 09:26:36.964 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /huiyan -> dist\huiyan
2025-07-25 09:26:36.964 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-25 09:26:36.965 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-25 09:26:36.965 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lanse -> dist\lanse
2025-07-25 09:26:36.966 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-25 09:26:36.966 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-25 09:26:36.967 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-25 09:26:36.968 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-25 09:26:36.968 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-25 09:26:36.969 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-25 09:26:36.970 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 09:26:36.970 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 09:26:36.971 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 09:26:36.971 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-25 09:26:37.042 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-25 09:26:37.043 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-25 09:26:37.043 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-25 09:26:37.043 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-25 09:26:37.043 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-25 09:26:37.044 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-25 09:26:37.082 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-25 09:26:37.085 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-25 09:26:37.085 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-25 09:26:37.086 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-25 09:26:37.086 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-25 09:26:37.086 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-25 09:26:37.086 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-25 09:26:37.087 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-25 09:26:37.087 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-25 09:26:37.087 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-25 09:26:37.088 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-25 09:26:37.088 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-25 09:26:37.088 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-25 09:26:37.088 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-25 09:26:37.088 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-25 09:26:37.088 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-25 09:26:40.045 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-25 09:26:40.045 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-25 09:26:40.046 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-25 09:26:40.046 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-25 09:26:40.079 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-25 09:26:40.080 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-25 09:26:40.361 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-25 09:26:40.362 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-25 09:26:41.513 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3698] 在8002端口，当前连接数: 1
2025-07-25 09:26:42.365 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-25 09:26:42.990 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-25 09:26:44.111 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-25 09:26:44.221 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-25 09:27:29.960 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3698] 断开连接，剩余连接数: 0
2025-07-25 09:27:30.400 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3826] 在8002端口，当前连接数: 1
2025-07-25 09:27:31.228 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3826] 断开连接，剩余连接数: 0
2025-07-25 09:27:31.648 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3828] 在8002端口，当前连接数: 1
2025-07-25 09:27:32.346 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3828] 断开连接，剩余连接数: 0
2025-07-25 09:27:32.763 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3833] 在8002端口，当前连接数: 1
2025-07-25 09:27:37.107 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:27:41.740 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3833] 断开连接，剩余连接数: 0
2025-07-25 09:27:42.166 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3874] 在8002端口，当前连接数: 1
2025-07-25 09:27:43.908 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3874] 断开连接，剩余连接数: 0
2025-07-25 09:27:44.321 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3878] 在8002端口，当前连接数: 1
2025-07-25 09:27:45.093 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3878] 断开连接，剩余连接数: 0
2025-07-25 09:27:45.507 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3880] 在8002端口，当前连接数: 1
2025-07-25 09:27:46.191 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3880] 断开连接，剩余连接数: 0
2025-07-25 09:27:46.615 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3892] 在8002端口，当前连接数: 1
2025-07-25 09:28:37.122 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:28:50.963 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3892] 断开连接，剩余连接数: 0
2025-07-25 09:28:51.378 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4274] 在8002端口，当前连接数: 1
2025-07-25 09:28:51.599 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4274] 断开连接，剩余连接数: 0
2025-07-25 09:28:52.011 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4276] 在8002端口，当前连接数: 1
2025-07-25 09:29:31.469 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4276] 断开连接，剩余连接数: 0
2025-07-25 09:29:31.902 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4415] 在8002端口，当前连接数: 1
2025-07-25 09:29:37.128 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:29:47.216 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4415] 断开连接，剩余连接数: 0
2025-07-25 09:29:47.628 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4483] 在8002端口，当前连接数: 1
2025-07-25 09:30:37.158 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:30:40.106 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4483] 断开连接，剩余连接数: 0
2025-07-25 09:30:40.532 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4567] 在8002端口，当前连接数: 1
2025-07-25 09:31:37.158 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:32:37.171 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:33:37.160 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:34:37.182 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:35:37.206 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:36:37.232 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:37:37.238 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:38:37.253 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:39:37.282 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:40:37.295 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:41:37.302 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:42:37.329 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:43:37.316 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:44:37.336 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:45:37.352 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:46:07.486 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4567] 断开连接，剩余连接数: 0
2025-07-25 09:46:08.008 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:9968] 在8002端口，当前连接数: 1
2025-07-25 09:46:17.732 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:9968] 断开连接，剩余连接数: 0
2025-07-25 09:46:18.180 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:9978] 在8002端口，当前连接数: 1
2025-07-25 09:46:20.676 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:9978] 断开连接，剩余连接数: 0
2025-07-25 09:46:21.115 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:9981] 在8002端口，当前连接数: 1
2025-07-25 09:46:37.370 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:46:44.130 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:9981] 断开连接，剩余连接数: 0
2025-07-25 09:46:44.567 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:10017] 在8002端口，当前连接数: 1
2025-07-25 09:47:22.549 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:10017] 断开连接，剩余连接数: 0
2025-07-25 09:47:22.960 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:10045] 在8002端口，当前连接数: 1
2025-07-25 09:47:37.390 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:48:37.402 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:49:37.414 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:50:37.428 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:51:37.440 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:52:37.439 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:53:37.437 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:54:37.435 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:55:37.449 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:56:37.464 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:57:37.476 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:58:37.472 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 09:58:45.878 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:10045] 断开连接，剩余连接数: 0
2025-07-25 09:58:46.503 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6606] 在8002端口，当前连接数: 1
2025-07-25 09:58:48.758 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:6606] 断开连接，剩余连接数: 0
2025-07-25 09:58:49.240 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6620] 在8002端口，当前连接数: 1
2025-07-25 09:58:54.014 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:6620] 断开连接，剩余连接数: 0
2025-07-25 09:58:54.472 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6629] 在8002端口，当前连接数: 1
2025-07-25 09:59:03.795 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:6629] 断开连接，剩余连接数: 0
2025-07-25 09:59:04.266 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6635] 在8002端口，当前连接数: 1
2025-07-25 09:59:05.914 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:6635] 断开连接，剩余连接数: 0
2025-07-25 09:59:06.360 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:6639] 在8002端口，当前连接数: 1
2025-07-25 09:59:37.498 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:00:37.515 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:01:37.510 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:02:37.529 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:03:37.524 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:04:37.519 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:05:37.545 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:06:37.539 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:06:40.613 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:6639] 断开连接，剩余连接数: 0
2025-07-25 10:06:41.217 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2522] 在8002端口，当前连接数: 1
2025-07-25 10:06:43.137 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-25 10:06:43.138 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-25 10:06:43.138 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-25 10:06:43.138 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-25 10:06:43.139 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-25 10:06:43.139 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-25 10:06:43.139 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-25 10:06:43.140 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-25 10:06:43.140 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-25 10:06:43.140 | INFO     | __main__:shutdown_event:557 - 应用关闭完成
2025-07-25 10:06:43.141 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-25 10:06:43.141 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-25 10:06:43.141 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
2025-07-25 10:06:48.328 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-25 10:06:48.329 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /723 -> dist\723
2025-07-25 10:06:48.329 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /725_test -> dist\725_test
2025-07-25 10:06:48.330 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-25 10:06:48.330 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-25 10:06:48.331 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-25 10:06:48.332 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /huiyan -> dist\huiyan
2025-07-25 10:06:48.333 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-25 10:06:48.334 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-25 10:06:48.334 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lanse -> dist\lanse
2025-07-25 10:06:48.334 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-25 10:06:48.334 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-25 10:06:48.335 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-25 10:06:48.335 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-25 10:06:48.336 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-25 10:06:48.336 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-25 10:06:48.337 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 10:06:48.337 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 10:06:48.338 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 10:06:48.338 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-25 10:06:48.389 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-25 10:06:48.390 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-25 10:06:48.390 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-25 10:06:48.391 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-25 10:06:48.391 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-25 10:06:48.392 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-25 10:06:48.431 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-25 10:06:48.435 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-25 10:06:48.435 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-25 10:06:48.435 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-25 10:06:48.435 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-25 10:06:48.436 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-25 10:06:48.436 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-25 10:06:48.436 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-25 10:06:48.436 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-25 10:06:48.436 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-25 10:06:48.436 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-25 10:06:48.437 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-25 10:06:48.437 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-25 10:06:48.437 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-25 10:06:48.437 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-25 10:06:48.437 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-25 10:06:48.583 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2580] 在8002端口，当前连接数: 1
2025-07-25 10:06:51.393 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-25 10:06:51.393 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-25 10:06:51.393 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-25 10:06:51.393 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-25 10:06:51.428 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-25 10:06:51.429 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-25 10:06:51.762 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-25 10:06:51.762 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-25 10:06:52.747 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2601] 在8002端口，当前连接数: 2
2025-07-25 10:06:53.767 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-25 10:06:54.390 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-25 10:06:55.506 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-25 10:06:55.614 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-25 10:07:22.511 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2601] 断开连接，剩余连接数: 1
2025-07-25 10:07:23.078 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2654] 在8002端口，当前连接数: 2
2025-07-25 10:07:36.666 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2654] 断开连接，剩余连接数: 1
2025-07-25 10:07:37.126 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2672] 在8002端口，当前连接数: 2
2025-07-25 10:07:48.433 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:07:49.888 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2672] 断开连接，剩余连接数: 1
2025-07-25 10:07:50.322 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2713] 在8002端口，当前连接数: 2
2025-07-25 10:08:48.440 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:09:12.508 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2713] 断开连接，剩余连接数: 1
2025-07-25 10:09:13.149 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2985] 在8002端口，当前连接数: 2
2025-07-25 10:09:48.451 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:10:48.479 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:10:55.727 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2985] 断开连接，剩余连接数: 1
2025-07-25 10:10:56.161 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3180] 在8002端口，当前连接数: 2
2025-07-25 10:11:33.398 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2580] 断开连接，剩余连接数: 1
2025-07-25 10:11:43.333 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-25 10:11:43.333 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=3269)
2025-07-25 10:11:45.617 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3180] 断开连接，剩余连接数: 0
2025-07-25 10:11:46.039 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3271] 在8002端口，当前连接数: 1
2025-07-25 10:11:48.483 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:11:48.589 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-25 10:11:48.589 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:48.589 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:48.590 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:48.591 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-25 10:11:48.591 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:11:49.810 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-25 10:11:49.811 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:49.811 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:49.811 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:49.812 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-25 10:11:49.812 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:11:50.421 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-25 10:11:50.421 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:50.422 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:50.422 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:50.423 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-25 10:11:50.423 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:11:50.927 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:11:50.928 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:50.928 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:50.929 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:50.929 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:11:50.929 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:11:51.475 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-25 10:11:51.475 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:51.476 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:51.476 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:51.476 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-25 10:11:51.476 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:11:52.006 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-25 10:11:52.007 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:52.008 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:52.008 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:52.009 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:11:52.009 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:11:52.377 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-25 10:11:52.378 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:52.378 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:52.378 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:52.379 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-25 10:11:52.379 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:11:55.258 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3271] 断开连接，剩余连接数: 0
2025-07-25 10:11:55.700 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3287] 在8002端口，当前连接数: 1
2025-07-25 10:11:56.814 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3287] 断开连接，剩余连接数: 0
2025-07-25 10:11:57.229 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3290] 在8002端口，当前连接数: 1
2025-07-25 10:11:58.924 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-25 10:11:58.925 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:11:58.926 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:11:58.926 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:11:58.927 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-25 10:11:58.927 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:00.704 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-25 10:12:00.705 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:00.705 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:00.705 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:00.705 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-25 10:12:00.707 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:01.152 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-25 10:12:01.153 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:01.153 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:01.153 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:01.154 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-25 10:12:01.154 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:01.597 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:12:01.597 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:01.598 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:01.598 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:01.599 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:12:01.599 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:01.976 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-25 10:12:01.976 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:01.977 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:01.977 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:01.977 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-25 10:12:01.978 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:02.329 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-25 10:12:02.329 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:02.330 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:02.330 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:02.331 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-25 10:12:02.332 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:02.674 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-25 10:12:02.675 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:02.675 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:02.675 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:02.676 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:12:02.676 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:03.066 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-25 10:12:03.067 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:03.067 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:03.067 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:03.069 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-25 10:12:03.069 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:03.446 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-25 10:12:03.447 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:03.448 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:03.448 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:03.449 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-25 10:12:03.449 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:03.734 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-25 10:12:03.735 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:03.735 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:03.736 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:03.736 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-25 10:12:03.736 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:04.547 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:12:04.547 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:04.547 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:04.548 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:04.549 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:12:04.549 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:04.950 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-25 10:12:04.951 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:04.951 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:04.951 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:04.952 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-25 10:12:04.952 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:05.327 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-25 10:12:05.328 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:05.328 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:05.328 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:05.329 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-25 10:12:05.329 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:19.224 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-25 10:12:19.225 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=3269)
2025-07-25 10:12:19.597 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-25 10:12:19.598 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=3334)
2025-07-25 10:12:22.645 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-25 10:12:22.645 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=3334)
2025-07-25 10:12:23.005 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-25 10:12:23.005 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=3355)
2025-07-25 10:12:38.402 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-25 10:12:38.403 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=3355)
2025-07-25 10:12:38.759 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-25 10:12:38.759 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=3360)
2025-07-25 10:12:48.499 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:12:48.780 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-25 10:12:48.781 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:48.781 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:48.781 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:48.782 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-25 10:12:48.782 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:12:49.964 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-25 10:12:49.964 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:12:49.964 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:12:49.965 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:12:49.965 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-25 10:12:49.966 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:05.552 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3290] 断开连接，剩余连接数: 0
2025-07-25 10:13:05.965 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3402] 在8002端口，当前连接数: 1
2025-07-25 10:13:11.177 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-25 10:13:11.178 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:11.178 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:11.178 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:11.179 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-25 10:13:11.179 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:14.024 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-25 10:13:14.024 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:14.025 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:14.025 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:14.026 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:13:14.026 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:17.032 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-25 10:13:17.033 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:17.033 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:17.034 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:17.034 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-25 10:13:17.035 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:21.677 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-25 10:13:21.677 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:21.678 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:21.678 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:21.678 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:13:21.679 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:23.287 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-25 10:13:23.288 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:23.288 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:23.288 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:23.289 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-25 10:13:23.289 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:28.785 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:13:28.786 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:28.786 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:28.787 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:28.787 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:13:28.788 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:30.238 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-25 10:13:30.238 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:30.238 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:30.238 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:30.240 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-25 10:13:30.240 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:36.224 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:13:36.224 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:13:36.225 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:13:36.225 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:13:36.226 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:13:36.226 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:13:48.515 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:14:05.903 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3402] 断开连接，剩余连接数: 0
2025-07-25 10:14:06.332 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3485] 在8002端口，当前连接数: 1
2025-07-25 10:14:48.510 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:15:31.260 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3485] 断开连接，剩余连接数: 0
2025-07-25 10:15:31.684 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3595] 在8002端口，当前连接数: 1
2025-07-25 10:15:44.601 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-25 10:15:44.601 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:15:44.602 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:15:44.602 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:15:44.603 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-25 10:15:44.604 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:15:48.507 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:15:48.663 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:15:48.664 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:15:48.664 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:15:48.665 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:15:48.666 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:15:48.666 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:15:50.714 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-25 10:15:50.715 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:15:50.715 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:15:50.716 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:15:50.716 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-25 10:15:50.716 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:15:53.380 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:15:53.381 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:15:53.382 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:15:53.382 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:15:53.383 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:15:53.383 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:15:57.903 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:15:57.903 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:15:57.904 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:15:57.905 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:15:57.906 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:15:57.906 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:07.470 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:16:07.471 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:16:07.472 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:16:07.472 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:16:07.473 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:16:07.473 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:09.066 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-25 10:16:09.067 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:16:09.067 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:16:09.067 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:16:09.068 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-25 10:16:09.068 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:17.531 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-25 10:16:17.532 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:16:17.532 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:16:17.534 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:16:17.535 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:16:17.535 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:22.093 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-25 10:16:22.094 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:16:22.094 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:16:22.094 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:16:22.095 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-25 10:16:22.096 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:30.488 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-25 10:16:30.488 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:16:30.488 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:16:30.489 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:16:30.489 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:16:30.489 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:34.770 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-25 10:16:34.771 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:16:34.771 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:16:34.772 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:16:34.773 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-25 10:16:34.773 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:40.740 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-25 10:16:40.740 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-25 10:16:40.740 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-25 10:16:40.742 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-25 10:16:40.743 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-25 10:16:40.743 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-25 10:16:48.510 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:17:48.522 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:18:19.823 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3595] 断开连接，剩余连接数: 0
2025-07-25 10:18:20.230 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3822] 在8002端口，当前连接数: 1
2025-07-25 10:18:48.534 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:19:48.558 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:20:48.478 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3822] 断开连接，剩余连接数: 0
2025-07-25 10:20:48.535 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:20:48.898 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4068] 在8002端口，当前连接数: 1
2025-07-25 10:21:48.559 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:22:48.565 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:23:48.569 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:24:48.584 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:25:48.592 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:26:48.606 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:27:48.614 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:28:48.643 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:29:48.634 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:30:48.633 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:31:48.644 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:32:48.668 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:33:48.685 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:34:48.695 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:35:48.698 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:36:48.697 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:37:13.493 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-25 10:37:13.493 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=3360)
2025-07-25 10:37:13.601 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-25 10:37:13.602 | INFO     | src.expression_controller:stop:41 - 表情控制器已停止
2025-07-25 10:37:13.602 | INFO     | src.message_handler:stop:28 - 消息处理器已停止
2025-07-25 10:37:13.602 | INFO     | src.remote_signal_processor:stop:114 - 表情触发器已停止
2025-07-25 10:37:13.602 | INFO     | src.remote_signal_processor:stop:125 - 遥控器信号处理器已停止
2025-07-25 10:37:13.603 | INFO     | src.action_queue_manager:stop:156 - 动作队列管理器已停止
2025-07-25 10:37:13.603 | INFO     | src.queue_execution_engine:stop:103 - 队列执行引擎已停止
2025-07-25 10:37:13.603 | INFO     | src.queue_status_manager:stop:61 - 队列状态管理器已停止
2025-07-25 10:37:13.603 | INFO     | src.unified_controller:stop:120 - 统一控制器已停止
2025-07-25 10:37:13.603 | INFO     | __main__:shutdown_event:557 - 应用关闭完成
2025-07-25 10:37:13.604 | INFO     | src.queue_execution_engine:_scheduler_loop:136 - 队列调度器已停止
2025-07-25 10:37:13.604 | INFO     | src.queue_execution_engine:_monitor_loop:192 - 队列监控器已停止
2025-07-25 10:37:13.604 | INFO     | src.queue_execution_engine:_cleanup_loop:353 - 队列清理器已停止
2025-07-25 10:37:16.070 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-25 10:37:16.071 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /723 -> dist\723
2025-07-25 10:37:16.071 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /725_test -> dist\725_test
2025-07-25 10:37:16.072 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-25 10:37:16.073 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-25 10:37:16.073 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-25 10:37:16.074 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /hijiki -> dist\hijiki
2025-07-25 10:37:16.074 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /huiyan -> dist\huiyan
2025-07-25 10:37:16.075 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-25 10:37:16.075 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-25 10:37:16.076 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lanse -> dist\lanse
2025-07-25 10:37:16.076 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-25 10:37:16.077 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-25 10:37:16.078 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-25 10:37:16.078 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-25 10:37:16.079 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /tororo -> dist\tororo
2025-07-25 10:37:16.079 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-25 10:37:16.079 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-25 10:37:16.081 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 10:37:16.081 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 10:37:16.082 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-25 10:37:16.082 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-25 10:37:16.128 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-25 10:37:16.129 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-25 10:37:16.129 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-25 10:37:16.130 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-25 10:37:16.130 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-25 10:37:16.130 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-25 10:37:16.170 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-25 10:37:16.173 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-25 10:37:16.173 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-25 10:37:16.173 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-25 10:37:16.173 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-25 10:37:16.174 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-25 10:37:16.174 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-25 10:37:16.174 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-25 10:37:16.174 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-25 10:37:16.174 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-25 10:37:16.175 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-25 10:37:16.175 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-25 10:37:16.175 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-25 10:37:16.175 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-25 10:37:16.176 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-25 10:37:16.176 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-25 10:37:18.997 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2832] 在8002端口，当前连接数: 1
2025-07-25 10:37:19.137 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-25 10:37:19.137 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-25 10:37:19.138 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-25 10:37:19.138 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-25 10:37:19.171 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-25 10:37:19.172 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-25 10:37:19.453 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-25 10:37:19.453 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-25 10:37:21.063 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:2849] 在8002端口，当前连接数: 2
2025-07-25 10:37:21.456 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-25 10:37:22.079 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-25 10:37:23.199 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-25 10:37:23.308 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-25 10:38:16.177 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:38:52.149 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2849] 断开连接，剩余连接数: 1
2025-07-25 10:38:52.683 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:3003] 在8002端口，当前连接数: 2
2025-07-25 10:39:16.179 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:40:16.186 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:41:16.188 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:42:16.184 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:43:16.193 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:44:16.206 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:45:16.231 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:46:16.237 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:47:16.247 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:48:16.238 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:49:16.261 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:50:16.279 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:51:16.295 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:52:16.308 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:53:16.295 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:54:16.314 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:55:16.292 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:56:16.307 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:57:16.312 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:58:16.326 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 10:59:16.341 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:00:16.356 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:01:16.360 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:02:16.375 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:02:33.571 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:2832] 断开连接，剩余连接数: 1
2025-07-25 11:02:36.563 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:3003] 断开连接，剩余连接数: 0
2025-07-25 11:03:16.379 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:04:16.397 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:05:16.407 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:06:16.401 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:07:16.407 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:08:16.424 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:09:16.437 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:10:16.427 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:11:16.434 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:12:16.443 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:13:16.443 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:14:16.455 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:15:16.451 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:16:16.441 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:17:16.449 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:18:16.449 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:19:16.469 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:20:16.481 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:21:16.487 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:22:16.507 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:23:16.516 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:24:16.519 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:25:16.540 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:26:16.561 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:27:16.576 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:28:16.576 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:29:16.589 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:30:16.598 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:31:16.621 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:32:16.641 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:33:16.628 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:34:16.657 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:35:16.655 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:36:16.645 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:37:16.648 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:38:16.672 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:39:16.684 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:40:16.698 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:41:16.715 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:42:16.726 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:43:16.713 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:44:16.713 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:45:16.730 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:46:16.732 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:47:16.746 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:48:16.762 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:49:16.772 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:50:16.798 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:51:16.815 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:52:16.831 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:53:16.833 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:54:16.850 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:55:16.874 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:56:16.891 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:57:16.908 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:58:16.909 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 11:59:16.921 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:00:16.924 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:01:16.923 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:02:16.933 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:03:16.954 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:04:16.954 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:05:16.959 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:06:16.983 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:07:16.990 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:08:17.012 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:09:17.014 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:10:16.997 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:11:17.013 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:12:17.023 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:13:17.047 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:14:17.048 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:15:17.043 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:16:17.048 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:17:17.044 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:18:17.060 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:19:17.037 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:20:17.064 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:21:17.059 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:22:17.066 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:23:17.078 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:24:17.071 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:25:17.068 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:26:17.081 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:27:17.106 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:28:17.108 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:29:17.118 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:30:17.127 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:31:17.136 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:32:17.148 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:33:17.164 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:34:17.186 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:35:17.177 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:36:17.162 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:37:17.184 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:38:17.189 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:39:17.203 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:40:17.203 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:41:17.216 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:42:17.217 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:43:17.233 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:44:17.250 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:45:17.253 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:46:17.279 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:47:17.284 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:48:17.304 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:49:17.313 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:50:17.305 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:51:17.313 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:52:17.316 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:53:17.316 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:54:17.336 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:55:17.348 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:56:17.363 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:57:17.385 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:58:17.397 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 12:59:17.418 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:00:17.430 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:01:17.443 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:02:17.442 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:03:17.467 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:04:17.475 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:05:17.487 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:06:17.482 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:07:17.498 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:08:17.506 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:09:17.521 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:10:17.539 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:11:17.542 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:12:17.558 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:13:17.560 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:14:17.570 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:15:17.594 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:16:17.603 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:17:17.612 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:18:17.631 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:19:17.630 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:20:17.652 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:21:17.669 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:22:17.675 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:23:17.664 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:24:17.670 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:25:17.674 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:26:17.680 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:27:17.681 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-25 13:28:17.688 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
