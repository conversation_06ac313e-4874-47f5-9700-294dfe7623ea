2025-07-28 17:34:40.743 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-28 17:34:40.752 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-28 17:34:40.752 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-28 17:34:40.753 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-28 17:34:40.755 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-28 17:34:40.755 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-28 17:34:40.756 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-28 17:34:40.757 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-28 17:34:40.758 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-28 17:34:40.759 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-28 17:34:40.760 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-28 17:34:40.760 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-28 17:34:40.761 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-28 17:34:40.763 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-28 17:34:40.764 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-28 17:34:40.764 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-28 17:34:40.833 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-28 17:34:40.834 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-28 17:34:40.834 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-28 17:34:40.835 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-28 17:34:40.836 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-28 17:34:40.836 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-28 17:34:40.888 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-28 17:34:40.892 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-28 17:34:40.893 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-28 17:34:40.893 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-28 17:34:40.893 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-28 17:34:40.894 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-28 17:34:40.894 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-28 17:34:40.894 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-28 17:34:40.894 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-28 17:34:40.895 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-28 17:34:40.895 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-28 17:34:40.896 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-28 17:34:40.896 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-28 17:34:40.896 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-28 17:34:40.896 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-28 17:34:40.896 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-28 17:34:43.838 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-28 17:34:43.838 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-28 17:34:43.839 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-28 17:34:43.839 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-28 17:34:43.894 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-28 17:34:43.895 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-28 17:34:44.277 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-28 17:34:44.278 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-28 17:34:46.082 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4742] 在8002端口，当前连接数: 1
2025-07-28 17:34:46.280 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-28 17:34:46.901 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-28 17:34:48.061 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-28 17:34:48.175 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-28 17:35:29.698 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4742] 断开连接，剩余连接数: 0
2025-07-28 17:35:30.152 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4907] 在8002端口，当前连接数: 1
2025-07-28 17:35:40.898 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:35:45.253 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4907] 断开连接，剩余连接数: 0
2025-07-28 17:35:45.676 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:4938] 在8002端口，当前连接数: 1
2025-07-28 17:36:40.920 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:37:40.917 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:38:40.923 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:39:40.936 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:40:33.410 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:4938] 断开连接，剩余连接数: 0
2025-07-28 17:40:33.946 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:5288] 在8002端口，当前连接数: 1
2025-07-28 17:40:34.063 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:5288] 断开连接，剩余连接数: 0
2025-07-28 17:40:34.488 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:5290] 在8002端口，当前连接数: 1
2025-07-28 17:40:37.385 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:5290] 断开连接，剩余连接数: 0
2025-07-28 17:40:40.940 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:41:40.937 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:42:40.934 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:43:40.912 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:44:40.938 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:45:40.947 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:46:40.963 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:47:40.986 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:48:41.016 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:49:41.015 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:50:40.999 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:51:41.012 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:52:41.019 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:53:41.027 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:54:41.024 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:55:41.034 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:56:41.055 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:57:41.064 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:58:41.069 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-28 17:59:41.093 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:26:09.516 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /721 -> dist\721
2025-07-29 13:26:09.517 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /729_TEST -> dist\729_TEST
2025-07-29 13:26:09.518 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-07-29 13:26:09.518 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-07-29 13:26:09.519 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-07-29 13:26:09.520 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-07-29 13:26:09.520 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-07-29 13:26:09.521 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-07-29 13:26:09.522 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /lvse -> dist\lvse
2025-07-29 13:26:09.522 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /pink_eyes -> dist\pink_eyes
2025-07-29 13:26:09.522 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-07-29 13:26:09.523 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /xm -> dist\xm
2025-07-29 13:26:09.523 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /XMN -> dist\XMN
2025-07-29 13:26:09.524 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /yanjiuceshi -> dist\yanjiuceshi
2025-07-29 13:26:09.524 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-07-29 13:26:09.525 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-29 13:26:09.526 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-29 13:26:09.527 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-07-29 13:26:09.527 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-07-29 13:26:09.594 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-07-29 13:26:09.595 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-07-29 13:26:09.595 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-07-29 13:26:09.596 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-07-29 13:26:09.596 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-07-29 13:26:09.596 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-07-29 13:26:09.633 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-07-29 13:26:09.636 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-29 13:26:09.636 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-07-29 13:26:09.638 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-07-29 13:26:09.638 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-07-29 13:26:09.638 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-07-29 13:26:09.638 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-07-29 13:26:09.638 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-07-29 13:26:09.638 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-07-29 13:26:09.639 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-07-29 13:26:09.639 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-07-29 13:26:09.640 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-07-29 13:26:09.640 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-07-29 13:26:09.640 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-07-29 13:26:09.640 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-07-29 13:26:09.641 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-07-29 13:26:12.609 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-07-29 13:26:12.609 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-07-29 13:26:12.610 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-07-29 13:26:12.610 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-07-29 13:26:12.634 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-07-29 13:26:12.635 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-07-29 13:26:13.072 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-07-29 13:26:13.073 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-07-29 13:26:14.504 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:5584] 在8002端口，当前连接数: 1
2025-07-29 13:26:15.081 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-07-29 13:26:15.693 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-07-29 13:26:16.813 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-07-29 13:26:16.919 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-07-29 13:27:04.116 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:5584] 断开连接，剩余连接数: 0
2025-07-29 13:27:04.544 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:5643] 在8002端口，当前连接数: 1
2025-07-29 13:27:08.310 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:5643] 断开连接，剩余连接数: 0
2025-07-29 13:27:08.734 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:5647] 在8002端口，当前连接数: 1
2025-07-29 13:27:09.619 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:27:48.340 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-07-29 13:27:48.341 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=5684)
2025-07-29 13:28:02.429 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-29 13:28:02.430 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:02.430 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:02.431 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:02.432 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-29 13:28:02.432 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:03.939 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-29 13:28:03.939 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:03.940 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:03.940 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:03.941 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-29 13:28:03.941 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:06.219 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:28:06.221 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:06.221 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:06.221 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:06.222 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:28:06.223 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:07.600 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:28:07.602 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:07.602 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:07.603 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:07.603 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:28:07.605 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:09.630 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:28:21.050 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-29 13:28:21.051 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:21.051 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:21.052 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:21.053 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-29 13:28:21.053 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:23.153 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-29 13:28:23.154 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:23.154 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:23.155 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:23.155 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-29 13:28:23.156 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:24.443 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:28:24.444 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:24.445 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:24.445 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:24.446 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:28:24.446 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:25.844 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:28:25.844 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:25.845 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:25.845 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:25.846 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:28:25.847 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:28:27.230 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:28:27.231 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:28:27.231 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:28:27.232 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:28:27.233 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:28:27.234 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:05.580 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:5647] 断开连接，剩余连接数: 0
2025-07-29 13:29:06.011 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:5743] 在8002端口，当前连接数: 1
2025-07-29 13:29:09.627 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:29:19.893 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-29 13:29:19.893 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:19.894 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:19.894 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:19.895 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-29 13:29:19.895 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:21.019 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-29 13:29:21.020 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:21.020 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:21.021 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:21.022 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-29 13:29:21.022 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:22.047 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:29:22.049 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:22.049 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:22.049 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:22.051 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:29:22.051 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:23.397 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:29:23.397 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:23.398 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:23.399 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:23.401 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:29:23.402 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:23.966 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:29:23.967 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:23.967 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:23.968 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:23.969 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:29:23.969 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:24.826 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:29:24.826 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:24.827 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:24.827 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:24.828 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:29:24.828 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:25.537 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:29:25.538 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:25.538 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:25.538 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:25.539 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:29:25.540 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:27.016 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:29:27.016 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:27.017 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:27.017 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:27.018 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:29:27.018 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:27.563 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:29:27.564 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:27.564 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:27.564 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:27.565 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:29:27.566 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:28.847 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:29:28.847 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:28.848 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:28.848 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:28.850 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:29:28.850 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:29.290 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:29:29.292 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:29.292 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:29.293 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:29.294 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:29:29.294 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:30.514 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:29:30.515 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:30.515 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:30.516 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:30.516 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:29:30.518 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:31.241 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:29:31.242 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:31.242 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:31.243 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:31.244 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:29:31.244 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:32.160 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:29:32.162 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:32.162 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:32.162 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:32.163 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:29:32.163 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:33.144 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:29:33.144 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:33.145 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:33.145 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:33.145 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:29:33.146 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:29:34.216 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:29:34.217 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:29:34.217 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:29:34.218 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:29:34.218 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:29:34.218 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:30:09.639 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:31:09.649 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:31:46.153 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-29 13:31:46.153 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:46.153 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:46.153 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:46.154 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-29 13:31:46.155 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:47.393 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:31:47.395 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:47.395 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:47.395 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:47.396 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:31:47.397 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:48.342 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:31:48.342 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:48.343 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:48.343 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:48.344 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:31:48.345 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:49.447 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:31:49.448 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:49.448 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:49.449 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:49.449 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:31:49.450 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:50.272 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:31:50.275 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:50.276 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:50.276 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:50.277 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:31:50.277 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:51.160 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-29 13:31:51.160 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:51.161 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:51.161 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:51.162 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-29 13:31:51.162 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:52.547 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:31:52.547 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:52.548 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:52.548 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:52.549 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:31:52.549 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:53.086 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-29 13:31:53.086 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:53.087 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:53.087 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:53.089 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-29 13:31:53.089 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:31:53.649 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:31:53.650 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:31:53.650 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:31:53.651 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:31:53.651 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:31:53.651 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:32:09.672 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:33:09.680 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:34:09.690 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:35:09.670 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:36:09.680 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:37:09.677 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:38:09.695 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:39:09.710 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:40:09.728 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:41:09.749 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:41:22.153 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-07-29 13:41:22.154 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:22.155 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:22.155 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:22.155 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-07-29 13:41:22.157 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:22.786 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:41:22.787 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:22.787 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:22.787 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:22.788 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:22.789 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:23.738 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-07-29 13:41:23.738 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:23.739 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:23.739 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:23.740 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-07-29 13:41:23.740 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:25.042 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-07-29 13:41:25.043 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:25.043 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:25.044 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:25.045 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:25.045 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:25.586 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:41:25.588 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:25.588 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:25.588 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:25.589 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:25.589 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:26.227 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:41:26.229 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:26.229 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:26.230 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:26.231 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:41:26.231 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:26.753 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:41:26.754 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:26.754 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:26.755 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:26.756 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:26.756 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:27.266 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:41:27.268 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:27.268 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:27.269 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:27.269 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:41:27.269 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:27.747 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:41:27.747 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:27.749 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:27.749 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:27.751 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:27.752 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:28.154 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:41:28.155 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:28.155 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:28.157 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:28.158 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:41:28.158 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:28.587 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:41:28.587 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:28.588 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:28.588 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:28.589 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:28.589 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:28.915 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:41:28.917 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:28.917 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:28.917 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:28.918 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:41:28.918 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:29.419 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:41:29.419 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:29.420 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:29.420 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:29.420 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:41:29.421 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:29.900 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:41:29.901 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:29.902 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:29.902 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:29.903 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:29.903 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:30.323 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-07-29 13:41:30.323 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:30.323 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:30.324 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:30.324 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-07-29 13:41:30.325 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:30.700 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:41:30.700 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:30.700 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:30.701 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:30.701 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:30.701 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:31.020 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-07-29 13:41:31.020 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:31.021 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:31.021 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:31.021 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-07-29 13:41:31.022 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:41:32.354 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-07-29 13:41:32.355 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-07-29 13:41:32.355 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-07-29 13:41:32.356 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-07-29 13:41:32.357 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-07-29 13:41:32.358 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-07-29 13:42:09.740 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:43:09.758 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:44:09.776 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:45:09.780 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:46:09.781 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:47:09.791 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:48:09.811 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:49:09.825 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:50:09.830 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:51:09.836 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:52:09.860 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:53:09.871 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:54:09.871 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:55:09.877 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:56:09.887 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:56:36.206 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:5743] 断开连接，剩余连接数: 0
2025-07-29 13:57:09.864 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:58:09.888 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 13:59:09.896 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 14:00:09.920 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 14:01:09.927 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 14:06:52.961 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-07-29 14:06:53.248 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=5684)
2025-07-29 14:06:53.599 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 14:07:53.832 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 14:08:53.878 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-07-29 14:09:54.019 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
