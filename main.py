"""
WebSocket消息转发中间层主程序
支持WebSocket和遥控器多种控制方式，提供表情控制和直接控制功能
集成Live2D前端服务功能
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import json
from loguru import logger
import sys
import os
from typing import Set
import asyncio
import webbrowser
import time
import pyautogui
import threading
import subprocess
import shutil

from src.unified_controller import UnifiedController
from src.connection_manager import ConnectionManager

# 配置日志
logger.remove()
logger.add(sys.stdout, level="INFO", format="{time} | {level} | {message}")
logger.add("logs/app.log", rotation="1 day", retention="7 days", level="DEBUG")

app = FastAPI(title="Robot WebSocket Forwarder", version="1.0.0")

# 动态挂载静态文件服务
def setup_static_files():
    """动态扫描并挂载dist目录下的所有静态文件和目录"""
    dist_path = "dist"

    if not os.path.exists(dist_path):
        logger.warning(f"静态文件目录 {dist_path} 不存在")
        return

    # 扫描dist目录下的所有项目
    try:
        for item in os.listdir(dist_path):
            item_path = os.path.join(dist_path, item)

            # 如果是目录，挂载为静态文件服务
            if os.path.isdir(item_path):
                mount_path = f"/{item}"
                try:
                    app.mount(mount_path, StaticFiles(directory=item_path), name=f"static_{item}")
                    logger.info(f"已挂载静态目录: {mount_path} -> {item_path}")
                except Exception as e:
                    logger.error(f"挂载静态目录 {mount_path} 失败: {e}")

            # 如果是文件，创建单独的路由
            elif os.path.isfile(item_path):
                # 为常见的静态文件创建路由
                file_route = f"/{item}"

                # 动态创建路由函数
                def create_file_handler(file_path: str):
                    async def serve_file():
                        if os.path.exists(file_path):
                            return FileResponse(file_path)
                        else:
                            logger.warning(f"文件不存在: {file_path}")
                            from fastapi import HTTPException
                            raise HTTPException(status_code=404, detail="File not found")

                    # 设置函数名以便调试
                    serve_file.__name__ = f"serve_{item.replace('.', '_').replace('-', '_')}"
                    return serve_file

                try:
                    # 注册路由
                    handler = create_file_handler(item_path)
                    app.get(file_route)(handler)
                    logger.info(f"已注册文件路由: {file_route} -> {item_path}")
                except Exception as e:
                    logger.error(f"注册文件路由 {file_route} 失败: {e}")

    except Exception as e:
        logger.error(f"扫描静态文件目录失败: {e}")

# 执行静态文件设置
setup_static_files()

# 初始化组件
connection_manager = ConnectionManager()
unified_controller = UnifiedController()

# Live2D连接管理
live2d_connections: Set[WebSocket] = set()

# Live2D消息转换函数
def convert_to_live2d_message(robot_message: dict) -> dict:
    """将机器人控制消息转换为Live2D格式"""
    # 提取控制指令
    yaw_cmd = robot_message.get("yaw_cmd")
    pitch_cmd = robot_message.get("pitch_cmd")

    # 优先级：yaw_cmd > pitch_cmd > center
    if yaw_cmd in ["left", "right", "center"]:
        group = yaw_cmd
    elif pitch_cmd in ["up", "down", "center"]:
        group = pitch_cmd
    else:
        group = "center"

    return {
        "type": "playMotion",
        "group": group,
        "index": 0
    }

def convert_video_to_live2d_message(video_message: dict) -> dict:
    """将遥控器视频播放消息转换为Live2D动作格式"""
    # 从direction字段获取动作组
    direction = video_message.get("direction", "center")

    # 确保direction是有效的Live2D动作组
    valid_groups = ["up", "down", "center", "left", "right"]
    if direction not in valid_groups:
        direction = "center"

    return {
        "type": "playMotion",
        "group": direction,
        "index": 0
    }

# Live2D广播函数
async def broadcast_to_live2d(message: str):
    """向所有Live2D客户端广播消息"""
    if not live2d_connections:
        return

    disconnected = []
    for connection in live2d_connections:
        try:
            # 检查连接类型并使用正确的发送方法
            if hasattr(connection, 'send_text'):
                # FastAPI WebSocket
                await connection.send_text(message)
            elif hasattr(connection, 'send'):
                # websockets库的WebSocket
                await connection.send(message)
            else:
                logger.error(f"未知的WebSocket连接类型: {type(connection)}")
                disconnected.append(connection)
        except Exception as e:
            logger.error(f"向Live2D客户端发送消息失败: {e}")
            disconnected.append(connection)

    # 清理断开的连接
    for connection in disconnected:
        live2d_connections.discard(connection)

# 增强的应用层广播回调
async def enhanced_app_broadcast_callback(message):
    """增强的应用层广播回调函数，支持Live2D"""
    # 原有的广播给普通WebSocket客户端
    await connection_manager.broadcast(json.dumps(message))

    # 如果是机器人控制消息，转换并发送给Live2D客户端
    if message.get("action") == "move_head_and_ear":
        live2d_message = convert_to_live2d_message(message)
        await broadcast_to_live2d(json.dumps(live2d_message))
        logger.info(f"已转发Live2D消息: {live2d_message}")

    # 如果是遥控器的视频播放消息，转换为Live2D动作消息
    elif message.get("action") == "play_video":
        live2d_message = convert_video_to_live2d_message(message)
        await broadcast_to_live2d(json.dumps(live2d_message))
        logger.info(f"已转发遥控器Live2D消息: {live2d_message}")

# 设置应用层广播回调
unified_controller.set_app_broadcast_callback(enhanced_app_broadcast_callback)


@app.get("/")
async def get():
    """提供简单的WebSocket测试页面"""
    html = """
    <!DOCTYPE html>
    <html>
        <head>
            <title>机器人方向控制</title>
            <style>
                body { font-family: Arial; margin: 20px; }
                .controls { margin: 20px 0; }
                button { margin: 5px; padding: 10px 20px; font-size: 16px; }
                #messages { border: 1px solid #ccc; padding: 10px; height: 200px; overflow-y: auto; }
                .nav-links { margin: 20px 0; padding: 10px; background: #f0f0f0; }
                .nav-links a { margin-right: 15px; text-decoration: none; color: #007bff; }
            </style>
        </head>
        <body>
            <h1>机器人控制系统</h1>

            <div class="nav-links">
                <a href="/live2d" target="_blank">🎭 Live2D 动画界面</a>
                <a href="/api/status">📊 系统状态</a>
                <a href="/health">💚 健康检查</a>
            </div>

            <h3>标准接口控制 (move_head_and_ear)</h3>
            <div class="controls">
                <label>头部左右 (yaw)：</label>
                <button onclick="moveHeadAndEar({yaw_cmd: 'left'})">左</button>
                <button onclick="moveHeadAndEar({yaw_cmd: 'center'})">中</button>
                <button onclick="moveHeadAndEar({yaw_cmd: 'right'})">右</button><br>

                <label>头部上下 (pitch)：</label>
                <button onclick="moveHeadAndEar({pitch_cmd: 'up'})">上</button>
                <button onclick="moveHeadAndEar({pitch_cmd: 'center'})">中</button>
                <button onclick="moveHeadAndEar({pitch_cmd: 'down'})">下</button><br>

            </div>


            <div class="controls">
                <button onclick="connect()">连接</button>
                <button onclick="disconnect()">断开</button>
            </div>
            <div id="messages"></div>

            <script>
                var ws = null;

                function connect() {
                    ws = new WebSocket("ws://localhost:8000/ws");
                    ws.onopen = function() {
                        addMessage("连接成功");
                    };
                    ws.onmessage = function(event) {
                        addMessage("收到: " + event.data);
                    };
                    ws.onclose = function() {
                        addMessage("连接关闭");
                    };
                }

                function disconnect() {
                    if (ws) ws.close();
                }

                function moveHeadAndEar(params) {
                    if (!ws) {
                        addMessage("请先连接");
                        return;
                    }
                    var message = {"action": "move_head_and_ear", ...params};
                    ws.send(JSON.stringify(message));

                    // 构建描述信息
                    var desc = [];
                    if (params.yaw_cmd) desc.push("yaw:" + params.yaw_cmd);
                    if (params.pitch_cmd) desc.push("pitch:" + params.pitch_cmd);

                    addMessage("发送: " + desc.join(", "));
                }

                function addMessage(msg) {
                    var messages = document.getElementById('messages');
                    messages.innerHTML += msg + '<br>';
                    messages.scrollTop = messages.scrollHeight;
                }

                // 自动连接
                window.onload = function() { connect(); };
            </script>
        </body>
    </html>
    """
    return HTMLResponse(content=html)


@app.get("/live2d")
async def serve_live2d():
    """提供Live2D应用页面"""
    return FileResponse("dist/index.html")





@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await connection_manager.connect(websocket)
    logger.info(f"新的WebSocket连接: {websocket.client}")

    try:
        while True:
            # 接收WebSocket消息
            data = await websocket.receive_text()
            logger.info(f"收到消息: {data}")

            try:
                # 解析JSON消息
                message = json.loads(data)

                # 处理消息并转发给统一控制器
                result = await unified_controller.handle_websocket_message(message)

                # 发送处理结果回客户端
                await websocket.send_text(json.dumps({
                    "status": "success",
                    "result": result,
                    "original_message": message
                }))

            except json.JSONDecodeError:
                error_msg = "无效的JSON格式"
                logger.error(f"JSON解析错误: {data}")
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "error": error_msg,
                    "original_message": data
                }))

            except Exception as e:
                error_msg = f"处理消息时发生错误: {str(e)}"
                logger.error(error_msg)
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "error": error_msg,
                    "original_message": data
                }))

    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)
        logger.info(f"WebSocket连接断开: {websocket.client}")


@app.websocket("/live2d-ws")
async def live2d_websocket_endpoint(websocket: WebSocket):
    """Live2D WebSocket连接端点"""
    await websocket.accept()
    live2d_connections.add(websocket)
    client_info = f"{websocket.client.host}:{websocket.client.port}"
    logger.info(f"新的Live2D客户端连接 [{client_info}]，当前连接数: {len(live2d_connections)}")

    try:
        # 不发送连接消息，避免前端警告
        # Live2D前端不需要连接确认消息

        # 保持连接并处理客户端消息
        while True:
            data = await websocket.receive_text()
            logger.info(f"从Live2D客户端 [{client_info}] 接收到消息: {data}")

            try:
                # 解析消息并响应
                message = json.loads(data)
                # 如果客户端发送了ping消息，回应pong
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": asyncio.get_event_loop().time()
                    }))
            except json.JSONDecodeError:
                logger.warning(f"从Live2D客户端接收到无效JSON: {data}")
            except Exception as e:
                logger.error(f"处理Live2D客户端消息时出错: {e}")

    except WebSocketDisconnect:
        logger.info(f"Live2D客户端 [{client_info}] 连接已关闭")
    except Exception as e:
        logger.error(f"与Live2D客户端 [{client_info}] 通信时出错: {e}")
    finally:
        if websocket in live2d_connections:
            live2d_connections.discard(websocket)
            logger.info(f"Live2D客户端 [{client_info}] 断开连接，剩余连接数: {len(live2d_connections)}")


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "Robot WebSocket Forwarder",
        "version": "1.0.0",
        "connections": connection_manager.get_connection_count(),
        "controller_status": unified_controller.get_status()
    }


@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    return unified_controller.get_status()


@app.get("/api/controllers")
async def get_controller_info():
    """获取控制器信息"""
    return unified_controller.get_controller_info()


@app.get("/api/expressions")
async def list_expressions():
    """列出所有可用表情"""
    status = unified_controller.get_status()
    return {
        "expressions": status.get("available_expressions", []),
        "total_count": len(status.get("available_expressions", []))
    }


@app.post("/api/expression/{expression_name}")
async def play_expression(expression_name: str):
    """播放指定表情"""
    result = await unified_controller.play_expression(expression_name)
    return result


@app.post("/api/expression/stop")
async def stop_expression():
    """停止当前表情"""
    result = await unified_controller.stop_current_expression()
    return result


@app.post("/api/emergency_stop")
async def emergency_stop():
    """紧急停止所有动作"""
    await unified_controller.emergency_stop()
    return {"status": "emergency_stop_executed"}


@app.post("/api/controller/{controller_name}/enable")
async def enable_controller(controller_name: str):
    """启用指定控制器"""
    success = unified_controller.enable_controller(controller_name)
    return {"success": success, "controller": controller_name, "action": "enabled"}


@app.post("/api/controller/{controller_name}/disable")
async def disable_controller(controller_name: str):
    """禁用指定控制器"""
    success = unified_controller.disable_controller(controller_name)
    return {"success": success, "controller": controller_name, "action": "disabled"}


# 动作队列API端点
@app.post("/api/queue/submit")
async def submit_action_queue(request: dict):
    """提交动作队列"""
    try:
        # 处理队列请求
        result = await unified_controller.submit_action_queue(request)

        # 如果有视频文件信息，广播给应用层
        if result.get("success") and "metadata" in request:
            metadata = request["metadata"]
            if "video_file" in metadata:
                # 构造应用层消息
                app_message = {
                    "action": "play_video",
                    "video_file": metadata["video_file"],
                    "direction": metadata.get("direction", ""),
                    "source": "controller",
                    "queue_id": result.get("queue_id")
                }

                # 广播给所有连接的WebSocket客户端（应用层）
                await connection_manager.broadcast(json.dumps(app_message))
                logger.info(f"已广播视频播放指令: {metadata['video_file']}")

        return result

    except Exception as e:
        logger.error(f"队列提交失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }


@app.post("/api/queue/{queue_id}/control")
async def control_action_queue(queue_id: str, action: dict):
    """控制动作队列"""
    control_request = {"queue_id": queue_id, **action}
    result = await unified_controller.control_action_queue(control_request)
    return result


@app.get("/api/queue/{queue_id}/status")
async def get_queue_status(queue_id: str):
    """获取队列状态"""
    result = unified_controller.get_queue_status(queue_id)
    return result


@app.get("/api/queues")
async def list_action_queues(status: str = None):
    """列出动作队列"""
    result = unified_controller.list_action_queues(status)
    return result


@app.get("/api/queue/statistics")
async def get_queue_statistics():
    """获取队列统计信息"""
    result = unified_controller.get_queue_statistics()
    return result


@app.post("/api/queue/clear")
async def clear_completed_queues():
    """清理已完成的队列"""
    result = unified_controller.clear_completed_queues()
    return result


@app.post("/api/queue/expression_sequence")
async def submit_expression_sequence(request: dict):
    """提交表情序列"""
    expressions = request.get("expressions", [])
    name = request.get("name", "表情序列")
    delays = request.get("delays")

    result = await unified_controller.submit_expression_sequence(expressions, name, delays)
    return result


@app.post("/api/queue/movement_sequence")
async def submit_movement_sequence(request: dict):
    """提交动作序列"""
    movements = request.get("movements", [])
    name = request.get("name", "动作序列")
    duration = request.get("duration", 1.0)

    result = await unified_controller.submit_movement_sequence(movements, name, duration)
    return result


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    try:
        # 启动统一控制器（启用WebSocket、表情触发器和Joy监听器）
        await unified_controller.start(["websocket", "expression_trigger", "joy_listener"])
        logger.info("应用启动完成")
    except Exception as e:
        logger.error(f"应用启动失败: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    try:
        unified_controller.stop()
        logger.info("应用关闭完成")
    except Exception as e:
        logger.error(f"应用关闭失败: {e}")


async def start_live2d_websocket_server():
    """启动Live2D WebSocket服务器在8002端口"""
    import websockets
    from websockets.legacy.server import serve as ws_serve

    async def handle_live2d_client(websocket, path):
        """处理Live2D客户端连接（8002端口）"""
        live2d_connections.add(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"新的Live2D客户端连接 [{client_info}] 在8002端口，当前连接数: {len(live2d_connections)}")

        try:
            # 不发送连接消息，避免前端警告
            # Live2D前端不需要连接确认消息

            # 保持连接并处理客户端消息
            async for message in websocket:
                logger.info(f"从Live2D客户端 [{client_info}] 接收到消息: {message}")

                try:
                    # 解析消息并响应
                    data = json.loads(message)
                    # 如果客户端发送了ping消息，回应pong
                    if data.get("type") == "ping":
                        await websocket.send(json.dumps({
                            "type": "pong",
                            "timestamp": asyncio.get_event_loop().time()
                        }))
                except json.JSONDecodeError:
                    logger.warning(f"从Live2D客户端接收到无效JSON: {message}")
                except Exception as e:
                    logger.error(f"处理Live2D客户端消息时出错: {e}")

        except Exception as e:
            logger.error(f"与Live2D客户端 [{client_info}] 通信时出错: {e}")
        finally:
            if websocket in live2d_connections:
                live2d_connections.discard(websocket)
                logger.info(f"Live2D客户端 [{client_info}] 断开连接，剩余连接数: {len(live2d_connections)}")

    # 启动WebSocket服务器在8002端口
    try:
        async with ws_serve(handle_live2d_client, "0.0.0.0", 8002):
            logger.info("Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002")
            await asyncio.Future()  # 运行直到被取消
    except Exception as e:
        logger.error(f"启动Live2D WebSocket服务器失败: {e}")
        raise


def auto_open_browser_and_fullscreen(url: str, auto_fullscreen: bool = True):
    """自动打开浏览器并全屏"""
    def browser_automation():
        # 等待服务器启动
        time.sleep(3)

        # 检测SSH环境并设置DISPLAY为远程物理显示器
        is_ssh = 'SSH_CLIENT' in os.environ or 'SSH_TTY' in os.environ
        logger.info(f"SSH环境检测: {'是' if is_ssh else '否'}")
        logger.info(f"当前环境变量 DISPLAY: {os.environ.get('DISPLAY', '未设置')}")

        if is_ssh:
            # 在SSH会话中，强制设置DISPLAY为远程物理显示器
            os.environ['DISPLAY'] = ':0'
            logger.info("已设置 DISPLAY=:0 用于远程物理显示器")
        elif 'DISPLAY' not in os.environ:
            # 非SSH环境但没有DISPLAY，也设置为:0
            os.environ['DISPLAY'] = ':0'
            logger.info("已设置 DISPLAY=:0")

        logger.info(f"正在打开浏览器: {url}")

        # 只使用chromium浏览器
        browser_success = False

        # 设置ARM64 Ubuntu环境变量
        env = os.environ.copy()
        env['DISPLAY'] = ':0'
        env['XAUTHORITY'] = '/home/' + env.get('USER', 'ubuntu') + '/.Xauthority'

        # 尝试epiphany-browser
        if shutil.which('epiphany-browser'):
            try:
                logger.info("尝试使用 epiphany-browser 启动")

                # epiphany-browser 命令
                cmd = ['epiphany-browser', url]
                logger.info(f"执行命令: {' '.join(cmd)}")

                # epiphany-browser 可能会快速退出（如果已有实例在运行）
                # 这是正常行为，所以我们不检查进程状态
                subprocess.Popen(cmd, env=env, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

                browser_success = True
                logger.info("epiphany-browser 启动命令已执行（注意：epiphany会关闭之前的页面打开新页面）")

            except Exception as e:
                logger.error(f"启动 epiphany-browser 失败: {e}")
                browser_success = False
        else:
            logger.warning("未找到 epiphany-browser")
            browser_success = False

        if not browser_success:
            try:
                logger.info("epiphany-browser 启动失败，尝试系统默认浏览器")
                # 设置环境变量确保默认浏览器能正确显示
                webbrowser.open(url)
                browser_success = True
                logger.info("系统默认浏览器启动成功")
            except Exception as e:
                logger.error(f"系统默认浏览器也失败: {e}")
                logger.info("请手动打开浏览器访问: " + url)

        # 如果浏览器启动失败，跳过GUI操作
        if not browser_success:
            logger.error("浏览器启动失败，跳过全屏操作")
            return

        if auto_fullscreen and browser_success:
            # 等待浏览器打开
            logger.info("等待浏览器打开...")
            time.sleep(2)

            try:
                # 禁用pyautogui的安全检查
                pyautogui.FAILSAFE = False

                # 1. 移动鼠标到屏幕中间
                screen_width, screen_height = pyautogui.size()
                logger.info(f"移动鼠标到屏幕中心点 ({screen_width/2}, {screen_height/2})")
                pyautogui.moveTo(screen_width/2, screen_height/2)

                # 等待一小段时间确保鼠标已移动
                time.sleep(0.5)

                # 2. 按F11键进行全屏
                logger.info("按下F11键进入全屏模式")
                pyautogui.press('f11')

                # 等待全屏模式生效
                time.sleep(1)

                # 3. 隐藏鼠标光标
                try:
                    from src.mouse_utils import hide_mouse_cursor
                    hide_mouse_cursor()
                except Exception as e:
                    logger.error(f"无法隐藏鼠标光标: {e}")

                # 额外的方法：将鼠标移到屏幕右下角
                try:
                    pyautogui.moveTo(screen_width-1, screen_height-1)
                    logger.info("已将鼠标移至屏幕边缘")
                except Exception as e:
                    logger.error(f"移动鼠标到边缘失败: {e}")

            except Exception as e:
                logger.error(f"GUI自动化操作失败: {e}")
                logger.info("跳过全屏和鼠标操作")

    # 在新线程中执行浏览器自动化
    browser_thread = threading.Thread(target=browser_automation, daemon=True)
    browser_thread.start()


if __name__ == "__main__":
    import uvicorn
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='启动集成的机器人控制和Live2D服务器')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    parser.add_argument('--no-fullscreen', action='store_true', help='不自动全屏和隐藏鼠标')
    parser.add_argument('--live2d-only', action='store_true', help='只打开Live2D界面')

    args = parser.parse_args()

    # 启动Live2D WebSocket服务器（在新线程中）
    def run_live2d_ws_server():
        asyncio.run(start_live2d_websocket_server())

    ws_thread = threading.Thread(target=run_live2d_ws_server, daemon=True)
    ws_thread.start()
    logger.info("Live2D WebSocket服务器线程已启动，端口: 8002")

    # 自动打开浏览器
    if not args.no_browser:
        if args.live2d_only:
            url = "http://localhost:8000/live2d"
        else:
            url = "http://localhost:8000"

        auto_open_browser_and_fullscreen(url, not args.no_fullscreen)

    # 启动主HTTP服务器在8000端口
    logger.info("启动集成服务器...")
    logger.info("- HTTP/API 服务: http://localhost:8000")
    logger.info("- Live2D 界面: http://localhost:8000/live2d")
    logger.info("- Live2D WebSocket: ws://localhost:8002")
    logger.info("- 控制面板: http://localhost:8000")

    try:
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    except KeyboardInterrupt:
        # 恢复鼠标光标显示
        if not args.no_browser and not args.no_fullscreen:
            try:
                from src.mouse_utils import show_mouse_cursor
                show_mouse_cursor()
            except Exception as e:
                logger.error(f"恢复鼠标光标显示失败: {e}")
        logger.info("\n服务器已停止")
