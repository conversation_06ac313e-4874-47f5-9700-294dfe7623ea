# 软件使用说明书

## 1. 功能简介

本软件用于通过函数接口控制机器人头部（左右、上下）和两只耳朵（上下）的位置。
**无需手柄/joy输入**，只需调用`move_head_and_ear`函数即可实现各种组合动作。

------

## 2. 主要接口

### move_head_and_ear

```
move_head_and_ear(
    yaw_cmd=None,         # 'left'/'center'/'right' 或 None
    pitch_cmd=None,       # 'up'/'center'/'down' 或 None
    left_ear_cmd=None,    # 'down'/'center'/'up' 或 None
    right_ear_cmd=None    # 'down'/'center'/'up' 或 None
)
```

- **yaw_cmd**：头部左右，'left'为左，'center'为中，'right'为右
- **pitch_cmd**：头部上下，'up'为上，'center'为中，'down'为下
- **left_ear_cmd**：左耳，'down'为下，'center'为中，'up'为上
- **right_ear_cmd**：右耳，'down'为下，'center'为中，'up'为上

**可单独或同时控制任意一个或多个部位。**

------

## 3. 示例用法

```
# 实例化节点
node = HeadControlNode()

# 头部右转，左耳上抬，右耳下垂
node.move_head_and_ear(yaw_cmd='right', left_ear_cmd='up', right_ear_cmd='down')

# 头部回中，耳朵回中
node.move_head_and_ear(yaw_cmd='center', left_ear_cmd='center', right_ear_cmd='center')

# 头部左转，抬头
node.move_head_and_ear(yaw_cmd='left', pitch_cmd='up')

# 只动耳朵
node.move_head_and_ear(left_ear_cmd='down', right_ear_cmd='up')
```

------

## 4. 运行方法

1. 启动ROS2环境，确保`cmotor_interface`包已正确安装。

2. 运行本脚本：

   ```
   python head_control_interface.py
   ```

3. 按需在代码中调用`move_head_and_ear`接口实现动作。