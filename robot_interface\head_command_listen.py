import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Joy
from std_msgs.msg import String  # 暂时使用String类型，后续可改为ExpressionMsg

class ExpressionControlNode(Node):
    def __init__(self):
        super().__init__('expression_control_node')
        self.joy_sub = self.create_subscription(
            Joy, '/joy', self.joy_callback, 10)
        # 发布表情话题
        self.expr_pub = self.create_publisher(String, '/expression', 10)

        # 阈值
        self.threshold = 0.5
        # 上次轴值
        self.last_axis6 = 0.0  # yaw
        self.last_axis7 = 0.0  # pitch

        # 当前位置状态跟踪
        self.current_yaw_position = 'center'    # 'left', 'center', 'right'
        self.current_pitch_position = 'center'  # 'up', 'center', 'down'

    def joy_callback(self, msg: Joy):
        axis6 = msg.axes[6]
        axis7 = msg.axes[7]

        # 处理yaw方向（左右）的状态机逻辑
        self.handle_yaw_state_machine(axis6)

        # 处理pitch方向（上下）的状态机逻辑
        self.handle_pitch_state_machine(axis7)

        self.last_axis6 = axis6
        self.last_axis7 = axis7

    def handle_yaw_state_machine(self, axis6):
        """处理yaw方向的状态机逻辑"""
        # 检测按键触发（从非激活状态到激活状态）
        left_pressed = axis6 > self.threshold and self.last_axis6 <= self.threshold
        right_pressed = axis6 < -self.threshold and self.last_axis6 >= -self.threshold

        if left_pressed:
            if self.current_yaw_position == 'right':
                # 从右边按左键，先回中
                self.publish_expression('yaw_center')
                self.current_yaw_position = 'center'
            elif self.current_yaw_position == 'center':
                # 从中间按左键，往左
                self.publish_expression('yaw_left')
                self.current_yaw_position = 'left'
            # 如果已经在左边，按左键无动作

        elif right_pressed:
            if self.current_yaw_position == 'left':
                # 从左边按右键，先回中
                self.publish_expression('yaw_center')
                self.current_yaw_position = 'center'
            elif self.current_yaw_position == 'center':
                # 从中间按右键，往右
                self.publish_expression('yaw_right')
                self.current_yaw_position = 'right'
            # 如果已经在右边，按右键无动作

    def handle_pitch_state_machine(self, axis7):
        """处理pitch方向的状态机逻辑"""
        # 检测按键触发（从非激活状态到激活状态）
        up_pressed = axis7 > self.threshold and self.last_axis7 <= self.threshold
        down_pressed = axis7 < -self.threshold and self.last_axis7 >= -self.threshold

        if up_pressed:
            if self.current_pitch_position == 'down':
                # 从下面按上键，先回中
                self.publish_expression('pitch_center')
                self.current_pitch_position = 'center'
            elif self.current_pitch_position == 'center':
                # 从中间按上键，往上
                self.publish_expression('pitch_up')
                self.current_pitch_position = 'up'
            # 如果已经在上面，按上键无动作

        elif down_pressed:
            if self.current_pitch_position == 'up':
                # 从上面按下键，先回中
                self.publish_expression('pitch_center')
                self.current_pitch_position = 'center'
            elif self.current_pitch_position == 'center':
                # 从中间按下键，往下
                self.publish_expression('pitch_down')
                self.current_pitch_position = 'down'
            # 如果已经在下面，按下键无动作

    def publish_expression(self, expr_name: str):
        self.get_logger().info(f'Publishing expression: {expr_name}')
        msg = String()
        msg.data = expr_name  # 将表情名称放入data字段
        self.expr_pub.publish(msg)


def main(args=None):
    rclpy.init(args=args)
    node = ExpressionControlNode()
    try:
        rclpy.spin(node)
    finally:
        node.destroy_node()
        rclpy.shutdown()
