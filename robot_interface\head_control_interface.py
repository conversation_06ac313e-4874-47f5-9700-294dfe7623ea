import time
import rclpy
from rclpy.node import Node
from cmotor_interface.msg import Cmotorcommand
from std_msgs.msg import String
import numpy as np

class HeadControlNode(Node):
    def __init__(self):
        super().__init__('head_control_node')
        self.publisher = self.create_publisher(Cmotorcommand, '/motor_commands', 10)
        self.ear_publisher = self.create_publisher(Cmotorcommand, '/ear_commands', 10)  # 耳朵控制专用

        # 头部电机：yaw=3，pitch=2
        self.yaw_positions = [-0.75, 0.0, 0.75]    # 0=左, 1=中, 2=右
        self.pitch_positions = [0.19, 0.0, -0.37]  # 0=上, 1=中, 2=下
        self.yaw_state = 1
        self.pitch_state = 1
        self.current_positions = {
            3: self.yaw_positions[self.yaw_state],
            2: self.pitch_positions[self.pitch_state],
        }

        self.interpolation_steps = 200
        self.publish_rate_hz = 350

        # 耳朵电机状态控制
        self.ear_positions = [-1.0, 0.0, 1.0]  # 0=下, 1=中, 2=上
        self.ear_state = {5: 1, 6: 1}  # 初始都在中间

        # 遥控器topic订阅（可选功能）
        self.remote_subscription = None
        self.enable_remote_control = False  # 默认禁用，可通过参数启用

    def move_head_and_ear(self, yaw_cmd=None, pitch_cmd=None, left_ear_cmd=None, right_ear_cmd=None):
        """
        控制头部和耳朵动作的统一接口。

        参数:
            yaw_cmd: 'left'/'center'/'right' 或 None
            pitch_cmd: 'up'/'center'/'down' 或 None
            left_ear_cmd: 'down'/'center'/'up' 或 None
            right_ear_cmd: 'down'/'center'/'up' 或 None

        说明:
            - 头部从一端到另一端会自动经过center，避免过冲。
            - 可单独或同时控制头部和耳朵。
        """
        # 控制yaw
        if yaw_cmd is not None:
            target_state = {'left': 0, 'center': 1, 'right': 2}.get(yaw_cmd)
            if target_state is not None and target_state != self.yaw_state:
                # 如果从一端到另一端，先到中间
                if (self.yaw_state == 0 and target_state == 2) or (self.yaw_state == 2 and target_state == 0):
                    center_pos = self.yaw_positions[1]
                    self._send_position_sequence(3, self.current_positions[3], center_pos)
                    self.yaw_state = 1
                    self.current_positions[3] = center_pos
                    target_pos = self.yaw_positions[target_state]
                    self._send_position_sequence(3, center_pos, target_pos)
                    self.yaw_state = target_state
                    self.current_positions[3] = target_pos
                else:
                    old_pos = self.current_positions[3]
                    target_pos = self.yaw_positions[target_state]
                    self._send_position_sequence(3, old_pos, target_pos)
                    self.yaw_state = target_state
                    self.current_positions[3] = target_pos

        # 控制pitch
        if pitch_cmd is not None:
            target_state = {'up': 0, 'center': 1, 'down': 2}.get(pitch_cmd)
            if target_state is not None and target_state != self.pitch_state:
                if (self.pitch_state == 0 and target_state == 2) or (self.pitch_state == 2 and target_state == 0):
                    center_pos = self.pitch_positions[1]
                    self._send_position_sequence(2, self.current_positions[2], center_pos)
                    self.pitch_state = 1
                    self.current_positions[2] = center_pos
                    target_pos = self.pitch_positions[target_state]
                    self._send_position_sequence(2, center_pos, target_pos)
                    self.pitch_state = target_state
                    self.current_positions[2] = target_pos
                else:
                    old_pos = self.current_positions[2]
                    target_pos = self.pitch_positions[target_state]
                    self._send_position_sequence(2, old_pos, target_pos)
                    self.pitch_state = target_state
                    self.current_positions[2] = target_pos

        # 控制左耳
        if left_ear_cmd is not None:
            target_state = {'down': 0, 'center': 1, 'up': 2}.get(left_ear_cmd)
            if target_state is not None and target_state != self.ear_state[5]:
                self._send_ear_motor_command(5, self.ear_positions[target_state])
                self.ear_state[5] = target_state

        # 控制右耳
        if right_ear_cmd is not None:
            target_state = {'down': 0, 'center': 1, 'up': 2}.get(right_ear_cmd)
            if target_state is not None and target_state != self.ear_state[6]:
                self._send_ear_motor_command(6, self.ear_positions[target_state])
                self.ear_state[6] = target_state

    def _send_ear_motor_command(self, motor_id: int, position: float):
        msg = Cmotorcommand()
        msg.motor_ids = [motor_id]
        msg.control_modes = [0]  # 位置模式
        msg.positions = [position]
        msg.speeds = [1.0]
        msg.torques = [0.0]
        msg.kps = [0.0]
        msg.kds = [0.0]
        self.ear_publisher.publish(msg)
        self.get_logger().info(f"耳朵电机{motor_id} -> {position:.3f} rad")

    def _send_position_sequence(self, motor_id: int, start: float, end: float):
        T = self.interpolation_steps / self.publish_rate_hz
        dt = 1.0 / self.publish_rate_hz
        N = self.interpolation_steps

        # 五次多项式插值
        a0 = start
        a1 = a2 = 0.0
        M = np.array([
            [  T**3,    T**4,     T**5],
            [3*T**2,   4*T**3,   5*T**4],
            [6*T,     12*T**2,  20*T**3]
        ])
        b = np.array([end - a0, 0.0, 0.0])
        a3, a4, a5 = np.linalg.solve(M, b)

        for i in range(N + 1):
            t = i * dt
            pos = a0 + a1*t + a2*t**2 + a3*t**3 + a4*t**4 + a5*t**5
            vel = a1 + 2*a2*t + 3*a3*t**2 + 4*a4*t**3 + 5*a5*t**4

            msg = Cmotorcommand()
            msg.motor_ids     = [motor_id]
            msg.control_modes = [3]      # 力位混合
            msg.positions     = [pos]
            msg.speeds        = [vel]
            msg.torques       = [0.0]
            msg.kps           = [500.0]
            msg.kds           = [10.0]
            self.publisher.publish(msg)
            time.sleep(dt)

    def enable_remote_control_listener(self, topic_name="/remote_control"):
        """
        启用遥控器控制监听

        Args:
            topic_name: 遥控器topic名称，默认为 "/remote_control"
        """
        if self.remote_subscription is None:
            self.remote_subscription = self.create_subscription(
                String,
                topic_name,
                self._remote_control_callback,
                10
            )
            self.enable_remote_control = True
            self.get_logger().info(f"遥控器控制已启用，监听topic: {topic_name}")
        else:
            self.get_logger().warning("遥控器控制已经启用")

    def disable_remote_control_listener(self):
        """禁用遥控器控制监听"""
        if self.remote_subscription is not None:
            self.destroy_subscription(self.remote_subscription)
            self.remote_subscription = None
            self.enable_remote_control = False
            self.get_logger().info("遥控器控制已禁用")

    def _remote_control_callback(self, msg):
        """
        遥控器控制回调函数

        Args:
            msg: String消息，包含遥控器命令
        """
        try:
            command = msg.data.strip()
            self.get_logger().info(f"收到遥控器命令: {command}")

            # 解析遥控器命令并执行相应动作
            if command == "up":
                self.move_head_and_ear(pitch_cmd="up")
            elif command == "down":
                self.move_head_and_ear(pitch_cmd="down")
            elif command == "left":
                self.move_head_and_ear(yaw_cmd="left")
            elif command == "right":
                self.move_head_and_ear(yaw_cmd="right")
            elif command == "center":
                self.move_head_and_ear(yaw_cmd="center", pitch_cmd="center")
            elif command == "left_ear_up":
                self.move_head_and_ear(left_ear_cmd="up")
            elif command == "left_ear_down":
                self.move_head_and_ear(left_ear_cmd="down")
            elif command == "right_ear_up":
                self.move_head_and_ear(right_ear_cmd="up")
            elif command == "right_ear_down":
                self.move_head_and_ear(right_ear_cmd="down")
            else:
                self.get_logger().warning(f"未知遥控器命令: {command}")

        except Exception as e:
            self.get_logger().error(f"处理遥控器命令失败: {e}")

def main(args=None):
    rclpy.init(args=args)
    node = HeadControlNode()
    try:
        # 示例：让头部右转，左耳上抬，右耳下垂
        node.move_head_and_ear(yaw_cmd='right', left_ear_cmd='up', right_ear_cmd='down')
        time.sleep(1)
        # 示例：头部回中，耳朵回中
        node.move_head_and_ear(yaw_cmd='center', left_ear_cmd='center', right_ear_cmd='center')
        time.sleep(1)
        # 示例：头部左转，抬头
        node.move_head_and_ear(yaw_cmd='left', pitch_cmd='up')
        time.sleep(1)
        # 示例：只动耳朵
        node.move_head_and_ear(left_ear_cmd='down', right_ear_cmd='up')
        time.sleep(1)
        rclpy.spin(node)
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
