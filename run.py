"""
启动脚本 - 集成版本
现在main.py同时提供API服务(8000端口)和Live2D WebSocket服务(8002端口)
"""

import sys
import subprocess
import shutil

def get_python_executable():
    """获取正确的Python可执行文件名"""
    # 检查python3是否可用
    if shutil.which("python3"):
        return "python3"
    # 检查python是否可用
    elif shutil.which("python"):
        return "python"
    else:
        raise RuntimeError("未找到Python解释器")

if __name__ == "__main__":
    print("启动集成服务器...")
    print("- HTTP/API 服务: http://localhost:8000")
    print("- Live2D 界面: http://localhost:8000/live2d")
    print("- Live2D WebSocket: ws://localhost:8002")
    print("- 控制面板: http://localhost:8000")
    print()

    # 获取正确的Python可执行文件
    python_cmd = get_python_executable()

    # 默认启动Live2D界面
    # 如果需要其他选项，可以通过命令行参数传递给main.py
    if len(sys.argv) > 1:
        # 如果有命令行参数，直接传递给main.py
        args = [python_cmd, "main.py"] + sys.argv[1:]
        print(f"执行命令: {' '.join(args)}")
        subprocess.run(args)
    else:
        # 默认启动Live2D界面
        print("默认启动Live2D界面（全屏模式）")
        print("如需其他选项，请使用:")
        print(f"  {python_cmd} run.py --no-fullscreen  # 不全屏")
        print(f"  {python_cmd} run.py --no-browser     # 不打开浏览器")
        print(f"  {python_cmd} main.py                 # 打开控制面板")
        print()

        subprocess.run([python_cmd, "main.py", "--live2d-only"])
