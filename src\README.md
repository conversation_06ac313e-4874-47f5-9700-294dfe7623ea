# 机器人控制中间件系统 - 源码说明文档

## 系统概述

本系统是一个基于Python的机器人控制中间件，提供WebSocket消息转发、动作队列管理、表情控制等功能。系统采用模块化设计，支持多种控制源（WebSocket、遥控器、应用层队列）的统一管理。

## 核心架构

```
┌─────────────────────────────────────────────────────────────┐
│                    统一控制管理器                              │
│                 (unified_controller.py)                    │
├─────────────────────────────────────────────────────────────┤
│  WebSocket控制  │  遥控器控制  │  应用层队列  │  表情控制      │
├─────────────────┼─────────────┼─────────────┼─────────────────┤
│ message_handler │ remote_*    │ application │ expression_     │
│                 │ processor   │ interface   │ controller      │
├─────────────────────────────────────────────────────────────┤
│                    动作队列管理系统                            │
│              (action_queue_manager.py)                     │
├─────────────────────────────────────────────────────────────┤
│                    机器人控制客户端                            │
│                  (robot_client.py)                         │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. 统一控制管理器 (unified_controller.py)

**功能**: 系统的核心协调器，整合所有控制源和处理逻辑

**主要职责**:
- 统一管理WebSocket、遥控器、动作队列等多种控制源
- 协调各个子模块的启动和停止
- 提供统一的消息路由和处理接口
- 维护系统状态和统计信息

**关键特性**:
- 支持控制器的动态启用/禁用
- 提供紧急停止功能
- 消息统计和状态监控
- 应用层广播回调机制

### 2. 动作队列管理系统 (action_queue_manager.py)

**功能**: 高级动作序列管理，支持复杂的机器人动作编排

**核心组件**:
- `ActionType`: 动作类型枚举（头部控制、表情播放、延迟等）
- `QueueStatus`: 队列状态管理（等待、运行、暂停、完成等）
- `Priority`: 优先级系统（低、普通、高、紧急）
- `QueueAction`: 单个动作定义
- `ActionQueue`: 动作队列容器
- `ActionQueueManager`: 队列管理器

**高级特性**:
- 并发队列执行（可配置最大并发数）
- 动作重试机制
- 实时进度跟踪
- 状态变化回调
- 队列暂停/恢复/取消
- 执行超时控制

### 3. 应用层接口 (application_interface.py)

**功能**: 为应用层提供标准化的API接口

**主要接口**:
- `submit_action_queue()`: 提交动作队列
- `control_queue()`: 控制队列执行（启动/暂停/恢复/取消）
- `get_queue_status()`: 获取队列状态
- `list_queues()`: 列出队列
- `get_statistics()`: 获取统计信息

**便捷方法**:
- `submit_expression_sequence()`: 快速提交表情序列
- `submit_movement_sequence()`: 快速提交动作序列

### 4. 机器人控制客户端 (robot_client.py)

**功能**: ROS2机器人控制接口的封装

**主要特性**:
- ROS2节点管理
- 头部和耳朵控制接口
- 优雅的错误处理和降级
- 模拟模式支持（当ROS2不可用时）

**接口方法**:
- `move_head_and_ear_direct()`: 直接控制头部和耳朵
- `move_head_and_ear()`: 标准控制接口

### 5. 表情控制器 (expression_controller.py)

**功能**: 表情播放和控制的专门模块

**主要功能**:
- 表情映射和管理
- 表情序列播放
- 表情中断和切换
- WebSocket和遥控器表情控制

### 6. 支持模块

#### 消息处理器 (message_handler.py)
- WebSocket消息的解析和路由
- 消息格式验证
- 错误处理和响应生成

#### 遥控器信号处理器 (remote_signal_processor.py)
- Joy控制器信号监听
- 信号到动作的映射
- 多种遥控器类型支持

#### 表情映射器 (expression_mapper.py)
- 表情名称到文件的映射
- 表情序列定义
- 机器人动作配置

#### 队列执行引擎 (queue_execution_engine.py)
- 队列执行策略管理
- 执行优化和调度
- 性能监控

#### 队列状态管理器 (queue_status_manager.py)
- 队列状态持久化
- 状态变化通知
- 历史记录管理

#### 连接管理器 (connection_manager.py)
- WebSocket连接管理
- 连接状态监控
- 重连机制

## 模块解耦分析

### 优秀的解耦设计

1. **分层架构清晰**
   - 应用层接口 → 队列管理 → 机器人控制，层次分明
   - 每层职责单一，接口明确

2. **接口抽象良好**
   - `ApplicationInterface`为应用层提供标准化API
   - `RobotClient`封装ROS2复杂性
   - 回调机制实现松耦合通信

3. **状态管理独立**
   - `QueueStatusManager`专门管理队列状态
   - 状态变化通过回调通知，避免直接耦合
   - 各模块维护自己的状态，互不干扰

4. **配置与逻辑分离**
   - `ExpressionMapper`独立管理表情映射配置
   - 执行策略可配置，支持运行时调整
   - 控制器启用/禁用配置化

5. **错误处理隔离**
   - 各模块独立处理自己的错误
   - 错误不会传播影响其他模块
   - 提供优雅降级机制

### 模块依赖关系图

```
UnifiedController (协调层)
├── ApplicationInterface (应用接口层)
│   └── ActionQueueManager (队列管理层)
├── ExpressionController (表情控制层)
├── MessageHandler (消息处理层)
├── RemoteSignalProcessor (遥控处理层)
└── RobotClient (硬件接口层)

支持模块:
├── ExpressionMapper (配置管理)
├── QueueExecutionEngine (执行引擎)
├── QueueStatusManager (状态管理)
└── ConnectionManager (连接管理)
```

### 需要改进的耦合点

1. **统一控制器职责过重**
   ```python
   # 当前: UnifiedController承担太多职责
   class UnifiedController:
       def handle_websocket_message()
       def handle_remote_control()
       def submit_action_queue()
       def play_expression()
       # ... 20+ 个方法

   # 建议: 拆分为多个专门的协调器
   class MessageRouter:      # 消息路由
   class ControllerManager:  # 控制器管理
   class SystemCoordinator:  # 系统协调
   ```

2. **硬编码依赖**
   ```python
   # 问题: 直接依赖具体实现
   self.robot_client = RobotClient()
   self.expression_controller = ExpressionController(self.robot_client)

   # 建议: 依赖注入
   def __init__(self, robot_client: IRobotClient,
                expression_controller: IExpressionController):
   ```

3. **循环依赖风险**
   - `UnifiedController` ↔ `ApplicationInterface` 存在双向调用
   - 建议使用事件总线模式解耦

4. **配置耦合**
   ```python
   # 问题: 配置分散在各个模块中
   self.max_concurrent_queues = 3  # 在ActionQueueManager中
   self.enabled_controllers = {...} # 在UnifiedController中

   # 建议: 统一配置管理
   class SystemConfig:
       queue_config: QueueConfig
       controller_config: ControllerConfig
   ```

### 解耦质量评分

| 模块 | 单一职责 | 接口抽象 | 依赖注入 | 配置分离 | 总分 |
|------|----------|----------|----------|----------|------|
| ActionQueueManager | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | 4.0/5 |
| ApplicationInterface | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 4.5/5 |
| RobotClient | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | 3.8/5 |
| ExpressionController | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | 3.8/5 |
| UnifiedController | ⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ | 2.5/5 |

### 改进建议

1. **引入依赖注入容器**
2. **实现事件驱动架构**
3. **拆分UnifiedController**
4. **统一配置管理**
5. **添加接口定义**

## API接口规范

### WebSocket消息格式

```json
{
  "action": "move_head_and_ear|play_expression|stop_expression",
  "parameters": {
    // 具体参数根据action类型而定
  }
}
```

### 动作队列格式

```json
{
  "name": "队列名称",
  "actions": [
    {
      "action_type": "move_head_and_ear",
      "parameters": {
        "yaw_cmd": "left",
        "pitch_cmd": "up"
      },
      "duration": 2.0,
      "delay_after": 0.5
    }
  ],
  "priority": "normal",
  "auto_start": true
}
```

## 使用示例

### 基本使用

```python
from src.unified_controller import UnifiedController

# 初始化控制器
controller = UnifiedController()

# 启动系统
await controller.start()

# 处理WebSocket消息
result = await controller.handle_websocket_message({
    "action": "play_expression",
    "expression": "happy"
})

# 提交动作队列
queue_result = await controller.submit_action_queue({
    "name": "测试序列",
    "actions": [
        {
            "action_type": "play_expression",
            "parameters": {"expression": "happy"},
            "duration": 2.0
        }
    ]
})
```

### 高级队列操作

```python
# 创建复杂动作序列
movements = [
    {"yaw_cmd": "left", "pitch_cmd": "up"},
    {"yaw_cmd": "right", "pitch_cmd": "down"},
    {"yaw_cmd": "center", "pitch_cmd": "center"}
]

result = await controller.submit_movement_sequence(
    movements, 
    name="巡视动作", 
    duration=1.5
)
```

## 配置和扩展

### 添加新的动作类型

1. 在`ActionType`枚举中添加新类型
2. 在`ActionQueueManager._execute_action()`中添加处理逻辑
3. 更新相关文档和测试

### 添加新的控制源

1. 创建新的处理器模块
2. 在`UnifiedController`中注册新控制器
3. 实现相应的消息路由逻辑

## 性能特性

- **并发处理**: 支持多队列并发执行
- **内存管理**: 自动清理已完成的队列
- **错误恢复**: 完善的错误处理和重试机制
- **状态持久化**: 队列状态的持久化存储
- **实时监控**: 详细的执行统计和状态监控

## 依赖关系

- **ROS2**: 机器人控制接口
- **asyncio**: 异步编程支持
- **loguru**: 日志管理
- **websockets**: WebSocket通信（在上层使用）

## 模块功能对比表

| 模块 | 主要功能 | 输入接口 | 输出接口 | 依赖关系 | 复杂度 |
|------|----------|----------|----------|----------|--------|
| **UnifiedController** | 系统协调 | WebSocket消息、遥控信号 | 统一响应 | 所有模块 | 高 |
| **ActionQueueManager** | 队列管理 | 动作队列定义 | 执行结果、状态回调 | RobotClient | 中 |
| **ApplicationInterface** | 应用接口 | 标准API请求 | JSON响应 | ActionQueueManager | 低 |
| **RobotClient** | 硬件控制 | 控制指令 | 执行结果 | ROS2 | 中 |
| **ExpressionController** | 表情控制 | 表情指令 | 播放结果 | RobotClient、ExpressionMapper | 中 |
| **MessageHandler** | 消息处理 | WebSocket消息 | 处理结果 | RobotClient | 低 |
| **RemoteSignalProcessor** | 遥控处理 | Joy信号 | 控制指令 | ExpressionMapper | 中 |

## 系统集成指南

### 1. 基础集成

```python
# 最小化集成示例
from src.unified_controller import UnifiedController

async def basic_setup():
    # 创建控制器
    controller = UnifiedController()

    # 设置应用层回调（可选）
    async def app_callback(message):
        print(f"应用层收到: {message}")

    controller.set_app_broadcast_callback(app_callback)

    # 启动系统
    await controller.start()

    return controller
```

### 2. 高级集成

```python
# 完整功能集成
async def advanced_setup():
    controller = UnifiedController()

    # 配置启用的控制器
    controllers_to_start = [
        "expression_trigger",  # Joy表情触发
        "joy_listener"        # Joy监听器
    ]

    # 启动指定控制器
    await controller.start(controllers_to_start)

    # 设置队列状态回调
    def on_queue_status_change(queue_id, status):
        print(f"队列 {queue_id} 状态变更: {status}")

    controller.queue_manager.add_status_callback(on_queue_status_change)

    return controller
```

### 3. 自定义扩展

```python
# 扩展新的动作类型
from src.action_queue_manager import ActionType

# 1. 添加新的动作类型
class CustomActionType(ActionType):
    CUSTOM_DANCE = "custom_dance"
    CUSTOM_GESTURE = "custom_gesture"

# 2. 扩展执行逻辑
class CustomActionQueueManager(ActionQueueManager):
    async def _execute_action(self, action):
        if action.action_type == CustomActionType.CUSTOM_DANCE:
            # 实现自定义舞蹈逻辑
            await self._execute_dance_sequence(action.parameters)
        else:
            # 调用父类方法处理标准动作
            await super()._execute_action(action)
```

## 性能优化建议

### 1. 队列管理优化

```python
# 配置队列参数
queue_manager.max_concurrent_queues = 5  # 根据硬件能力调整
queue_manager.queue_history_limit = 50   # 控制内存使用

# 定期清理
async def cleanup_task():
    while True:
        await asyncio.sleep(300)  # 5分钟清理一次
        queue_manager.clear_completed_queues()
```

### 2. 内存管理

```python
# 监控内存使用
def monitor_memory():
    stats = controller.get_queue_statistics()
    if stats["total_queues"] > 100:
        controller.clear_completed_queues()
        logger.warning("队列数量过多，已自动清理")
```

### 3. 错误恢复

```python
# 实现错误恢复机制
async def error_recovery():
    try:
        await controller.start()
    except Exception as e:
        logger.error(f"启动失败: {e}")
        # 尝试重启核心组件
        await controller.robot_client.reconnect()
        await controller.start()
```

## 故障排除

### 常见问题

1. **ROS2连接失败**
   ```bash
   # 检查ROS2环境
   ros2 node list
   ros2 topic list

   # 检查Python路径
   echo $PYTHONPATH
   ```

2. **队列执行卡住**
   ```python
   # 检查队列状态
   stats = controller.get_queue_statistics()
   print(f"运行中队列: {stats['running_queues']}")

   # 强制清理
   controller.clear_completed_queues()
   ```

3. **内存泄漏**
   ```python
   # 监控队列数量
   queues = controller.list_action_queues()
   print(f"总队列数: {queues['total_count']}")

   # 定期清理
   controller.clear_completed_queues()
   ```

## 注意事项

1. **ROS2环境**: 需要正确配置ROS2环境和相关依赖
2. **权限管理**: 确保有足够权限访问机器人硬件接口
3. **并发限制**: 根据硬件性能合理配置队列并发数量
4. **内存使用**: 定期清理已完成的队列避免内存泄漏
5. **错误处理**: 实现适当的错误恢复和重试机制
6. **日志管理**: 配置合适的日志级别和输出方式

## 版本兼容性

- **Python**: 3.8+
- **ROS2**: Humble/Iron/Rolling
- **asyncio**: 内置支持
- **loguru**: 0.6.0+

## 扩展开发

### 添加新控制器

1. 继承基础控制器接口
2. 实现必要的生命周期方法
3. 在UnifiedController中注册
4. 添加相应的配置选项

### 自定义动作类型

1. 扩展ActionType枚举
2. 在ActionQueueManager中添加执行逻辑
3. 更新API文档
4. 添加单元测试
