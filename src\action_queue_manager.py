"""
动作队列管理系统
接收、存储和执行来自应用层的动作队列
"""

import asyncio
import uuid
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime
from loguru import logger
import json


class ActionType(Enum):
    """动作类型枚举"""
    MOVE_HEAD_AND_EAR = "move_head_and_ear"
    PLAY_EXPRESSION = "play_expression"
    STOP_EXPRESSION = "stop_expression"
    DELAY = "delay"
    CUSTOM = "custom"


class QueueStatus(Enum):
    """队列状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    PAUSED = "paused"        # 已暂停
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消
    FAILED = "failed"        # 执行失败


class Priority(Enum):
    """优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class QueueAction:
    """队列动作定义"""
    action_type: ActionType
    parameters: Dict[str, Any]
    duration: float = 0.0           # 动作持续时间（秒）
    delay_before: float = 0.0       # 执行前延迟（秒）
    delay_after: float = 0.0        # 执行后延迟（秒）
    timeout: float = 30.0           # 超时时间（秒）
    retry_count: int = 0            # 重试次数
    description: str = ""           # 动作描述
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "action_type": self.action_type.value,
            "parameters": self.parameters,
            "duration": self.duration,
            "delay_before": self.delay_before,
            "delay_after": self.delay_after,
            "timeout": self.timeout,
            "retry_count": self.retry_count,
            "description": self.description
        }


@dataclass
class ActionQueue:
    """动作队列定义"""
    queue_id: str
    name: str
    actions: List[QueueAction]
    priority: Priority = Priority.NORMAL
    created_time: datetime = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: QueueStatus = QueueStatus.PENDING
    current_action_index: int = 0
    total_actions: int = 0
    completed_actions: int = 0
    failed_actions: int = 0
    error_message: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now()
        if self.metadata is None:
            self.metadata = {}
        self.total_actions = len(self.actions)
    
    def get_progress(self) -> float:
        """获取执行进度（0-1）"""
        if self.total_actions == 0:
            return 1.0
        return self.completed_actions / self.total_actions
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "queue_id": self.queue_id,
            "name": self.name,
            "actions": [action.to_dict() for action in self.actions],
            "priority": self.priority.value,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "status": self.status.value,
            "current_action_index": self.current_action_index,
            "total_actions": self.total_actions,
            "completed_actions": self.completed_actions,
            "failed_actions": self.failed_actions,
            "progress": self.get_progress(),
            "error_message": self.error_message,
            "metadata": self.metadata
        }


class ActionQueueManager:
    """动作队列管理器"""
    
    def __init__(self, robot_callback: Callable[[Dict[str, Any]], Any]):
        """
        初始化队列管理器
        
        Args:
            robot_callback: 机器人控制回调函数
        """
        self.robot_callback = robot_callback
        self.queues: Dict[str, ActionQueue] = {}
        self.execution_tasks: Dict[str, asyncio.Task] = {}
        self.is_running = False
        self.max_concurrent_queues = 3  # 最大并发队列数
        self.queue_history_limit = 100  # 队列历史记录限制
        
        # 状态回调
        self.status_callbacks: List[Callable[[str, QueueStatus], None]] = []
        self.progress_callbacks: List[Callable[[str, float], None]] = []
    
    async def start(self):
        """启动队列管理器"""
        self.is_running = True
        logger.info("动作队列管理器已启动")
    
    def stop(self):
        """停止队列管理器"""
        self.is_running = False
        
        # 取消所有执行中的任务
        for task in self.execution_tasks.values():
            if not task.done():
                task.cancel()
        
        logger.info("动作队列管理器已停止")
    
    def add_status_callback(self, callback: Callable[[str, QueueStatus], None]):
        """添加状态变化回调"""
        self.status_callbacks.append(callback)
    
    def add_progress_callback(self, callback: Callable[[str, float], None]):
        """添加进度变化回调"""
        self.progress_callbacks.append(callback)
    
    def _notify_status_change(self, queue_id: str, status: QueueStatus):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(queue_id, status)
            except Exception as e:
                logger.error(f"状态回调执行失败: {e}")
    
    def _notify_progress_change(self, queue_id: str, progress: float):
        """通知进度变化"""
        for callback in self.progress_callbacks:
            try:
                callback(queue_id, progress)
            except Exception as e:
                logger.error(f"进度回调执行失败: {e}")
    
    def create_queue(self, name: str, actions: List[Dict[str, Any]], 
                    priority: Priority = Priority.NORMAL,
                    metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        创建动作队列
        
        Args:
            name: 队列名称
            actions: 动作列表
            priority: 优先级
            metadata: 元数据
            
        Returns:
            队列ID
        """
        try:
            queue_id = str(uuid.uuid4())
            
            # 解析动作列表
            queue_actions = []
            for action_data in actions:
                action = self._parse_action(action_data)
                queue_actions.append(action)
            
            # 创建队列
            queue = ActionQueue(
                queue_id=queue_id,
                name=name,
                actions=queue_actions,
                priority=priority,
                metadata=metadata or {}
            )
            
            self.queues[queue_id] = queue
            
            logger.info(f"动作队列已创建: {queue_id} - {name} ({len(queue_actions)}个动作)")
            
            return queue_id
            
        except Exception as e:
            logger.error(f"创建动作队列失败: {e}")
            raise
    
    def _parse_action(self, action_data: Dict[str, Any]) -> QueueAction:
        """解析动作数据"""
        action_type_str = action_data.get("action_type", action_data.get("action", ""))
        
        # 映射动作类型
        action_type_mapping = {
            "move_head_and_ear": ActionType.MOVE_HEAD_AND_EAR,
            "play_expression": ActionType.PLAY_EXPRESSION,
            "stop_expression": ActionType.STOP_EXPRESSION,
            "delay": ActionType.DELAY,
            "custom": ActionType.CUSTOM
        }
        
        action_type = action_type_mapping.get(action_type_str, ActionType.CUSTOM)
        
        # 提取参数
        parameters = action_data.get("parameters", {})
        if not parameters:
            # 如果没有parameters字段，将除了控制字段外的所有字段作为参数
            control_fields = {"action_type", "action", "duration", "delay_before", 
                            "delay_after", "timeout", "retry_count", "description"}
            parameters = {k: v for k, v in action_data.items() if k not in control_fields}
        
        return QueueAction(
            action_type=action_type,
            parameters=parameters,
            duration=action_data.get("duration", 0.0),
            delay_before=action_data.get("delay_before", 0.0),
            delay_after=action_data.get("delay_after", 0.0),
            timeout=action_data.get("timeout", 30.0),
            retry_count=action_data.get("retry_count", 0),
            description=action_data.get("description", "")
        )
    
    async def submit_queue(self, queue_id: str) -> bool:
        """
        提交队列执行
        
        Args:
            queue_id: 队列ID
            
        Returns:
            是否成功提交
        """
        try:
            if queue_id not in self.queues:
                logger.error(f"队列不存在: {queue_id}")
                return False
            
            queue = self.queues[queue_id]
            
            if queue.status != QueueStatus.PENDING:
                logger.error(f"队列状态不允许执行: {queue.status}")
                return False
            
            # 检查并发限制
            running_count = len([q for q in self.queues.values() 
                               if q.status == QueueStatus.RUNNING])
            
            if running_count >= self.max_concurrent_queues:
                logger.warning(f"达到最大并发队列数限制: {self.max_concurrent_queues}")
                return False
            
            # 创建执行任务
            task = asyncio.create_task(self._execute_queue(queue_id))
            self.execution_tasks[queue_id] = task
            
            logger.info(f"队列已提交执行: {queue_id}")
            return True
            
        except Exception as e:
            logger.error(f"提交队列执行失败: {e}")
            return False
    
    async def _execute_queue(self, queue_id: str):
        """执行队列"""
        try:
            queue = self.queues[queue_id]
            queue.status = QueueStatus.RUNNING
            queue.start_time = datetime.now()
            self._notify_status_change(queue_id, queue.status)
            
            logger.info(f"开始执行队列: {queue_id} - {queue.name}")
            
            for i, action in enumerate(queue.actions):
                if not self.is_running or queue.status == QueueStatus.CANCELLED:
                    break
                
                # 暂停检查
                while queue.status == QueueStatus.PAUSED:
                    await asyncio.sleep(0.1)
                
                queue.current_action_index = i
                
                try:
                    # 执行前延迟
                    if action.delay_before > 0:
                        await asyncio.sleep(action.delay_before)
                    
                    # 执行动作
                    await self._execute_action(action)
                    
                    # 执行后延迟
                    if action.delay_after > 0:
                        await asyncio.sleep(action.delay_after)
                    
                    queue.completed_actions += 1
                    self._notify_progress_change(queue_id, queue.get_progress())
                    
                    logger.debug(f"动作执行完成: {queue_id}[{i}] - {action.description}")
                    
                except Exception as e:
                    logger.error(f"动作执行失败: {queue_id}[{i}] - {e}")
                    queue.failed_actions += 1
                    
                    # 重试逻辑
                    if action.retry_count > 0:
                        for retry in range(action.retry_count):
                            try:
                                logger.info(f"重试动作: {queue_id}[{i}] - 第{retry+1}次")
                                await asyncio.sleep(1.0)  # 重试延迟
                                await self._execute_action(action)
                                queue.completed_actions += 1
                                queue.failed_actions -= 1
                                break
                            except Exception as retry_e:
                                logger.error(f"重试失败: {retry_e}")
                                if retry == action.retry_count - 1:
                                    queue.error_message = str(e)
            
            # 完成执行
            queue.end_time = datetime.now()
            if queue.status != QueueStatus.CANCELLED:
                queue.status = QueueStatus.COMPLETED if queue.failed_actions == 0 else QueueStatus.FAILED
            
            self._notify_status_change(queue_id, queue.status)
            self._notify_progress_change(queue_id, queue.get_progress())
            
            logger.info(f"队列执行完成: {queue_id} - 状态: {queue.status.value}")
            
        except asyncio.CancelledError:
            queue.status = QueueStatus.CANCELLED
            queue.end_time = datetime.now()
            self._notify_status_change(queue_id, queue.status)
            logger.info(f"队列执行被取消: {queue_id}")
        except Exception as e:
            queue.status = QueueStatus.FAILED
            queue.end_time = datetime.now()
            queue.error_message = str(e)
            self._notify_status_change(queue_id, queue.status)
            logger.error(f"队列执行失败: {queue_id} - {e}")
        finally:
            # 清理执行任务
            if queue_id in self.execution_tasks:
                del self.execution_tasks[queue_id]
    
    async def _execute_action(self, action: QueueAction):
        """执行单个动作"""
        try:
            if action.action_type == ActionType.DELAY:
                # 延迟动作
                delay_time = action.parameters.get("time", action.duration)
                await asyncio.sleep(delay_time)
                
            elif action.action_type in [ActionType.MOVE_HEAD_AND_EAR, 
                                      ActionType.PLAY_EXPRESSION, 
                                      ActionType.STOP_EXPRESSION]:
                # 机器人控制动作
                control_data = action.parameters.copy()
                control_data["action"] = action.action_type.value
                
                # 调用机器人控制接口
                if self.robot_callback:
                    if asyncio.iscoroutinefunction(self.robot_callback):
                        result = await self.robot_callback(control_data)
                    else:
                        result = self.robot_callback(control_data)
                    
                    # 检查执行结果
                    if isinstance(result, dict) and not result.get("success", True):
                        raise Exception(f"机器人控制失败: {result.get('error', 'Unknown error')}")
                
                # 动作持续时间
                if action.duration > 0:
                    await asyncio.sleep(action.duration)
                    
            elif action.action_type == ActionType.CUSTOM:
                # 自定义动作
                logger.info(f"执行自定义动作: {action.parameters}")
                if action.duration > 0:
                    await asyncio.sleep(action.duration)
            
        except Exception as e:
            logger.error(f"动作执行失败: {action.action_type.value} - {e}")
            raise

    def pause_queue(self, queue_id: str) -> bool:
        """暂停队列执行"""
        try:
            if queue_id not in self.queues:
                return False

            queue = self.queues[queue_id]
            if queue.status == QueueStatus.RUNNING:
                queue.status = QueueStatus.PAUSED
                self._notify_status_change(queue_id, queue.status)
                logger.info(f"队列已暂停: {queue_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"暂停队列失败: {e}")
            return False

    def resume_queue(self, queue_id: str) -> bool:
        """恢复队列执行"""
        try:
            if queue_id not in self.queues:
                return False

            queue = self.queues[queue_id]
            if queue.status == QueueStatus.PAUSED:
                queue.status = QueueStatus.RUNNING
                self._notify_status_change(queue_id, queue.status)
                logger.info(f"队列已恢复: {queue_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"恢复队列失败: {e}")
            return False

    def cancel_queue(self, queue_id: str) -> bool:
        """取消队列执行"""
        try:
            if queue_id not in self.queues:
                return False

            queue = self.queues[queue_id]

            # 取消执行任务
            if queue_id in self.execution_tasks:
                task = self.execution_tasks[queue_id]
                if not task.done():
                    task.cancel()

            queue.status = QueueStatus.CANCELLED
            queue.end_time = datetime.now()
            self._notify_status_change(queue_id, queue.status)

            logger.info(f"队列已取消: {queue_id}")
            return True

        except Exception as e:
            logger.error(f"取消队列失败: {e}")
            return False

    def get_queue_status(self, queue_id: str) -> Optional[Dict[str, Any]]:
        """获取队列状态"""
        if queue_id in self.queues:
            return self.queues[queue_id].to_dict()
        return None

    def list_queues(self, status_filter: Optional[QueueStatus] = None) -> List[Dict[str, Any]]:
        """列出队列"""
        queues = list(self.queues.values())

        if status_filter:
            queues = [q for q in queues if q.status == status_filter]

        # 按优先级和创建时间排序
        queues.sort(key=lambda q: (q.priority.value, q.created_time), reverse=True)

        return [q.to_dict() for q in queues]

    def delete_queue(self, queue_id: str) -> bool:
        """删除队列"""
        try:
            if queue_id not in self.queues:
                return False

            queue = self.queues[queue_id]

            # 如果队列正在执行，先取消
            if queue.status == QueueStatus.RUNNING:
                self.cancel_queue(queue_id)

            del self.queues[queue_id]
            logger.info(f"队列已删除: {queue_id}")
            return True

        except Exception as e:
            logger.error(f"删除队列失败: {e}")
            return False

    def clear_completed_queues(self) -> int:
        """清理已完成的队列"""
        try:
            completed_statuses = {QueueStatus.COMPLETED, QueueStatus.FAILED, QueueStatus.CANCELLED}
            to_delete = [qid for qid, queue in self.queues.items()
                        if queue.status in completed_statuses]

            count = 0
            for queue_id in to_delete:
                if self.delete_queue(queue_id):
                    count += 1

            logger.info(f"已清理 {count} 个已完成的队列")
            return count

        except Exception as e:
            logger.error(f"清理队列失败: {e}")
            return 0

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            total_queues = len(self.queues)
            status_counts = {}

            for status in QueueStatus:
                status_counts[status.value] = len([q for q in self.queues.values()
                                                 if q.status == status])

            running_queues = [q for q in self.queues.values() if q.status == QueueStatus.RUNNING]

            return {
                "total_queues": total_queues,
                "status_counts": status_counts,
                "running_queues": len(running_queues),
                "max_concurrent_queues": self.max_concurrent_queues,
                "queue_history_limit": self.queue_history_limit,
                "is_running": self.is_running,
                "active_tasks": len(self.execution_tasks)
            }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def create_and_submit_queue(self, name: str, actions: List[Dict[str, Any]],
                               priority: Priority = Priority.NORMAL,
                               metadata: Optional[Dict[str, Any]] = None) -> str:
        """创建并立即提交队列"""
        try:
            queue_id = self.create_queue(name, actions, priority, metadata)

            # 异步提交执行
            asyncio.create_task(self.submit_queue(queue_id))

            return queue_id

        except Exception as e:
            logger.error(f"创建并提交队列失败: {e}")
            raise
