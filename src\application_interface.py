"""
应用层接口封装
为应用层提供标准化的动作队列提交接口
"""

from typing import Dict, Any, List, Optional
from loguru import logger
from .action_queue_manager import ActionQueueManager, Priority, QueueStatus


class ApplicationInterface:
    """应用层接口"""
    
    def __init__(self, queue_manager: ActionQueueManager):
        """
        初始化应用层接口
        
        Args:
            queue_manager: 动作队列管理器
        """
        self.queue_manager = queue_manager
    
    async def submit_action_queue(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交动作队列
        
        Args:
            request: 队列请求数据
            
        Returns:
            提交结果
        """
        try:
            # 验证请求格式
            if not isinstance(request, dict):
                raise ValueError("请求必须是JSON对象")
            
            # 提取队列信息
            queue_name = request.get("name", "")
            if not queue_name:
                raise ValueError("队列名称不能为空")
            
            actions = request.get("actions", [])
            if not actions:
                raise ValueError("动作列表不能为空")
            
            # 解析优先级
            priority_str = request.get("priority", "normal").lower()
            priority_mapping = {
                "low": Priority.LOW,
                "normal": Priority.NORMAL,
                "high": Priority.HIGH,
                "urgent": Priority.URGENT
            }
            priority = priority_mapping.get(priority_str, Priority.NORMAL)
            
            # 提取元数据
            metadata = request.get("metadata", {})
            
            # 提取执行选项
            auto_start = request.get("auto_start", True)
            
            # 创建队列
            queue_id = self.queue_manager.create_queue(
                name=queue_name,
                actions=actions,
                priority=priority,
                metadata=metadata
            )
            
            # 自动启动执行
            if auto_start:
                success = await self.queue_manager.submit_queue(queue_id)
                if not success:
                    return {
                        "success": False,
                        "error": "队列提交失败",
                        "queue_id": queue_id
                    }
            
            return {
                "success": True,
                "queue_id": queue_id,
                "message": f"动作队列已{'提交执行' if auto_start else '创建'}",
                "queue_info": self.queue_manager.get_queue_status(queue_id)
            }
            
        except Exception as e:
            error_msg = f"提交动作队列失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    async def control_queue(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        控制队列执行
        
        Args:
            request: 控制请求
            
        Returns:
            控制结果
        """
        try:
            queue_id = request.get("queue_id")
            if not queue_id:
                raise ValueError("队列ID不能为空")
            
            action = request.get("action", "").lower()
            if not action:
                raise ValueError("控制动作不能为空")
            
            result = False
            message = ""
            
            if action == "start":
                result = await self.queue_manager.submit_queue(queue_id)
                message = "队列已启动" if result else "队列启动失败"
                
            elif action == "pause":
                result = self.queue_manager.pause_queue(queue_id)
                message = "队列已暂停" if result else "队列暂停失败"
                
            elif action == "resume":
                result = self.queue_manager.resume_queue(queue_id)
                message = "队列已恢复" if result else "队列恢复失败"
                
            elif action == "cancel":
                result = self.queue_manager.cancel_queue(queue_id)
                message = "队列已取消" if result else "队列取消失败"
                
            elif action == "delete":
                result = self.queue_manager.delete_queue(queue_id)
                message = "队列已删除" if result else "队列删除失败"
                
            else:
                raise ValueError(f"不支持的控制动作: {action}")
            
            return {
                "success": result,
                "message": message,
                "queue_id": queue_id,
                "action": action
            }
            
        except Exception as e:
            error_msg = f"控制队列失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def get_queue_status(self, queue_id: str) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            status = self.queue_manager.get_queue_status(queue_id)
            if status:
                return {
                    "success": True,
                    "queue_status": status
                }
            else:
                return {
                    "success": False,
                    "error": "队列不存在"
                }
                
        except Exception as e:
            error_msg = f"获取队列状态失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def list_queues(self, status_filter: Optional[str] = None) -> Dict[str, Any]:
        """列出队列"""
        try:
            # 解析状态过滤器
            status_enum = None
            if status_filter:
                status_mapping = {
                    "pending": QueueStatus.PENDING,
                    "running": QueueStatus.RUNNING,
                    "paused": QueueStatus.PAUSED,
                    "completed": QueueStatus.COMPLETED,
                    "cancelled": QueueStatus.CANCELLED,
                    "failed": QueueStatus.FAILED
                }
                status_enum = status_mapping.get(status_filter.lower())
            
            queues = self.queue_manager.list_queues(status_enum)
            
            return {
                "success": True,
                "queues": queues,
                "total_count": len(queues)
            }
            
        except Exception as e:
            error_msg = f"列出队列失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            stats = self.queue_manager.get_statistics()
            return {
                "success": True,
                "statistics": stats
            }
            
        except Exception as e:
            error_msg = f"获取统计信息失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def clear_completed_queues(self) -> Dict[str, Any]:
        """清理已完成的队列"""
        try:
            count = self.queue_manager.clear_completed_queues()
            return {
                "success": True,
                "message": f"已清理 {count} 个队列",
                "cleared_count": count
            }
            
        except Exception as e:
            error_msg = f"清理队列失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    # 便捷方法：快速创建常用动作队列
    
    async def submit_expression_sequence(self, expressions: List[str], 
                                       name: str = "表情序列",
                                       delays: Optional[List[float]] = None) -> Dict[str, Any]:
        """
        提交表情序列
        
        Args:
            expressions: 表情名称列表
            name: 队列名称
            delays: 每个表情后的延迟时间列表
            
        Returns:
            提交结果
        """
        try:
            actions = []
            delays = delays or [1.0] * len(expressions)
            
            for i, expression in enumerate(expressions):
                # 播放表情动作
                actions.append({
                    "action_type": "play_expression",
                    "parameters": {"expression": expression},
                    "description": f"播放表情: {expression}"
                })
                
                # 添加延迟
                if i < len(delays) and delays[i] > 0:
                    actions.append({
                        "action_type": "delay",
                        "parameters": {"time": delays[i]},
                        "description": f"延迟 {delays[i]} 秒"
                    })
            
            request = {
                "name": name,
                "actions": actions,
                "priority": "normal",
                "auto_start": True
            }
            
            return await self.submit_action_queue(request)
            
        except Exception as e:
            error_msg = f"提交表情序列失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    async def submit_movement_sequence(self, movements: List[Dict[str, str]], 
                                     name: str = "动作序列",
                                     duration: float = 1.0) -> Dict[str, Any]:
        """
        提交动作序列
        
        Args:
            movements: 动作列表，每个动作包含头部和耳朵控制参数
            name: 队列名称
            duration: 每个动作的持续时间
            
        Returns:
            提交结果
        """
        try:
            actions = []
            
            for movement in movements:
                action = {
                    "action_type": "move_head_and_ear",
                    "parameters": movement,
                    "duration": duration,
                    "description": f"执行动作: {movement}"
                }
                actions.append(action)
            
            request = {
                "name": name,
                "actions": actions,
                "priority": "normal",
                "auto_start": True
            }
            
            return await self.submit_action_queue(request)
            
        except Exception as e:
            error_msg = f"提交动作序列失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
