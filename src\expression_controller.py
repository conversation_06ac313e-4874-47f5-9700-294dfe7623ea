"""
表情控制接口
提供表情控制的API接口，支持WebSocket和遥控器两种控制方式
"""

import asyncio
from typing import Dict, Any, Optional
from loguru import logger
from .expression_mapper import ExpressionMapper, ExpressionSequence, RobotAction
from .robot_client import RobotClient


class ExpressionController:
    """表情控制器"""
    
    def __init__(self, robot_client: RobotClient):
        """
        初始化表情控制器
        
        Args:
            robot_client: 机器人客户端实例
        """
        self.robot_client = robot_client
        self.expression_mapper = ExpressionMapper()
        self.current_expression_task = None
        self.is_running = False
    
    async def start(self):
        """启动表情控制器"""
        self.is_running = True
        logger.info("表情控制器已启动")
    
    def stop(self):
        """停止表情控制器"""
        self.is_running = False
        
        # 停止当前表情任务
        if self.current_expression_task and not self.current_expression_task.done():
            self.current_expression_task.cancel()
        
        logger.info("表情控制器已停止")
    
    async def handle_expression_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理表情控制消息
        
        Args:
            message: 表情控制消息
            
        Returns:
            处理结果
        """
        try:
            # 验证消息格式
            if not isinstance(message, dict):
                raise ValueError("消息必须是JSON对象")
            
            action = message.get("action")
            if not action:
                raise ValueError("消息中缺少'action'字段")
            
            if action == "play_expression":
                # 播放表情
                result = await self._handle_play_expression(message)
            elif action == "stop_expression":
                # 停止当前表情
                result = await self._handle_stop_expression(message)
            elif action == "list_expressions":
                # 列出所有表情
                result = await self._handle_list_expressions(message)
            elif action == "get_expression_info":
                # 获取表情信息
                result = await self._handle_get_expression_info(message)
            elif action == "create_expression":
                # 创建自定义表情
                result = await self._handle_create_expression(message)
            elif action == "move_head_and_ear":
                # 兼容原有的直接控制接口
                result = await self._handle_direct_control(message)
            else:
                raise ValueError(f"不支持的动作类型: {action}")
            
            logger.info(f"表情消息处理成功: {action}")
            return result
            
        except Exception as e:
            error_msg = f"表情消息处理失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "message": message
            }
    
    async def _handle_play_expression(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理播放表情请求"""
        expression_name = message.get("expression")
        if not expression_name:
            raise ValueError("缺少'expression'字段")
        
        expression = self.expression_mapper.get_expression(expression_name)
        if not expression:
            raise ValueError(f"未找到表情: {expression_name}")
        
        # 停止当前表情
        if self.current_expression_task and not self.current_expression_task.done():
            self.current_expression_task.cancel()
            await asyncio.sleep(0.1)  # 短暂等待取消完成
        
        # 启动新表情
        self.current_expression_task = asyncio.create_task(
            self._execute_expression(expression)
        )
        
        return {
            "success": True,
            "data": {
                "message": f"表情播放已启动: {expression_name}",
                "expression": expression_name,
                "description": expression.description,
                "actions_count": len(expression.actions),
                "loop": expression.loop,
                "loop_count": expression.loop_count
            },
            "status_code": 200
        }
    
    async def _handle_stop_expression(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理停止表情请求"""
        if self.current_expression_task and not self.current_expression_task.done():
            self.current_expression_task.cancel()
            
            # 回到中性位置
            await self._execute_neutral_position()
            
            return {
                "success": True,
                "data": {
                    "message": "表情播放已停止，回到中性位置"
                },
                "status_code": 200
            }
        else:
            return {
                "success": True,
                "data": {
                    "message": "当前没有表情在播放"
                },
                "status_code": 200
            }
    
    async def _handle_list_expressions(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理列出表情请求"""
        expressions = self.expression_mapper.list_expressions()
        expression_details = []
        
        for name in expressions:
            expr = self.expression_mapper.get_expression(name)
            if expr:
                expression_details.append({
                    "name": name,
                    "description": expr.description,
                    "actions_count": len(expr.actions),
                    "loop": expr.loop,
                    "loop_count": expr.loop_count
                })
        
        return {
            "success": True,
            "data": {
                "expressions": expression_details,
                "total_count": len(expressions)
            },
            "status_code": 200
        }
    
    async def _handle_get_expression_info(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取表情信息请求"""
        expression_name = message.get("expression")
        if not expression_name:
            raise ValueError("缺少'expression'字段")
        
        expression = self.expression_mapper.get_expression(expression_name)
        if not expression:
            raise ValueError(f"未找到表情: {expression_name}")
        
        return {
            "success": True,
            "data": {
                "expression": expression.to_dict()
            },
            "status_code": 200
        }
    
    async def _handle_create_expression(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理创建表情请求"""
        expression_data = message.get("expression_data")
        if not expression_data:
            raise ValueError("缺少'expression_data'字段")
        
        # 验证表情数据格式
        name = expression_data.get("name")
        description = expression_data.get("description", "")
        actions_data = expression_data.get("actions", [])
        
        if not name:
            raise ValueError("表情名称不能为空")
        
        if not actions_data:
            raise ValueError("表情动作不能为空")
        
        # 构建动作列表
        actions = []
        for action_data in actions_data:
            actions.append(RobotAction(**action_data))
        
        # 创建表情
        expression = ExpressionSequence(
            name=name,
            description=description,
            actions=actions,
            loop=expression_data.get("loop", False),
            loop_count=expression_data.get("loop_count", 1)
        )
        
        # 添加到映射器
        self.expression_mapper.add_expression(expression)
        self.expression_mapper.save_config()
        
        return {
            "success": True,
            "data": {
                "message": f"表情创建成功: {name}",
                "expression": expression.to_dict()
            },
            "status_code": 200
        }
    
    async def _handle_direct_control(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理直接控制请求（兼容原有接口）"""
        # 直接调用机器人客户端
        yaw_cmd = message.get("yaw_cmd")
        pitch_cmd = message.get("pitch_cmd")
        left_ear_cmd = message.get("left_ear_cmd")
        right_ear_cmd = message.get("right_ear_cmd")
        
        result = await self.robot_client.move_head_and_ear_direct(
            yaw_cmd=yaw_cmd,
            pitch_cmd=pitch_cmd,
            left_ear_cmd=left_ear_cmd,
            right_ear_cmd=right_ear_cmd
        )
        
        return result
    
    async def _execute_expression(self, expression: ExpressionSequence):
        """执行表情动作序列"""
        try:
            loop_count = expression.loop_count if expression.loop else 1
            current_loop = 0
            
            logger.info(f"开始执行表情: {expression.name}")
            
            while (loop_count == -1 or current_loop < loop_count) and self.is_running:
                for i, action in enumerate(expression.actions):
                    if not self.is_running:
                        break
                    
                    logger.debug(f"执行动作 {i+1}/{len(expression.actions)}: {action}")
                    
                    # 延迟执行
                    if action.delay > 0:
                        await asyncio.sleep(action.delay)
                    
                    # 执行动作
                    control_data = action.to_dict()
                    if control_data:  # 只有非空动作才发送
                        result = await self.robot_client.move_head_and_ear_direct(
                            yaw_cmd=action.yaw_cmd,
                            pitch_cmd=action.pitch_cmd,
                            left_ear_cmd=action.left_ear_cmd,
                            right_ear_cmd=action.right_ear_cmd
                        )
                        
                        if not result.get("success", False):
                            logger.error(f"动作执行失败: {result.get('error', 'Unknown error')}")
                    
                    # 动作持续时间
                    if action.duration > 0:
                        await asyncio.sleep(action.duration)
                
                current_loop += 1
                
                # 如果是循环播放，短暂暂停
                if expression.loop and current_loop < loop_count:
                    await asyncio.sleep(0.2)
            
            logger.info(f"表情执行完成: {expression.name}")
            
        except asyncio.CancelledError:
            logger.info(f"表情执行被取消: {expression.name}")
            raise
        except Exception as e:
            logger.error(f"执行表情失败: {e}")
            raise
    
    async def _execute_neutral_position(self):
        """执行回到中性位置"""
        try:
            await self.robot_client.move_head_and_ear_direct(
                yaw_cmd="center",
                pitch_cmd="center",
                left_ear_cmd="center",
                right_ear_cmd="center"
            )
            logger.info("已回到中性位置")
        except Exception as e:
            logger.error(f"回到中性位置失败: {e}")
    
    async def play_expression_by_name(self, expression_name: str) -> Dict[str, Any]:
        """通过名称播放表情（供遥控器调用）"""
        message = {
            "action": "play_expression",
            "expression": expression_name
        }
        return await self.handle_expression_message(message)
    
    def get_status(self) -> Dict[str, Any]:
        """获取控制器状态"""
        return {
            "is_running": self.is_running,
            "current_expression_running": (
                self.current_expression_task is not None and 
                not self.current_expression_task.done()
            ),
            "available_expressions": self.expression_mapper.list_expressions()
        }
