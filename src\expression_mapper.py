"""
表情映射系统
处理表情名称到视频文件的映射和机器人动作序列
"""

import json
import os
from typing import Dict, Any, List, Optional
from loguru import logger
from dataclasses import dataclass


@dataclass
class RobotAction:
    """机器人动作定义"""
    yaw_cmd: Optional[str] = None
    pitch_cmd: Optional[str] = None
    left_ear_cmd: Optional[str] = None
    right_ear_cmd: Optional[str] = None
    delay: float = 0.0  # 执行前延迟（秒）
    duration: float = 1.0  # 动作持续时间（秒）

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "yaw_cmd": self.yaw_cmd,
            "pitch_cmd": self.pitch_cmd,
            "left_ear_cmd": self.left_ear_cmd,
            "right_ear_cmd": self.right_ear_cmd,
            "delay": self.delay,
            "duration": self.duration
        }


@dataclass
class ExpressionSequence:
    """表情序列定义"""
    name: str
    description: str
    video_file: str = ""
    direction: str = "center"
    actions: List[RobotAction] = None
    loop: bool = False
    loop_count: int = 1

    def __post_init__(self):
        """初始化后处理"""
        if self.actions is None:
            # 如果没有指定动作，根据direction创建默认动作
            self.actions = [self._create_default_action()]

    def _create_default_action(self) -> RobotAction:
        """根据方向创建默认动作"""
        direction_map = {
            "left": RobotAction(yaw_cmd="left", duration=1.0),
            "right": RobotAction(yaw_cmd="right", duration=1.0),
            "up": RobotAction(pitch_cmd="up", duration=1.0),
            "down": RobotAction(pitch_cmd="down", duration=1.0),
            "center": RobotAction(yaw_cmd="center", pitch_cmd="center", duration=1.0)
        }
        return direction_map.get(self.direction, RobotAction(duration=1.0))

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "video_file": self.video_file,
            "direction": self.direction,
            "actions": [action.to_dict() for action in self.actions],
            "loop": self.loop,
            "loop_count": self.loop_count
        }


class ExpressionMapper:
    """表情映射器"""
    
    def __init__(self, config_file: str = "config/expressions.json"):
        self.config_file = config_file
        self.expressions: Dict[str, ExpressionSequence] = {}
        self.remote_mappings: Dict[str, Dict[str, str]] = {}
        self._load_default_expressions()
        self._load_config()
    
    def _load_default_expressions(self):
        """加载默认表情定义（简化版本）"""

        # 基于您当前系统的表情映射
        self.expressions["yaw_left"] = ExpressionSequence(
            name="yaw_left",
            description="左转表情",
            video_file="left.gif",
            direction="left"
        )

        self.expressions["yaw_right"] = ExpressionSequence(
            name="yaw_right",
            description="右转表情",
            video_file="right.gif",
            direction="right"
        )

        self.expressions["pitch_up"] = ExpressionSequence(
            name="pitch_up",
            description="抬头表情",
            video_file="xingxingyan.gif",
            direction="up"
        )

        self.expressions["pitch_down"] = ExpressionSequence(
            name="pitch_down",
            description="低头表情",
            video_file="cry.gif",
            direction="down"
        )

        self.expressions["center"] = ExpressionSequence(
            name="center",
            description="中心表情",
            video_file="meimao.gif",
            direction="center"
        )
    
    def _load_remote_mappings(self):
        """加载遥控器按键到表情的映射（简化版本）"""
        self.remote_mappings = {
            "joy_remote": {
                # Joy遥控器的表情映射
                "yaw_left": "yaw_left",
                "yaw_right": "yaw_right",
                "pitch_up": "pitch_up",
                "pitch_down": "pitch_down",
                "yaw_center": "center",
                "pitch_center": "center"
            }
        }
    
    def _load_config(self):
        """从配置文件加载表情定义"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载自定义表情
                if "expressions" in config:
                    for name, expr_data in config["expressions"].items():
                        # 加载动作列表
                        actions = []
                        if "actions" in expr_data:
                            for action_data in expr_data["actions"]:
                                actions.append(RobotAction(**action_data))

                        self.expressions[name] = ExpressionSequence(
                            name=name,
                            description=expr_data.get("description", ""),
                            video_file=expr_data.get("video_file", ""),
                            direction=expr_data.get("direction", "center"),
                            actions=actions if actions else None,
                            loop=expr_data.get("loop", False),
                            loop_count=expr_data.get("loop_count", 1)
                        )

                # 加载遥控器映射
                if "remote_mappings" in config:
                    self.remote_mappings.update(config["remote_mappings"])
                else:
                    self._load_remote_mappings()

                logger.info(f"表情配置已加载: {self.config_file}")
            else:
                self._load_remote_mappings()
                self.save_config()
                logger.info("使用默认表情配置，已保存到文件")

        except Exception as e:
            logger.error(f"加载表情配置失败: {e}")
            self._load_remote_mappings()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            config = {
                "expressions": {name: expr.to_dict() for name, expr in self.expressions.items()},
                "remote_mappings": self.remote_mappings
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"表情配置已保存: {self.config_file}")
            
        except Exception as e:
            logger.error(f"保存表情配置失败: {e}")
    
    def get_expression(self, name: str) -> Optional[ExpressionSequence]:
        """获取表情定义"""
        return self.expressions.get(name)
    
    def map_remote_signal(self, source: str, signal: str) -> Optional[str]:
        """将遥控器信号映射到表情名称"""
        if source in self.remote_mappings:
            return self.remote_mappings[source].get(signal)
        return None
    
    def list_expressions(self) -> List[str]:
        """列出所有可用表情"""
        return list(self.expressions.keys())
    
    def add_expression(self, expression: ExpressionSequence):
        """添加新表情"""
        self.expressions[expression.name] = expression
        logger.info(f"新表情已添加: {expression.name}")
    
    def remove_expression(self, name: str) -> bool:
        """删除表情"""
        if name in self.expressions:
            del self.expressions[name]
            logger.info(f"表情已删除: {name}")
            return True
        return False
