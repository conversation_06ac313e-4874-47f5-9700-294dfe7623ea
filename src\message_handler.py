"""
消息处理器
负责解析WebSocket消息并转换为机器人控制指令
支持原有的直接控制和新的表情控制
"""

from typing import Dict, Any
from loguru import logger
from .robot_client import RobotClient
from .expression_controller import ExpressionController


class MessageHandler:
    """WebSocket消息处理器"""

    def __init__(self, robot_client: RobotClient):
        self.robot_client = robot_client
        self.expression_controller = ExpressionController(robot_client)
    
    async def start(self):
        """启动消息处理器"""
        await self.expression_controller.start()
        logger.info("消息处理器已启动")

    def stop(self):
        """停止消息处理器"""
        self.expression_controller.stop()
        logger.info("消息处理器已停止")

    async def handle_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理WebSocket消息
        支持原有的直接控制和新的表情控制

        Args:
            message: 解析后的JSON消息

        Returns:
            处理结果
        """
        try:
            # 验证消息格式
            if not isinstance(message, dict):
                raise ValueError("消息必须是JSON对象")

            action = message.get("action")
            if not action:
                raise ValueError("消息中缺少'action'字段")

            # 表情控制相关动作
            if action in ["play_expression", "stop_expression", "list_expressions",
                         "get_expression_info", "create_expression"]:
                result = await self.expression_controller.handle_expression_message(message)
            elif action == "move_head_and_ear":
                # 处理标准的头部和耳朵控制指令（完全匹配demo接口）
                # 可以通过表情控制器处理（兼容性）或直接处理
                result = await self.expression_controller.handle_expression_message(message)
            else:
                raise ValueError(f"不支持的动作类型: {action}")

            logger.info(f"消息处理成功: {action}")
            return result

        except Exception as e:
            error_msg = f"消息处理失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "message": message
            }
    


    async def _handle_move_head_and_ear(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理统一的头部和耳朵控制指令（匹配demo接口）"""
        # 提取各个控制参数，完全匹配demo接口
        yaw_cmd = message.get("yaw_cmd")          # 'left'/'center'/'right' 或 None
        pitch_cmd = message.get("pitch_cmd")      # 'up'/'center'/'down' 或 None
        left_ear_cmd = message.get("left_ear_cmd")   # 'down'/'center'/'up' 或 None
        right_ear_cmd = message.get("right_ear_cmd") # 'down'/'center'/'up' 或 None

        # 验证至少有一个控制参数
        if not any([yaw_cmd, pitch_cmd, left_ear_cmd, right_ear_cmd]):
            raise ValueError("至少需要指定一个控制参数: yaw_cmd, pitch_cmd, left_ear_cmd, right_ear_cmd")

        # 验证yaw_cmd（如果提供）
        if yaw_cmd:
            valid_yaw = ["left", "center", "right"]
            if yaw_cmd not in valid_yaw:
                raise ValueError(f"无效的yaw_cmd: {yaw_cmd}，支持的值: {', '.join(valid_yaw)}")

        # 验证pitch_cmd（如果提供）
        if pitch_cmd:
            valid_pitch = ["up", "center", "down"]
            if pitch_cmd not in valid_pitch:
                raise ValueError(f"无效的pitch_cmd: {pitch_cmd}，支持的值: {', '.join(valid_pitch)}")

        # 验证left_ear_cmd（如果提供）
        if left_ear_cmd:
            valid_ear = ["down", "center", "up"]
            if left_ear_cmd not in valid_ear:
                raise ValueError(f"无效的left_ear_cmd: {left_ear_cmd}，支持的值: {', '.join(valid_ear)}")

        # 验证right_ear_cmd（如果提供）
        if right_ear_cmd:
            valid_ear = ["down", "center", "up"]
            if right_ear_cmd not in valid_ear:
                raise ValueError(f"无效的right_ear_cmd: {right_ear_cmd}，支持的值: {', '.join(valid_ear)}")

        return await self.robot_client.move_head_and_ear_direct(yaw_cmd, pitch_cmd, left_ear_cmd, right_ear_cmd)


