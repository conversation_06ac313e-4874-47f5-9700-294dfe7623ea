"""
跨平台鼠标控制工具模块
提供隐藏/显示鼠标光标的跨平台解决方案
"""

import platform
import ctypes
import subprocess
from loguru import logger
from typing import Optional


class MouseController:
    """跨平台鼠标控制器"""
    
    def __init__(self):
        self.system = platform.system()
        self.is_cursor_hidden = False
        
    def hide_cursor(self) -> bool:
        """
        隐藏鼠标光标
        
        Returns:
            bool: 是否成功隐藏
        """
        try:
            if self.system == "Windows":
                return self._hide_cursor_windows()
            elif self.system == "Linux":
                return self._hide_cursor_linux()
            elif self.system == "Darwin":  # macOS
                return self._hide_cursor_macos()
            else:
                logger.warning(f"不支持的系统: {self.system}")
                return False
                
        except Exception as e:
            logger.error(f"隐藏鼠标光标失败: {e}")
            return False
    
    def show_cursor(self) -> bool:
        """
        显示鼠标光标
        
        Returns:
            bool: 是否成功显示
        """
        try:
            if self.system == "Windows":
                return self._show_cursor_windows()
            elif self.system == "Linux":
                return self._show_cursor_linux()
            elif self.system == "Darwin":  # macOS
                return self._show_cursor_macos()
            else:
                logger.warning(f"不支持的系统: {self.system}")
                return False
                
        except Exception as e:
            logger.error(f"显示鼠标光标失败: {e}")
            return False
    
    def _hide_cursor_windows(self) -> bool:
        """Windows系统隐藏鼠标光标"""
        try:
            # 多次调用确保成功
            for _ in range(3):
                ctypes.windll.user32.ShowCursor(False)
            self.is_cursor_hidden = True
            logger.info("鼠标光标已隐藏 (Windows)")
            return True
        except Exception as e:
            logger.error(f"Windows隐藏鼠标失败: {e}")
            return False
    
    def _show_cursor_windows(self) -> bool:
        """Windows系统显示鼠标光标"""
        try:
            # 多次调用确保成功
            for _ in range(3):
                ctypes.windll.user32.ShowCursor(True)
            self.is_cursor_hidden = False
            logger.info("鼠标光标已显示 (Windows)")
            return True
        except Exception as e:
            logger.error(f"Windows显示鼠标失败: {e}")
            return False
    
    def _hide_cursor_linux(self) -> bool:
        """Linux系统隐藏鼠标光标"""
        try:
            # 方法1: 使用xdotool移动鼠标到角落
            try:
                subprocess.run(["xdotool", "mousemove", "0", "0"], 
                             check=True, capture_output=True)
                logger.info("鼠标已移至角落 (Linux xdotool)")
                self.is_cursor_hidden = True
                return True
            except (subprocess.SubprocessError, FileNotFoundError):
                pass
            
            # 方法2: 使用unclutter隐藏鼠标
            try:
                subprocess.run(["unclutter", "-idle", "0"], 
                             check=True, capture_output=True)
                logger.info("鼠标光标已隐藏 (Linux unclutter)")
                self.is_cursor_hidden = True
                return True
            except (subprocess.SubprocessError, FileNotFoundError):
                pass
            
            # 方法3: 使用xinput禁用鼠标
            try:
                # 获取鼠标设备ID
                result = subprocess.run(["xinput", "list"], 
                                      capture_output=True, text=True, check=True)
                # 这里可以解析输出找到鼠标设备并禁用
                # 为了安全起见，暂时不实现这个方法
                pass
            except (subprocess.SubprocessError, FileNotFoundError):
                pass
            
            logger.warning("Linux系统未找到可用的鼠标隐藏工具 (xdotool, unclutter)")
            return False
            
        except Exception as e:
            logger.error(f"Linux隐藏鼠标失败: {e}")
            return False
    
    def _show_cursor_linux(self) -> bool:
        """Linux系统显示鼠标光标"""
        try:
            # 如果使用了unclutter，需要杀死进程
            try:
                subprocess.run(["pkill", "unclutter"], 
                             check=False, capture_output=True)
                logger.info("已停止unclutter进程")
            except Exception:
                pass
            
            self.is_cursor_hidden = False
            logger.info("鼠标光标已恢复 (Linux)")
            return True
            
        except Exception as e:
            logger.error(f"Linux显示鼠标失败: {e}")
            return False
    
    def _hide_cursor_macos(self) -> bool:
        """macOS系统隐藏鼠标光标"""
        try:
            # macOS可以使用CGDisplayHideCursor
            # 这里需要使用PyObjC或其他方法
            logger.warning("macOS鼠标隐藏功能尚未实现")
            return False
        except Exception as e:
            logger.error(f"macOS隐藏鼠标失败: {e}")
            return False
    
    def _show_cursor_macos(self) -> bool:
        """macOS系统显示鼠标光标"""
        try:
            # macOS可以使用CGDisplayShowCursor
            logger.warning("macOS鼠标显示功能尚未实现")
            return False
        except Exception as e:
            logger.error(f"macOS显示鼠标失败: {e}")
            return False
    
    def move_to_corner(self) -> bool:
        """将鼠标移动到屏幕角落作为备用方案"""
        try:
            import pyautogui
            screen_width, screen_height = pyautogui.size()
            pyautogui.moveTo(screen_width-1, screen_height-1)
            logger.info("鼠标已移至屏幕角落")
            return True
        except Exception as e:
            logger.error(f"移动鼠标到角落失败: {e}")
            return False


# 全局鼠标控制器实例
_mouse_controller: Optional[MouseController] = None


def get_mouse_controller() -> MouseController:
    """获取全局鼠标控制器实例"""
    global _mouse_controller
    if _mouse_controller is None:
        _mouse_controller = MouseController()
    return _mouse_controller


def hide_mouse_cursor() -> bool:
    """隐藏鼠标光标的便捷函数"""
    controller = get_mouse_controller()
    success = controller.hide_cursor()
    
    # 如果隐藏失败，尝试移动到角落作为备用方案
    if not success:
        logger.info("尝试备用方案：移动鼠标到角落")
        controller.move_to_corner()
    
    return success


def show_mouse_cursor() -> bool:
    """显示鼠标光标的便捷函数"""
    controller = get_mouse_controller()
    return controller.show_cursor()


def is_cursor_hidden() -> bool:
    """检查鼠标光标是否已隐藏"""
    controller = get_mouse_controller()
    return controller.is_cursor_hidden
