"""
队列执行引擎
提供高级的队列执行控制功能，包括调度、监控和错误处理
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from loguru import logger
from .action_queue_manager import ActionQueueManager, QueueStatus, Priority


@dataclass
class ExecutionPolicy:
    """执行策略"""
    max_concurrent_queues: int = 3          # 最大并发队列数
    queue_timeout: float = 300.0            # 队列超时时间（秒）
    retry_failed_queues: bool = True        # 是否重试失败的队列
    max_retry_attempts: int = 3             # 最大重试次数
    retry_delay: float = 5.0                # 重试延迟（秒）
    auto_cleanup_completed: bool = True     # 自动清理已完成队列
    cleanup_interval: float = 60.0          # 清理间隔（秒）
    priority_scheduling: bool = True        # 优先级调度
    load_balancing: bool = True             # 负载均衡


class QueueExecutionEngine:
    """队列执行引擎"""
    
    def __init__(self, queue_manager: ActionQueueManager, policy: Optional[ExecutionPolicy] = None):
        """
        初始化执行引擎
        
        Args:
            queue_manager: 队列管理器
            policy: 执行策略
        """
        self.queue_manager = queue_manager
        self.policy = policy or ExecutionPolicy()
        
        # 执行状态
        self.is_running = False
        self.scheduler_task = None
        self.monitor_task = None
        self.cleanup_task = None
        
        # 统计信息
        self.execution_stats = {
            "total_executed": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "retried_executions": 0,
            "average_execution_time": 0.0,
            "last_execution_time": None
        }
        
        # 重试队列
        self.retry_queue: List[Dict[str, Any]] = []

        # 已处理的队列记录（避免重复处理）
        self.processed_queues: set = set()

        # 事件回调
        self.event_callbacks: Dict[str, List[Callable]] = {
            "queue_started": [],
            "queue_completed": [],
            "queue_failed": [],
            "queue_retried": [],
            "engine_overloaded": []
        }
    
    async def start(self):
        """启动执行引擎"""
        if self.is_running:
            logger.warning("执行引擎已在运行")
            return
        
        self.is_running = True
        
        # 启动调度器
        if self.policy.priority_scheduling:
            self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        # 启动监控器
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        # 启动清理器
        if self.policy.auto_cleanup_completed:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info("队列执行引擎已启动")
    
    def stop(self):
        """停止执行引擎"""
        self.is_running = False
        
        # 停止所有任务
        for task in [self.scheduler_task, self.monitor_task, self.cleanup_task]:
            if task and not task.done():
                task.cancel()
        
        logger.info("队列执行引擎已停止")
    
    def add_event_callback(self, event: str, callback: Callable):
        """添加事件回调"""
        if event in self.event_callbacks:
            self.event_callbacks[event].append(callback)
    
    def _trigger_event(self, event: str, *args, **kwargs):
        """触发事件"""
        for callback in self.event_callbacks.get(event, []):
            try:
                if asyncio.iscoroutinefunction(callback):
                    asyncio.create_task(callback(*args, **kwargs))
                else:
                    callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"事件回调执行失败: {event} - {e}")
    
    async def _scheduler_loop(self):
        """调度器循环"""
        logger.info("队列调度器已启动")
        
        while self.is_running:
            try:
                await self._schedule_queues()
                await asyncio.sleep(1.0)  # 调度间隔
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调度器错误: {e}")
                await asyncio.sleep(5.0)
        
        logger.info("队列调度器已停止")
    
    async def _schedule_queues(self):
        """调度队列执行"""
        try:
            # 获取待执行队列
            pending_queues = self.queue_manager.list_queues(QueueStatus.PENDING)
            
            if not pending_queues:
                return
            
            # 检查当前运行的队列数
            running_count = len([q for q in self.queue_manager.list_queues() 
                               if q["status"] == "running"])
            
            if running_count >= self.policy.max_concurrent_queues:
                # 检查是否过载
                if running_count > self.policy.max_concurrent_queues:
                    self._trigger_event("engine_overloaded", running_count)
                return
            
            # 按优先级排序
            if self.policy.priority_scheduling:
                pending_queues.sort(key=lambda q: q["priority"], reverse=True)
            
            # 提交执行
            available_slots = self.policy.max_concurrent_queues - running_count
            for queue in pending_queues[:available_slots]:
                queue_id = queue["queue_id"]
                success = await self.queue_manager.submit_queue(queue_id)
                
                if success:
                    self._trigger_event("queue_started", queue_id)
                    logger.info(f"队列已调度执行: {queue_id}")
                else:
                    logger.warning(f"队列调度失败: {queue_id}")
        
        except Exception as e:
            logger.error(f"调度队列失败: {e}")
    
    async def _monitor_loop(self):
        """监控循环"""
        logger.info("队列监控器已启动")
        
        while self.is_running:
            try:
                await self._monitor_queues()
                await self._process_retry_queue()
                await asyncio.sleep(2.0)  # 监控间隔
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控器错误: {e}")
                await asyncio.sleep(5.0)
        
        logger.info("队列监控器已停止")
    
    async def _monitor_queues(self):
        """监控队列状态"""
        try:
            all_queues = self.queue_manager.list_queues()
            current_time = datetime.now()
            
            for queue_data in all_queues:
                queue_id = queue_data["queue_id"]
                status = queue_data["status"]
                start_time_str = queue_data.get("start_time")
                
                # 检查超时
                if status == "running" and start_time_str:
                    start_time = datetime.fromisoformat(start_time_str)
                    elapsed = (current_time - start_time).total_seconds()
                    
                    if elapsed > self.policy.queue_timeout:
                        logger.warning(f"队列执行超时，取消执行: {queue_id}")
                        self.queue_manager.cancel_queue(queue_id)
                        continue
                
                # 处理完成的队列（避免重复处理）
                if status == "completed":
                    if queue_id not in self.processed_queues:
                        self._handle_queue_completed(queue_data)
                        self.processed_queues.add(queue_id)
                elif status == "failed":
                    if queue_id not in self.processed_queues:
                        self._handle_queue_failed(queue_data)
                        self.processed_queues.add(queue_id)
        
        except Exception as e:
            logger.error(f"监控队列失败: {e}")
    
    def _handle_queue_completed(self, queue_data: Dict[str, Any]):
        """处理已完成的队列"""
        queue_id = queue_data["queue_id"]
        
        # 更新统计
        self.execution_stats["total_executed"] += 1
        self.execution_stats["successful_executions"] += 1
        self.execution_stats["last_execution_time"] = datetime.now()
        
        # 计算执行时间
        if queue_data.get("start_time") and queue_data.get("end_time"):
            start_time = datetime.fromisoformat(queue_data["start_time"])
            end_time = datetime.fromisoformat(queue_data["end_time"])
            execution_time = (end_time - start_time).total_seconds()
            
            # 更新平均执行时间
            total = self.execution_stats["total_executed"]
            current_avg = self.execution_stats["average_execution_time"]
            self.execution_stats["average_execution_time"] = (
                (current_avg * (total - 1) + execution_time) / total
            )
        
        self._trigger_event("queue_completed", queue_id, queue_data)
        logger.info(f"队列执行完成: {queue_id}")
    
    def _handle_queue_failed(self, queue_data: Dict[str, Any]):
        """处理失败的队列"""
        queue_id = queue_data["queue_id"]
        
        # 更新统计
        self.execution_stats["total_executed"] += 1
        self.execution_stats["failed_executions"] += 1
        self.execution_stats["last_execution_time"] = datetime.now()
        
        # 检查是否需要重试
        if self.policy.retry_failed_queues:
            retry_count = queue_data.get("metadata", {}).get("retry_count", 0)
            
            if retry_count < self.policy.max_retry_attempts:
                # 添加到重试队列
                retry_item = {
                    "queue_id": queue_id,
                    "retry_count": retry_count + 1,
                    "retry_time": datetime.now() + timedelta(seconds=self.policy.retry_delay),
                    "original_data": queue_data
                }
                self.retry_queue.append(retry_item)
                
                logger.info(f"队列将重试: {queue_id} (第{retry_count + 1}次)")
                return
        
        self._trigger_event("queue_failed", queue_id, queue_data)
        logger.error(f"队列执行失败: {queue_id}")
    
    async def _process_retry_queue(self):
        """处理重试队列"""
        if not self.retry_queue:
            return
        
        current_time = datetime.now()
        to_retry = []
        
        # 找到需要重试的队列
        for i, retry_item in enumerate(self.retry_queue):
            if current_time >= retry_item["retry_time"]:
                to_retry.append((i, retry_item))
        
        # 从后往前删除，避免索引问题
        for i, retry_item in reversed(to_retry):
            del self.retry_queue[i]
            
            try:
                # 重新创建队列
                original_data = retry_item["original_data"]
                queue_name = f"{original_data['name']} (重试{retry_item['retry_count']})"
                
                # 更新元数据
                metadata = original_data.get("metadata", {})
                metadata["retry_count"] = retry_item["retry_count"]
                metadata["original_queue_id"] = retry_item["queue_id"]
                
                # 创建新队列
                new_queue_id = self.queue_manager.create_queue(
                    name=queue_name,
                    actions=original_data["actions"],
                    priority=Priority(original_data["priority"]),
                    metadata=metadata
                )
                
                self.execution_stats["retried_executions"] += 1
                self._trigger_event("queue_retried", new_queue_id, retry_item)
                
                logger.info(f"队列重试已创建: {new_queue_id}")
                
            except Exception as e:
                logger.error(f"创建重试队列失败: {e}")
    
    async def _cleanup_loop(self):
        """清理循环"""
        logger.info("队列清理器已启动")
        
        while self.is_running:
            try:
                await asyncio.sleep(self.policy.cleanup_interval)
                
                if self.policy.auto_cleanup_completed:
                    # 获取要清理的队列ID列表
                    completed_queues = self.queue_manager.list_queues(QueueStatus.COMPLETED)
                    failed_queues = self.queue_manager.list_queues(QueueStatus.FAILED)
                    cleanup_queue_ids = {q["queue_id"] for q in completed_queues + failed_queues}

                    # 清理队列
                    count = self.queue_manager.clear_completed_queues()
                    if count > 0:
                        logger.info(f"自动清理了 {count} 个已完成的队列")

                        # 清理已处理队列记录
                        self.processed_queues = self.processed_queues - cleanup_queue_ids
                        logger.debug(f"清理了 {len(cleanup_queue_ids)} 个已处理队列记录")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理器错误: {e}")
        
        logger.info("队列清理器已停止")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        return {
            "is_running": self.is_running,
            "policy": {
                "max_concurrent_queues": self.policy.max_concurrent_queues,
                "queue_timeout": self.policy.queue_timeout,
                "retry_failed_queues": self.policy.retry_failed_queues,
                "max_retry_attempts": self.policy.max_retry_attempts,
                "auto_cleanup_completed": self.policy.auto_cleanup_completed,
                "priority_scheduling": self.policy.priority_scheduling
            },
            "execution_stats": self.execution_stats.copy(),
            "retry_queue_size": len(self.retry_queue),
            "active_tasks": {
                "scheduler": self.scheduler_task is not None and not self.scheduler_task.done(),
                "monitor": self.monitor_task is not None and not self.monitor_task.done(),
                "cleanup": self.cleanup_task is not None and not self.cleanup_task.done()
            }
        }
    
    def update_policy(self, new_policy: ExecutionPolicy):
        """更新执行策略"""
        self.policy = new_policy
        logger.info("执行策略已更新")
    
    def get_retry_queue_status(self) -> List[Dict[str, Any]]:
        """获取重试队列状态"""
        return [
            {
                "queue_id": item["queue_id"],
                "retry_count": item["retry_count"],
                "retry_time": item["retry_time"].isoformat(),
                "original_name": item["original_data"]["name"]
            }
            for item in self.retry_queue
        ]
