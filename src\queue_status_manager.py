"""
队列状态管理
提供队列状态查询、进度跟踪和执行结果反馈机制
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
from loguru import logger
import json


@dataclass
class StatusSubscription:
    """状态订阅"""
    subscription_id: str
    queue_id: Optional[str]  # None表示订阅所有队列
    callback: Callable[[Dict[str, Any]], None]
    event_types: List[str]   # 订阅的事件类型
    created_time: datetime
    last_update: datetime


class QueueStatusManager:
    """队列状态管理器"""
    
    def __init__(self):
        """初始化状态管理器"""
        self.subscriptions: Dict[str, StatusSubscription] = {}
        self.status_history: Dict[str, List[Dict[str, Any]]] = {}
        self.progress_cache: Dict[str, float] = {}
        self.is_running = False
        
        # 状态更新间隔
        self.update_interval = 1.0
        self.history_limit = 100
        
        # 事件类型
        self.event_types = {
            "status_changed",
            "progress_updated", 
            "queue_created",
            "queue_started",
            "queue_completed",
            "queue_failed",
            "queue_cancelled",
            "action_started",
            "action_completed",
            "action_failed"
        }
    
    async def start(self):
        """启动状态管理器"""
        self.is_running = True
        logger.info("队列状态管理器已启动")
    
    def stop(self):
        """停止状态管理器"""
        self.is_running = False
        logger.info("队列状态管理器已停止")
    
    def subscribe_status(self, queue_id: Optional[str] = None, 
                        callback: Optional[Callable] = None,
                        event_types: Optional[List[str]] = None) -> str:
        """
        订阅队列状态更新
        
        Args:
            queue_id: 队列ID，None表示订阅所有队列
            callback: 状态更新回调函数
            event_types: 订阅的事件类型列表
            
        Returns:
            订阅ID
        """
        import uuid
        
        subscription_id = str(uuid.uuid4())
        event_types = event_types or list(self.event_types)
        
        subscription = StatusSubscription(
            subscription_id=subscription_id,
            queue_id=queue_id,
            callback=callback,
            event_types=event_types,
            created_time=datetime.now(),
            last_update=datetime.now()
        )
        
        self.subscriptions[subscription_id] = subscription
        
        logger.info(f"状态订阅已创建: {subscription_id} (队列: {queue_id or 'ALL'})")
        return subscription_id
    
    def unsubscribe_status(self, subscription_id: str) -> bool:
        """取消状态订阅"""
        if subscription_id in self.subscriptions:
            del self.subscriptions[subscription_id]
            logger.info(f"状态订阅已取消: {subscription_id}")
            return True
        return False
    
    def notify_status_change(self, queue_id: str, event_type: str, data: Dict[str, Any]):
        """通知状态变化"""
        try:
            # 记录状态历史
            self._record_status_history(queue_id, event_type, data)
            
            # 通知订阅者
            self._notify_subscribers(queue_id, event_type, data)
            
        except Exception as e:
            logger.error(f"通知状态变化失败: {e}")
    
    def _record_status_history(self, queue_id: str, event_type: str, data: Dict[str, Any]):
        """记录状态历史"""
        if queue_id not in self.status_history:
            self.status_history[queue_id] = []
        
        history_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "data": data.copy()
        }
        
        self.status_history[queue_id].append(history_entry)
        
        # 限制历史记录数量
        if len(self.status_history[queue_id]) > self.history_limit:
            self.status_history[queue_id] = self.status_history[queue_id][-self.history_limit:]
    
    def _notify_subscribers(self, queue_id: str, event_type: str, data: Dict[str, Any]):
        """通知订阅者"""
        notification_data = {
            "queue_id": queue_id,
            "event_type": event_type,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        
        for subscription in self.subscriptions.values():
            try:
                # 检查是否匹配订阅条件
                if subscription.queue_id and subscription.queue_id != queue_id:
                    continue
                
                if event_type not in subscription.event_types:
                    continue
                
                # 调用回调函数
                if subscription.callback:
                    if asyncio.iscoroutinefunction(subscription.callback):
                        asyncio.create_task(subscription.callback(notification_data))
                    else:
                        subscription.callback(notification_data)
                
                subscription.last_update = datetime.now()
                
            except Exception as e:
                logger.error(f"通知订阅者失败: {subscription.subscription_id} - {e}")
    
    def get_queue_status_history(self, queue_id: str, 
                                limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取队列状态历史"""
        history = self.status_history.get(queue_id, [])
        
        if limit:
            history = history[-limit:]
        
        return history
    
    def get_all_status_history(self, limit: Optional[int] = None) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有队列的状态历史"""
        result = {}
        
        for queue_id, history in self.status_history.items():
            if limit:
                result[queue_id] = history[-limit:]
            else:
                result[queue_id] = history.copy()
        
        return result
    
    def get_subscriptions(self) -> List[Dict[str, Any]]:
        """获取所有订阅信息"""
        return [
            {
                "subscription_id": sub.subscription_id,
                "queue_id": sub.queue_id,
                "event_types": sub.event_types,
                "created_time": sub.created_time.isoformat(),
                "last_update": sub.last_update.isoformat()
            }
            for sub in self.subscriptions.values()
        ]
    
    def update_progress(self, queue_id: str, progress: float):
        """更新队列进度"""
        old_progress = self.progress_cache.get(queue_id, 0.0)
        self.progress_cache[queue_id] = progress
        
        # 只有进度有显著变化时才通知
        if abs(progress - old_progress) >= 0.01:  # 1%的变化
            self.notify_status_change(queue_id, "progress_updated", {
                "progress": progress,
                "previous_progress": old_progress
            })
    
    def get_progress(self, queue_id: str) -> float:
        """获取队列进度"""
        return self.progress_cache.get(queue_id, 0.0)
    
    def clear_history(self, queue_id: Optional[str] = None):
        """清理状态历史"""
        if queue_id:
            if queue_id in self.status_history:
                del self.status_history[queue_id]
                logger.info(f"已清理队列状态历史: {queue_id}")
        else:
            self.status_history.clear()
            logger.info("已清理所有队列状态历史")
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        total_queues = len(self.status_history)
        total_subscriptions = len(self.subscriptions)
        
        # 统计事件类型
        event_counts = {}
        for history in self.status_history.values():
            for entry in history:
                event_type = entry["event_type"]
                event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        return {
            "total_queues_tracked": total_queues,
            "total_subscriptions": total_subscriptions,
            "event_counts": event_counts,
            "history_limit": self.history_limit,
            "is_running": self.is_running
        }
    
    # WebSocket支持方法
    
    async def create_websocket_subscription(self, websocket, queue_id: Optional[str] = None,
                                          event_types: Optional[List[str]] = None) -> str:
        """创建WebSocket状态订阅"""
        async def websocket_callback(data: Dict[str, Any]):
            try:
                await websocket.send_text(json.dumps(data))
            except Exception as e:
                logger.error(f"WebSocket发送失败: {e}")
        
        return self.subscribe_status(queue_id, websocket_callback, event_types)
    
    def create_sse_subscription(self, queue_id: Optional[str] = None,
                               event_types: Optional[List[str]] = None) -> tuple:
        """创建Server-Sent Events订阅"""
        import queue
        
        event_queue = queue.Queue()
        
        def sse_callback(data: Dict[str, Any]):
            try:
                event_queue.put(data, block=False)
            except queue.Full:
                logger.warning("SSE事件队列已满，丢弃事件")
        
        subscription_id = self.subscribe_status(queue_id, sse_callback, event_types)
        
        return subscription_id, event_queue
    
    # 高级查询方法
    
    def query_status_history(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询状态历史"""
        results = []
        
        queue_id_filter = filters.get("queue_id")
        event_type_filter = filters.get("event_type")
        start_time_filter = filters.get("start_time")
        end_time_filter = filters.get("end_time")
        
        for queue_id, history in self.status_history.items():
            if queue_id_filter and queue_id != queue_id_filter:
                continue
            
            for entry in history:
                # 事件类型过滤
                if event_type_filter and entry["event_type"] != event_type_filter:
                    continue
                
                # 时间过滤
                entry_time = datetime.fromisoformat(entry["timestamp"])
                
                if start_time_filter:
                    start_time = datetime.fromisoformat(start_time_filter)
                    if entry_time < start_time:
                        continue
                
                if end_time_filter:
                    end_time = datetime.fromisoformat(end_time_filter)
                    if entry_time > end_time:
                        continue
                
                results.append({
                    "queue_id": queue_id,
                    **entry
                })
        
        # 按时间排序
        results.sort(key=lambda x: x["timestamp"])
        
        return results
    
    def get_queue_metrics(self, queue_id: str) -> Dict[str, Any]:
        """获取队列指标"""
        history = self.status_history.get(queue_id, [])
        
        if not history:
            return {}
        
        # 计算指标
        total_events = len(history)
        event_types = {}
        
        for entry in history:
            event_type = entry["event_type"]
            event_types[event_type] = event_types.get(event_type, 0) + 1
        
        # 计算时间跨度
        if total_events > 1:
            start_time = datetime.fromisoformat(history[0]["timestamp"])
            end_time = datetime.fromisoformat(history[-1]["timestamp"])
            duration = (end_time - start_time).total_seconds()
        else:
            duration = 0
        
        return {
            "queue_id": queue_id,
            "total_events": total_events,
            "event_types": event_types,
            "duration_seconds": duration,
            "current_progress": self.get_progress(queue_id),
            "first_event_time": history[0]["timestamp"] if history else None,
            "last_event_time": history[-1]["timestamp"] if history else None
        }
