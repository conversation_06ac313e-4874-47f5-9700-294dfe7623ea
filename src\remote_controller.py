"""
遥控器控制模块
支持ROS2 topic监听的遥控器控制
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Callable, Optional
from loguru import logger

# ROS2依赖
try:
    import rclpy
    from std_msgs.msg import String
    ROS2_AVAILABLE = True
except ImportError:
    ROS2_AVAILABLE = False
    logger.warning("ROS2不可用，遥控器功能将被禁用")


class RemoteControllerBase(ABC):
    """遥控器控制器基类"""

    def __init__(self, callback: Callable[[Dict[str, Any]], None]):
        """
        初始化遥控器控制器

        Args:
            callback: 信号回调函数，接收遥控器信号数据
        """
        self.callback = callback
        self.is_running = False

    @abstractmethod
    async def start_listening(self):
        """开始监听遥控器信号"""
        pass

    @abstractmethod
    def stop_listening(self):
        """停止监听遥控器信号"""
        pass

    def _send_signal(self, signal_data: Dict[str, Any]):
        """发送信号到回调函数"""
        try:
            if self.callback:
                self.callback(signal_data)
        except Exception as e:
            logger.error(f"发送遥控器信号失败: {e}")





class ROS2ExpressionTrigger(RemoteControllerBase):
    """ROS2表情触发器 - 监听/expression_trigger topic"""

    def __init__(self, callback: Callable[[Dict[str, Any]], None],
                 topic_name: str = "/expression"):
        super().__init__(callback)
        self.topic_name = topic_name
        self.node = None
        self.subscription = None

    async def start_listening(self):
        """开始监听ROS2表情触发topic"""
        if not ROS2_AVAILABLE:
            logger.error("ROS2不可用，无法启动表情触发监听")
            return

        try:
            # 初始化ROS2节点（如果还没有初始化）
            if not rclpy.ok():
                rclpy.init()

            # 创建节点
            self.node = rclpy.create_node('expression_trigger_listener')

            # 创建订阅
            self.subscription = self.node.create_subscription(
                String,
                self.topic_name,
                self._expression_callback,
                10
            )

            self.is_running = True
            logger.info(f"ROS2表情触发监听已启动，topic: {self.topic_name}")

            # 启动ROS2事件循环
            asyncio.create_task(self._ros2_spin_loop())

        except Exception as e:
            logger.error(f"ROS2表情触发初始化失败: {e}")

    def _expression_callback(self, msg):
        """表情触发消息回调"""
        try:
            expression_data = msg.data.strip()
            logger.info(f"收到表情触发: {expression_data}")

            # 映射表情名称到视频文件和方向
            expression_mapping = {
                'yaw_left': {'video': 'left.gif', 'direction': 'left'},
                'yaw_right': {'video': 'right.gif', 'direction': 'right'},
                'yaw_center': {'video': 'meimao.gif', 'direction': 'center'},
                'pitch_up': {'video': 'xingxingyan.gif', 'direction': 'up'},
                'pitch_down': {'video': 'cry.gif', 'direction': 'down'},
                'pitch_center': {'video': 'meimao.gif', 'direction': 'center'}
            }

            # 获取映射信息
            mapping = expression_mapping.get(expression_data)
            if mapping:
                control_data = {
                    "action": "play_video",  # 直接播放视频，不需要系统表情
                    "video_file": mapping['video'],
                    "direction": mapping['direction'],
                    "source": "joy_remote",
                    "type": "expression_trigger",
                    "original_expression": expression_data
                }
            else:
                # 未知表情，使用默认处理
                control_data = {
                    "action": "play_video",
                    "video_file": "meimao.gif",
                    "direction": "center",
                    "source": "joy_remote",
                    "type": "expression_trigger",
                    "original_expression": expression_data
                }

            self._send_signal(control_data)

        except Exception as e:
            logger.error(f"处理表情触发消息失败: {e}")

    async def _ros2_spin_loop(self):
        """ROS2事件循环 - 优化版本，减少延迟"""
        while self.is_running and rclpy.ok():
            try:
                # 减少timeout时间，提高响应速度
                rclpy.spin_once(self.node, timeout_sec=0.001)
                # 减少sleep时间，提高消息处理频率
                await asyncio.sleep(0.001)  # 1ms间隔，平衡性能和CPU使用
            except Exception as e:
                logger.error(f"ROS2事件循环错误: {e}")
                break

    def stop_listening(self):
        """停止监听"""
        self.is_running = False
        if self.node:
            try:
                self.node.destroy_node()
                logger.info("ROS2表情触发节点已销毁")
            except Exception as e:
                logger.error(f"销毁ROS2节点失败: {e}")
        self.node = None
        self.subscription = None














