"""
遥控器信号处理模块
接收遥控器信号，转换为标准控制指令，并触发表情动作
"""

import asyncio
from typing import Dict, Any, Callable, Optional
from loguru import logger
from .expression_mapper import ExpressionMapper, ExpressionSequence
from .remote_controller import ROS2ExpressionTrigger

# 导入Joy监听器
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'robot_interface'))
    from head_command_listen import ExpressionControlNode
    import rclpy
    JOY_LISTENER_AVAILABLE = True
except ImportError:
    JOY_LISTENER_AVAILABLE = False
    logger.warning("Joy监听器不可用，ROS2环境未配置")


class RemoteSignalProcessor:
    """遥控器信号处理器"""
    
    def __init__(self, robot_callback: Callable[[Dict[str, Any]], Any]):
        """
        初始化信号处理器
        
        Args:
            robot_callback: 机器人控制回调函数，接收标准控制指令
        """
        self.robot_callback = robot_callback
        self.expression_mapper = ExpressionMapper()
        self.is_running = False

        # Joy监听器
        self.joy_listener = None

        # 直接创建唯一的表情触发器
        self.expression_trigger = ROS2ExpressionTrigger(self._handle_remote_signal)

        logger.info("表情触发器已初始化")
    
    async def start(self, controllers: Optional[list] = None):
        """
        启动信号处理器

        Args:
            controllers: 要启动的遥控器列表，支持 "expression_trigger" 和 "joy_listener"
        """
        try:
            self.is_running = True

            # 启动表情触发器（如果在控制器列表中）
            if not controllers or "expression_trigger" in controllers:
                await self.expression_trigger.start_listening()
                logger.info("表情触发器已启动")

            # 启动Joy监听器（如果在控制器列表中）
            if controllers and "joy_listener" in controllers:
                await self._start_joy_listener()

            logger.info("遥控器信号处理器已启动")

        except Exception as e:
            logger.error(f"启动信号处理器失败: {e}")

    async def _start_joy_listener(self):
        """启动Joy监听器"""
        if not JOY_LISTENER_AVAILABLE:
            logger.warning("Joy监听器不可用，跳过启动")
            return

        try:
            # 初始化ROS2（如果还没有初始化）
            if not rclpy.ok():
                rclpy.init()

            # 创建Joy监听器节点（使用新版本，无需回调参数）
            self.joy_listener = ExpressionControlNode()

            # 在后台运行ROS2节点
            asyncio.create_task(self._spin_joy_listener())

            logger.info("Joy监听器已启动")

        except Exception as e:
            logger.error(f"启动Joy监听器失败: {e}")

    async def _spin_joy_listener(self):
        """运行Joy监听器的ROS2事件循环 - 优化版本"""
        while self.is_running and rclpy.ok():
            try:
                # 优化：减少timeout和sleep时间，提高响应速度
                rclpy.spin_once(self.joy_listener, timeout_sec=0.001)
                await asyncio.sleep(0.001)  # 1ms间隔，提高消息处理频率
            except Exception as e:
                logger.error(f"Joy监听器事件循环错误: {e}")
                break



    def stop(self):
        """停止信号处理器"""
        try:
            self.is_running = False

            # 停止表情触发器
            if self.expression_trigger:
                self.expression_trigger.stop_listening()
                logger.info("表情触发器已停止")

            # 停止Joy监听器
            if self.joy_listener:
                try:
                    self.joy_listener.destroy_node()
                    logger.info("Joy监听器已停止")
                except Exception as e:
                    logger.error(f"停止Joy监听器失败: {e}")
                self.joy_listener = None

            logger.info("遥控器信号处理器已停止")

        except Exception as e:
            logger.error(f"停止信号处理器失败: {e}")
    
    def _handle_remote_signal(self, signal_data: Dict[str, Any]):
        """
        处理遥控器信号

        Args:
            signal_data: 遥控器信号数据
        """
        try:
            if not self.is_running:
                return

            source = signal_data.get("source", "unknown")
            signal_type = signal_data.get("type", "unknown")

            logger.info(f"收到遥控器信号: {source} - {signal_type} - {signal_data}")

            # 处理信号类型
            if signal_type == "expression_trigger":
                # 表情触发信号，直接转发给机器人
                asyncio.create_task(self.robot_callback(signal_data))

            else:
                logger.warning(f"未知信号类型: {signal_type}")

        except Exception as e:
            logger.error(f"处理遥控器信号失败: {e}")
    

    


    

    

    
    def get_available_expressions(self) -> list:
        """获取可用表情列表"""
        return self.expression_mapper.list_expressions()
    
    def get_active_controllers(self) -> list:
        """获取活跃的遥控器列表"""
        active = []
        if self.expression_trigger and self.expression_trigger.is_running:
            active.append("ROS2ExpressionTrigger")
        if self.joy_listener:
            active.append("ExpressionControlNode")
        return active
    
    def add_custom_expression(self, expression: ExpressionSequence):
        """添加自定义表情"""
        self.expression_mapper.add_expression(expression)
        self.expression_mapper.save_config()
    
    def update_remote_mapping(self, source: str, signal: str, expression: str):
        """更新遥控器映射"""
        if source not in self.expression_mapper.remote_mappings:
            self.expression_mapper.remote_mappings[source] = {}
        
        self.expression_mapper.remote_mappings[source][signal] = expression
        self.expression_mapper.save_config()
        
        logger.info(f"遥控器映射已更新: {source}.{signal} -> {expression}")
    
    def get_signal_status(self) -> Dict[str, Any]:
        """获取信号处理器状态"""
        return {
            "is_running": self.is_running,
            "active_controllers": self.get_active_controllers(),
            "available_expressions": self.get_available_expressions()
        }
