"""
机器人控制接口客户端
负责调用ROS2机器人控制接口
"""

import asyncio
from typing import Dict, Any
from loguru import logger
import sys
import os

# 添加demo目录到Python路径
demo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'robot_interface')
if demo_path not in sys.path:
    sys.path.append(demo_path)

try:
    import rclpy
    from head_control_interface import HeadControlNode
    ROS2_AVAILABLE = True
except ImportError as e:
    logger.warning(f"ROS2不可用: {e}")
    ROS2_AVAILABLE = False


class RobotClient:
    """机器人控制接口客户端"""

    def __init__(self):
        self.node = None
        self.ros_initialized = False

        if ROS2_AVAILABLE:
            try:
                # 初始化ROS2
                rclpy.init()
                self.node = HeadControlNode()
                self.ros_initialized = True
                logger.info("ROS2机器人控制节点初始化成功")
            except Exception as e:
                logger.error(f"ROS2初始化失败: {e}")
                self.ros_initialized = False
        else:
            logger.warning("ROS2不可用，将使用模拟模式")
    


    async def move_head_and_ear_direct(self, yaw_cmd=None, pitch_cmd=None, left_ear_cmd=None, right_ear_cmd=None) -> Dict[str, Any]:
        """
        直接调用demo接口的move_head_and_ear方法
        完全匹配demo/README.md中的接口规范

        Args:
            yaw_cmd: 'left'/'center'/'right' 或 None
            pitch_cmd: 'up'/'center'/'down' 或 None
            left_ear_cmd: 'down'/'center'/'up' 或 None
            right_ear_cmd: 'down'/'center'/'up' 或 None

        Returns:
            控制结果
        """
        try:
            if not self.ros_initialized:
                # 模拟模式：返回成功，避免重试堆积
                logger.warning("ROS2未初始化，使用模拟模式")
                return {
                    "success": True,
                    "message": "模拟模式执行成功",
                    "mode": "simulation",
                    "parameters": {
                        "yaw_cmd": yaw_cmd,
                        "pitch_cmd": pitch_cmd,
                        "left_ear_cmd": left_ear_cmd,
                        "right_ear_cmd": right_ear_cmd
                    }
                }

            # 直接调用demo接口，参数名完全一致
            self.node.move_head_and_ear(
                yaw_cmd=yaw_cmd,
                pitch_cmd=pitch_cmd,
                left_ear_cmd=left_ear_cmd,
                right_ear_cmd=right_ear_cmd
            )

            # 构建响应信息
            actions = []
            if yaw_cmd:
                actions.append(f"yaw:{yaw_cmd}")
            if pitch_cmd:
                actions.append(f"pitch:{pitch_cmd}")
            if left_ear_cmd:
                actions.append(f"left_ear:{left_ear_cmd}")
            if right_ear_cmd:
                actions.append(f"right_ear:{right_ear_cmd}")

            logger.info(f"机器人控制成功: {', '.join(actions)}")

            return {
                "success": True,
                "data": {
                    "message": f"控制成功: {', '.join(actions)}",
                    "yaw_cmd": yaw_cmd,
                    "pitch_cmd": pitch_cmd,
                    "left_ear_cmd": left_ear_cmd,
                    "right_ear_cmd": right_ear_cmd,
                    "timestamp": asyncio.get_event_loop().time()
                },
                "status_code": 200
            }

        except Exception as e:
            error_msg = f"move_head_and_ear控制失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "error_type": "move_head_and_ear_error"
            }

    def __del__(self):
        """清理资源"""
        if self.ros_initialized and ROS2_AVAILABLE:
            try:
                if self.node:
                    self.node.destroy_node()
                rclpy.shutdown()
                logger.info("ROS2资源清理完成")
            except Exception as e:
                logger.error(f"ROS2资源清理失败: {e}")
