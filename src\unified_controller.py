"""
统一控制管理器
整合WebSocket、遥控器等多种控制源，实现统一的消息处理
"""

import asyncio
from typing import Dict, Any, Optional, List
from loguru import logger
from .robot_client import RobotClient
from .expression_controller import ExpressionController
from .remote_signal_processor import RemoteSignalProcessor
from .message_handler import MessageHandler
from .action_queue_manager import ActionQueueManager
from .application_interface import ApplicationInterface
from .queue_execution_engine import QueueExecutionEngine, ExecutionPolicy
from .queue_status_manager import QueueStatusManager


class UnifiedController:
    """统一控制管理器"""
    
    def __init__(self):
        """初始化统一控制器"""
        # 初始化核心组件
        self.robot_client = RobotClient()
        self.expression_controller = ExpressionController(self.robot_client)
        self.message_handler = MessageHandler(self.robot_client)

        # 应用层广播回调
        self.app_broadcast_callback = None

    def set_app_broadcast_callback(self, callback):
        """设置应用层广播回调函数"""
        self.app_broadcast_callback = callback

        # 初始化动作队列系统
        self.queue_manager = ActionQueueManager(self._handle_robot_control)
        self.application_interface = ApplicationInterface(self.queue_manager)
        self.queue_execution_engine = QueueExecutionEngine(self.queue_manager)
        self.queue_status_manager = QueueStatusManager()

        # 初始化遥控器信号处理器
        self.remote_processor = RemoteSignalProcessor(self._handle_remote_control)
        
        # 控制器状态
        self.is_running = False
        self.enabled_controllers = {
            "websocket": True,
            "expression_trigger": True,  # Joy表情触发器，默认启用
            "joy_listener": True,        # Joy监听器，默认启用
            "action_queue": True         # 动作队列默认启用
        }
        
        # 消息统计
        self.message_stats = {
            "websocket_messages": 0,
            "remote_signals": 0,
            "expression_plays": 0,
            "direct_controls": 0,
            "action_queues": 0,
            "errors": 0
        }
    
    async def start(self, controllers: Optional[List[str]] = None):
        """
        启动统一控制器
        
        Args:
            controllers: 要启动的控制器列表，None表示使用默认配置
        """
        try:
            self.is_running = True
            
            # 启动核心组件
            await self.expression_controller.start()
            await self.message_handler.start()

            # 启动动作队列系统
            if self.enabled_controllers.get("action_queue", True):
                await self.queue_manager.start()
                await self.queue_execution_engine.start()
                await self.queue_status_manager.start()
            
            # 启动遥控器（如果指定）
            if controllers:
                remote_controllers = [c for c in controllers if c in ["expression_trigger", "joy_listener"]]
                if remote_controllers:
                    await self.remote_processor.start(remote_controllers)
                    for controller in remote_controllers:
                        if controller in self.enabled_controllers:
                            self.enabled_controllers[controller] = True
            else:
                # 使用默认配置启动遥控器
                enabled_remotes = [k for k, v in self.enabled_controllers.items()
                                 if v and k in ["expression_trigger", "joy_listener"]]
                if enabled_remotes:
                    await self.remote_processor.start(enabled_remotes)
            
            logger.info(f"统一控制器已启动，启用的控制器: {self._get_enabled_controllers()}")
            
        except Exception as e:
            logger.error(f"启动统一控制器失败: {e}")
            raise
    
    def stop(self):
        """停止统一控制器"""
        try:
            self.is_running = False
            
            # 停止所有组件
            self.expression_controller.stop()
            self.message_handler.stop()
            self.remote_processor.stop()

            # 停止动作队列系统
            self.queue_manager.stop()
            self.queue_execution_engine.stop()
            self.queue_status_manager.stop()
            
            logger.info("统一控制器已停止")
            
        except Exception as e:
            logger.error(f"停止统一控制器失败: {e}")
    
    async def handle_websocket_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理WebSocket消息

        Args:
            message: WebSocket消息

        Returns:
            处理结果
        """
        try:
            if not self.enabled_controllers.get("websocket", True):
                return {
                    "success": False,
                    "error": "WebSocket控制已禁用"
                }

            self.message_stats["websocket_messages"] += 1

            # 通过消息处理器处理
            result = await self.message_handler.handle_message(message)

            # 更新统计
            if result.get("success", False):
                action = message.get("action", "")
                if action in ["play_expression", "stop_expression"]:
                    self.message_stats["expression_plays"] += 1
                elif action == "move_head_and_ear":
                    self.message_stats["direct_controls"] += 1

                    # 触发应用层广播回调（用于Live2D消息转发）
                    if self.app_broadcast_callback and action == "move_head_and_ear":
                        await self.app_broadcast_callback(message)
                        logger.info(f"已触发应用层广播回调: {action}")
            else:
                self.message_stats["errors"] += 1

            return result
            
        except Exception as e:
            self.message_stats["errors"] += 1
            logger.error(f"处理WebSocket消息失败: {e}")
            return {
                "success": False,
                "error": f"处理消息失败: {str(e)}"
            }
    
    async def _handle_remote_control(self, control_data: Dict[str, Any]):
        """
        处理遥控器控制信号

        Args:
            control_data: 遥控器控制数据
        """
        try:
            self.message_stats["remote_signals"] += 1

            # 检查是否是直接的视频播放指令
            if control_data.get("action") == "play_video":
                # 直接广播视频播放指令给应用层
                if self.app_broadcast_callback:
                    app_message = {
                        "action": "play_video",
                        "video_file": control_data.get("video_file", ""),
                        "direction": control_data.get("direction", ""),
                        "source": control_data.get("source", "remote"),
                        "type": control_data.get("type", "unknown")
                    }

                    await self.app_broadcast_callback(app_message)
                    logger.info(f"已广播视频播放指令: {control_data.get('video_file', '')}")

                result = {"success": True, "message": "视频播放指令已发送"}
            else:
                # 其他指令通过表情控制器处理
                result = await self.expression_controller.handle_expression_message(control_data)

            # 更新统计
            if result.get("success", False):
                action = control_data.get("action", "")
                if action in ["play_expression", "stop_expression"]:
                    self.message_stats["expression_plays"] += 1
                elif action == "move_head_and_ear":
                    self.message_stats["direct_controls"] += 1
            else:
                self.message_stats["errors"] += 1

            logger.debug(f"遥控器控制处理完成: {result.get('success', False)}")

        except Exception as e:
            self.message_stats["errors"] += 1
            logger.error(f"处理遥控器控制失败: {e}")
    
    def enable_controller(self, controller_name: str) -> bool:
        """
        启用控制器
        
        Args:
            controller_name: 控制器名称
            
        Returns:
            是否成功
        """
        if controller_name in self.enabled_controllers:
            self.enabled_controllers[controller_name] = True
            logger.info(f"控制器已启用: {controller_name}")
            return True
        else:
            logger.error(f"未知控制器: {controller_name}")
            return False
    
    def disable_controller(self, controller_name: str) -> bool:
        """
        禁用控制器
        
        Args:
            controller_name: 控制器名称
            
        Returns:
            是否成功
        """
        if controller_name in self.enabled_controllers:
            self.enabled_controllers[controller_name] = False
            logger.info(f"控制器已禁用: {controller_name}")
            return True
        else:
            logger.error(f"未知控制器: {controller_name}")
            return False
    
    def _get_enabled_controllers(self) -> List[str]:
        """获取启用的控制器列表"""
        return [k for k, v in self.enabled_controllers.items() if v]
    
    def get_status(self) -> Dict[str, Any]:
        """获取统一控制器状态"""
        return {
            "is_running": self.is_running,
            "enabled_controllers": self.enabled_controllers,
            "active_remote_controllers": self.remote_processor.get_active_controllers(),
            "expression_controller_status": self.expression_controller.get_status(),
            "remote_processor_status": self.remote_processor.get_signal_status(),
            "message_stats": self.message_stats.copy(),
            "available_expressions": self.expression_controller.expression_mapper.list_expressions()
        }
    
    def get_controller_info(self) -> Dict[str, Any]:
        """获取控制器信息"""
        return {
            "supported_controllers": list(self.enabled_controllers.keys()),
            "websocket_actions": [
                "move_head_and_ear",
                "play_expression", 
                "stop_expression",
                "list_expressions",
                "get_expression_info",
                "create_expression"
            ],
            "available_expressions": self.expression_controller.expression_mapper.list_expressions(),
            "remote_mappings": self.remote_processor.expression_mapper.remote_mappings
        }
    
    async def play_expression(self, expression_name: str) -> Dict[str, Any]:
        """
        直接播放表情（API接口）
        
        Args:
            expression_name: 表情名称
            
        Returns:
            执行结果
        """
        return await self.expression_controller.play_expression_by_name(expression_name)
    
    async def stop_current_expression(self) -> Dict[str, Any]:
        """停止当前表情"""
        message = {"action": "stop_expression"}
        return await self.expression_controller.handle_expression_message(message)
    
    def reset_stats(self):
        """重置消息统计"""
        self.message_stats = {
            "websocket_messages": 0,
            "remote_signals": 0,
            "expression_plays": 0,
            "direct_controls": 0,
            "errors": 0
        }
        logger.info("消息统计已重置")
    
    async def emergency_stop(self):
        """紧急停止所有动作"""
        try:
            # 停止当前表情
            await self.stop_current_expression()
            
            # 回到中性位置
            neutral_message = {
                "action": "move_head_and_ear",
                "yaw_cmd": "center",
                "pitch_cmd": "center", 
                "left_ear_cmd": "center",
                "right_ear_cmd": "center"
            }
            await self.handle_websocket_message(neutral_message)
            
            logger.info("紧急停止执行完成")
            
        except Exception as e:
            logger.error(f"紧急停止失败: {e}")
    
    async def _handle_robot_control(self, control_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理机器人控制请求（供队列系统调用）"""
        try:
            return await self.expression_controller.handle_expression_message(control_data)
        except Exception as e:
            logger.error(f"机器人控制失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # 动作队列相关方法

    async def submit_action_queue(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """提交动作队列"""
        try:
            self.message_stats["action_queues"] += 1
            result = await self.application_interface.submit_action_queue(request)

            if not result.get("success", False):
                self.message_stats["errors"] += 1

            return result

        except Exception as e:
            self.message_stats["errors"] += 1
            logger.error(f"提交动作队列失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def control_action_queue(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """控制动作队列"""
        try:
            return await self.application_interface.control_queue(request)
        except Exception as e:
            logger.error(f"控制动作队列失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_queue_status(self, queue_id: str) -> Dict[str, Any]:
        """获取队列状态"""
        return self.application_interface.get_queue_status(queue_id)

    def list_action_queues(self, status_filter: Optional[str] = None) -> Dict[str, Any]:
        """列出动作队列"""
        return self.application_interface.list_queues(status_filter)

    def get_queue_statistics(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        return self.application_interface.get_statistics()

    def clear_completed_queues(self) -> Dict[str, Any]:
        """清理已完成的队列"""
        return self.application_interface.clear_completed_queues()

    # 便捷方法

    async def submit_expression_sequence(self, expressions: List[str],
                                       name: str = "表情序列",
                                       delays: Optional[List[float]] = None) -> Dict[str, Any]:
        """提交表情序列"""
        return await self.application_interface.submit_expression_sequence(expressions, name, delays)

    async def submit_movement_sequence(self, movements: List[Dict[str, str]],
                                     name: str = "动作序列",
                                     duration: float = 1.0) -> Dict[str, Any]:
        """提交动作序列"""
        return await self.application_interface.submit_movement_sequence(movements, name, duration)

    def __del__(self):
        """析构函数"""
        if self.is_running:
            self.stop()
