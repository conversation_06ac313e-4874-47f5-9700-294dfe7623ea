#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式头部控制测试脚本 - 保持WebSocket连接版本
用于测试Live2D头部动画响应
支持输入: up, down, left, right, center, quit
"""

import asyncio
import websockets
import json

class InteractiveController:
    def __init__(self):
        self.uri = "ws://localhost:8000/ws"
        self.websocket = None
        self.connected = False

    async def connect(self):
        """建立WebSocket连接"""
        try:
            print(f"正在连接到 {self.uri}...")
            self.websocket = await websockets.connect(self.uri)
            self.connected = True
            print("✅ WebSocket连接已建立!")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            print("请确保服务器正在运行 (python run.py 或 python main.py)")
            return False

    async def disconnect(self):
        """断开WebSocket连接"""
        if self.websocket and self.connected:
            await self.websocket.close()
            self.connected = False
            print("🔌 WebSocket连接已断开")

    async def send_command(self, command):
        """发送指定命令"""
        if not self.connected:
            print("❌ WebSocket未连接")
            return False

        # 根据命令构建消息
        if command in ["up", "down"]:
            message = {
                "action": "move_head_and_ear",
                "pitch_cmd": command
            }
        elif command in ["left", "right"]:
            message = {
                "action": "move_head_and_ear",
                "yaw_cmd": command
            }
        elif command == "center":
            message = {
                "action": "move_head_and_ear",
                "yaw_cmd": "center",
                "pitch_cmd": "center"
            }
        else:
            print(f"❌ 不支持的命令: {command}")
            return False

        try:
            print(f"🎮 发送{command.upper()}命令: {message}")
            await self.websocket.send(json.dumps(message))

            # 等待响应
            response = await self.websocket.recv()
            print(f"📨 收到响应: {response}")

            # 解析响应
            try:
                response_data = json.loads(response)
                status = response_data.get("status")

                if status == "success":
                    # 检查result字段是否包含错误信息
                    result = response_data.get("result", {})
                    if isinstance(result, dict):
                        if result.get("success") == False:
                            error_msg = result.get("error", "Unknown error")
                            print(f"⚠️  命令处理失败: {error_msg}")
                            print("💡 但WebSocket通信成功，可能是机器人控制系统的问题")
                        else:
                            print(f"✅ {command.upper()}命令执行成功! Live2D应该显示{command}动画")
                    else:
                        print(f"✅ {command.upper()}命令发送成功! Live2D应该显示{command}动画")
                elif status == "error":
                    error_msg = response_data.get("error", "Unknown error")
                    print(f"❌ 服务器错误: {error_msg}")
                else:
                    print(f"⚠️  未知响应状态: {status}")

            except json.JSONDecodeError:
                print(f"❌ 响应格式错误: {response}")

            return True

        except websockets.exceptions.ConnectionClosed:
            print("❌ WebSocket连接已断开")
            self.connected = False
            return False
        except Exception as e:
            print(f"❌ 发送命令失败: {e}")
            return False

    async def interactive_test(self):
        """交互式测试"""
        print("🎮 交互式头部控制测试 (保持连接版本)")
        print("=" * 60)
        print("支持的命令:")
        print("  up     - 头部向上")
        print("  down   - 头部向下")
        print("  left   - 头部向左")
        print("  right  - 头部向右")
        print("  center - 头部回中")
        print("  reconnect - 重新连接")
        print("  quit   - 退出程序")
        print("=" * 60)

        # 建立初始连接
        if not await self.connect():
            return

        while True:
            try:
                command = input("\n请输入命令 (up/down/left/right/center/reconnect/quit): ").strip().lower()

                if command == "quit":
                    print("👋 退出程序")
                    break
                elif command == "reconnect":
                    await self.disconnect()
                    if await self.connect():
                        print("🔄 重新连接成功")
                    else:
                        print("❌ 重新连接失败")
                elif command in ["up", "down", "left", "right", "center"]:
                    success = await self.send_command(command)
                    if not success and not self.connected:
                        print("🔄 尝试重新连接...")
                        if await self.connect():
                            print("✅ 重新连接成功，请重试命令")
                        else:
                            print("❌ 重新连接失败")
                elif command == "":
                    continue
                else:
                    print(f"❌ 无效命令: {command}")
                    print("请输入: up, down, left, right, center, reconnect, 或 quit")

            except KeyboardInterrupt:
                print("\n👋 程序被中断，退出")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

        # 清理连接
        await self.disconnect()

async def main():
    """主函数"""
    controller = InteractiveController()
    await controller.interactive_test()

if __name__ == "__main__":
    asyncio.run(main())
